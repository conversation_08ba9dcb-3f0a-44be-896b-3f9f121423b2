using System;
using System.Data;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class RepairShop_ServiceRecords : System.Web.UI.Page
{
    private int userID;
    private int shopID;
    private string searchText = string.Empty;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取当前用户信息
        if (Session["UserID"] == null || Session["UserType"] == null)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        string userType = Session["UserType"].ToString();

        if (userType != "RepairShop")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取维修店ID
        shopID = ShopManager.GetShopIDByUserID(userID);
        if (shopID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            // 检查是否有特定的记录ID
            if (Request.QueryString["id"] != null)
            {
                int recordID;
                if (int.TryParse(Request.QueryString["id"], out recordID))
                {
                    // 显示特定记录详情
                    ShowRecordDetails(recordID);
                    return;
                }
            }

            // 从会话中获取搜索文本（如果有）
            if (Session["ServiceRecordSearchText"] != null)
            {
                searchText = Session["ServiceRecordSearchText"].ToString();
                txtSearch.Text = searchText;
            }

            // 加载维修记录列表
            LoadServiceRecords();
        }
    }

    /// <summary>
    /// 加载维修记录
    /// </summary>
    private void LoadServiceRecords()
    {
        DataTable records = GetFilteredRecords();
        gvServiceRecords.DataSource = records;
        gvServiceRecords.DataBind();
    }

    /// <summary>
    /// 获取过滤后的记录
    /// </summary>
    private DataTable GetFilteredRecords()
    {
        string query = @"SELECT sr.RecordID, sr.CompletedDate, sr.DiagnosisDetails, sr.PartsReplaced,
                        sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName,
                        a.AppointmentDate, a.AppointmentID,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.FullName AS OwnerName, s.ServiceName
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE a.ShopID = @ShopID";

        if (!string.IsNullOrEmpty(searchText))
        {
            query += @" AND (c.LicensePlate LIKE '%' + @SearchText + '%' OR 
                           co.FullName LIKE '%' + @SearchText + '%' OR
                           c.Make LIKE '%' + @SearchText + '%' OR
                           c.Model LIKE '%' + @SearchText + '%')";
        }

        query += " ORDER BY sr.CompletedDate DESC";

        System.Data.SqlClient.SqlParameter[] parameters;
        if (!string.IsNullOrEmpty(searchText))
        {
            parameters = new System.Data.SqlClient.SqlParameter[]
            {
                new System.Data.SqlClient.SqlParameter("@ShopID", shopID),
                new System.Data.SqlClient.SqlParameter("@SearchText", searchText)
            };
        }
        else
        {
            parameters = new System.Data.SqlClient.SqlParameter[]
            {
                new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
            };
        }

        return DatabaseHelper.ExecuteQuery(query, parameters);
    }

    /// <summary>
    /// 显示记录详情
    /// </summary>
    private void ShowRecordDetails(int recordID)
    {
        string query = @"SELECT sr.RecordID, sr.CompletedDate, sr.DiagnosisDetails, sr.PartsReplaced,
                        sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName,
                        a.AppointmentDate, a.AppointmentID,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        c.LicensePlate,
                        co.FullName AS OwnerName, co.OwnerID, s.ServiceName
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE sr.RecordID = @RecordID AND a.ShopID = @ShopID";

        System.Data.SqlClient.SqlParameter[] parameters = 
        {
            new System.Data.SqlClient.SqlParameter("@RecordID", recordID),
            new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
        };

        DataTable recordDetails = DatabaseHelper.ExecuteQuery(query, parameters);
        
        if (recordDetails != null && recordDetails.Rows.Count > 0)
        {
            DataRow record = recordDetails.Rows[0];
            
            // 填充详情页面
            lblRecordID.Text = record["RecordID"].ToString();
            lblAppointmentDate.Text = Convert.ToDateTime(record["AppointmentDate"]).ToString("yyyy-MM-dd HH:mm");
            lblCompletedDate.Text = Convert.ToDateTime(record["CompletedDate"]).ToString("yyyy-MM-dd HH:mm");
            lblOwnerName.Text = record["OwnerName"].ToString();
            lblCarInfo.Text = record["CarInfo"].ToString();
            lblServiceName.Text = record["ServiceName"].ToString();
            lblLaborCost.Text = string.Format("¥{0:N2}", Convert.ToDecimal(record["LaborCost"]));
            lblPartsCost.Text = string.Format("¥{0:N2}", Convert.ToDecimal(record["PartsCost"]));
            lblTotalCost.Text = string.Format("¥{0:N2}", Convert.ToDecimal(record["TotalCost"]));
            lblTechnicianName.Text = record["TechnicianName"].ToString();
            
            lblDiagnosisDetails.Text = record["DiagnosisDetails"] != DBNull.Value ? 
                record["DiagnosisDetails"].ToString() : "无详细诊断信息";
            
            lblPartsReplaced.Text = !string.IsNullOrEmpty(record["PartsReplaced"].ToString()) ? 
                record["PartsReplaced"].ToString() : "无更换零件";
            
            // 获取评价信息
            int ownerID = Convert.ToInt32(record["OwnerID"]);
            GetReviewInfo(recordID, ownerID);
            
            // 显示详情面板，隐藏列表
            pnlRecordDetails.Visible = true;
            gvServiceRecords.Visible = false;
        }
        else
        {
            // 无法找到记录或不属于该维修店
            Response.Redirect("~/RepairShop/ServiceRecords.aspx");
        }
    }

    /// <summary>
    /// 获取评价信息
    /// </summary>
    private void GetReviewInfo(int recordID, int ownerID)
    {
        string query = @"SELECT ReviewID, Rating, Comments, ReviewDate 
                        FROM Reviews 
                        WHERE ServiceRecordID = @RecordID AND OwnerID = @OwnerID AND ShopID = @ShopID";

        System.Data.SqlClient.SqlParameter[] parameters = 
        {
            new System.Data.SqlClient.SqlParameter("@RecordID", recordID),
            new System.Data.SqlClient.SqlParameter("@OwnerID", ownerID),
            new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
        };

        DataTable reviewTable = DatabaseHelper.ExecuteQuery(query, parameters);
        
        if (reviewTable != null && reviewTable.Rows.Count > 0)
        {
            DataRow review = reviewTable.Rows[0];
            
            int rating = Convert.ToInt32(review["Rating"]);
            string comments = review["Comments"] != DBNull.Value ? review["Comments"].ToString() : "无评论";
            DateTime reviewDate = Convert.ToDateTime(review["ReviewDate"]);
            int reviewID = Convert.ToInt32(review["ReviewID"]);
            
            // 显示评价内容
            StringBuilder stars = new StringBuilder();
            for (int i = 1; i <= 5; i++)
            {
                if (i <= rating)
                {
                    stars.Append("<i class=\"fas fa-star\"></i> ");
                }
                else
                {
                    stars.Append("<i class=\"far fa-star\"></i> ");
                }
            }
            
            litRatingStars.Text = stars.ToString();
            lblRating.Text = rating.ToString();
            lblReviewComments.Text = comments;
            lblReviewDate.Text = reviewDate.ToString("yyyy-MM-dd");
            
            // 加载评价照片
            LoadReviewPhotos(reviewID);
            
            pnlReviewDetails.Visible = true;
        }
        else
        {
            // 无评价
            pnlReviewDetails.Visible = false;
        }
    }

    /// <summary>
    /// 加载评价照片
    /// </summary>
    private void LoadReviewPhotos(int reviewID)
    {
        string query = "SELECT PhotoID, FileName, FilePath FROM ReviewPhotos WHERE ReviewID = @ReviewID";
        System.Data.SqlClient.SqlParameter parameter = new System.Data.SqlClient.SqlParameter("@ReviewID", reviewID);
        
        DataTable photosTable = DatabaseHelper.ExecuteQuery(query, parameter);
        
        if (photosTable != null && photosTable.Rows.Count > 0)
        {
            rptReviewPhotos.DataSource = photosTable;
            rptReviewPhotos.DataBind();
            pnlReviewPhotos.Visible = true;
        }
        else
        {
            pnlReviewPhotos.Visible = false;
        }
    }

    /// <summary>
    /// 搜索按钮点击事件
    /// </summary>
    protected void btnSearch_Click(object sender, EventArgs e)
    {
        searchText = txtSearch.Text.Trim();
        
        // 保存搜索文本到会话
        Session["ServiceRecordSearchText"] = searchText;
        
        // 重新加载记录列表
        LoadServiceRecords();
    }

    /// <summary>
    /// GridView行命令事件
    /// </summary>
    protected void gvServiceRecords_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "ViewRecord")
        {
            int recordID = Convert.ToInt32(e.CommandArgument);
            ShowRecordDetails(recordID);
        }
    }

    /// <summary>
    /// 返回列表按钮点击事件
    /// </summary>
    protected void btnBack_Click(object sender, EventArgs e)
    {
        // 隐藏详情面板，显示列表
        pnlRecordDetails.Visible = false;
        gvServiceRecords.Visible = true;
        
        // 重新加载记录列表
        LoadServiceRecords();
    }

    /// <summary>
    /// GridView分页事件
    /// </summary>
    protected void gvServiceRecords_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvServiceRecords.PageIndex = e.NewPageIndex;
        LoadServiceRecords();
    }
} 