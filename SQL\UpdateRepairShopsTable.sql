-- 检查 BusinessLicense 列是否存在，如果不存在则添加
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairShops]') AND name = 'BusinessLicense')
BEGIN
    ALTER TABLE [dbo].[RepairShops]
    ADD BusinessLicense NVARCHAR(50) NULL
END
GO

-- 检查 ShopType 列是否存在，如果不存在则添加
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairShops]') AND name = 'ShopType')
BEGIN
    ALTER TABLE [dbo].[RepairShops]
    ADD ShopType NVARCHAR(50) NULL
END
GO

-- 检查 CarOwners 表的 Gender 列是否存在，如果不存在则添加
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CarOwners]') AND name = 'Gender')
BEGIN
    ALTER TABLE [dbo].[CarOwners]
    ADD Gender NVARCHAR(10) NULL
END
GO

-- 更新已存在的数据
UPDATE [dbo].[RepairShops]
SET BusinessLicense = '未填写', ShopType = 'General'
WHERE BusinessLicense IS NULL
GO 