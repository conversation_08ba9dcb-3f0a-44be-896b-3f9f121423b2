using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Text;

public partial class Admin_ReviewManagement : System.Web.UI.Page
{
    private int shopID = 0;
    private int rating = 0;
    private string searchText = "";

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录且是管理员
        if (!User.Identity.IsAuthenticated || Session["UserType"] == null || Session["UserType"].ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            // 加载维修店列表
            LoadShops();
            
            // 加载评论列表
            LoadReviews();
        }
    }

    /// <summary>
    /// 加载维修店列表
    /// </summary>
    private void LoadShops()
    {
        string query = "SELECT ShopID, ShopName FROM RepairShops ORDER BY ShopName";
        DataTable shops = DatabaseHelper.ExecuteQuery(query);
        
        ddlShopFilter.Items.Clear();
        ddlShopFilter.Items.Add(new ListItem("所有维修店", "0"));
        
        if (shops != null && shops.Rows.Count > 0)
        {
            foreach (DataRow row in shops.Rows)
            {
                ddlShopFilter.Items.Add(new ListItem(row["ShopName"].ToString(), row["ShopID"].ToString()));
            }
        }
    }

    /// <summary>
    /// 加载评论列表
    /// </summary>
    private void LoadReviews()
    {
        // 获取筛选条件
        if (ddlShopFilter.SelectedValue != "0")
        {
            shopID = Convert.ToInt32(ddlShopFilter.SelectedValue);
        }
        
        if (ddlRatingFilter.SelectedValue != "0")
        {
            rating = Convert.ToInt32(ddlRatingFilter.SelectedValue);
        }
        
        searchText = txtSearch.Text.Trim();
        
        // 构建查询
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.Append(@"SELECT r.ReviewID, r.Rating, r.Comments, r.ReviewDate, 
                             u.Username, rs.ShopName, sr.RecordID
                             FROM Reviews r
                             INNER JOIN CarOwners co ON r.OwnerID = co.OwnerID
                             INNER JOIN Users u ON co.UserID = u.UserID
                             INNER JOIN RepairShops rs ON r.ShopID = rs.ShopID
                             INNER JOIN ServiceRecords sr ON r.ServiceRecordID = sr.RecordID
                             WHERE 1=1");
        
        // 添加筛选条件
        if (shopID > 0)
        {
            queryBuilder.Append(" AND r.ShopID = @ShopID");
        }
        
        if (rating > 0)
        {
            queryBuilder.Append(" AND r.Rating = @Rating");
        }
        
        if (!string.IsNullOrEmpty(searchText))
        {
            queryBuilder.Append(" AND r.Comments LIKE @SearchText");
        }
        
        queryBuilder.Append(" ORDER BY r.ReviewDate DESC");
        
        // 创建参数
        System.Collections.Generic.List<System.Data.SqlClient.SqlParameter> parameters = new System.Collections.Generic.List<System.Data.SqlClient.SqlParameter>();
        
        if (shopID > 0)
        {
            parameters.Add(new System.Data.SqlClient.SqlParameter("@ShopID", shopID));
        }
        
        if (rating > 0)
        {
            parameters.Add(new System.Data.SqlClient.SqlParameter("@Rating", rating));
        }
        
        if (!string.IsNullOrEmpty(searchText))
        {
            parameters.Add(new System.Data.SqlClient.SqlParameter("@SearchText", "%" + searchText + "%"));
        }
        
        // 执行查询
        DataTable reviewsTable = DatabaseHelper.ExecuteQuery(queryBuilder.ToString(), parameters.ToArray());
        
        // 绑定数据
        gvReviews.DataSource = reviewsTable;
        gvReviews.DataBind();
    }

    /// <summary>
    /// 维修店筛选下拉框选择改变事件
    /// </summary>
    protected void ddlShopFilter_SelectedIndexChanged(object sender, EventArgs e)
    {
        LoadReviews();
    }

    /// <summary>
    /// 评分筛选下拉框选择改变事件
    /// </summary>
    protected void ddlRatingFilter_SelectedIndexChanged(object sender, EventArgs e)
    {
        LoadReviews();
    }

    /// <summary>
    /// 搜索按钮点击事件
    /// </summary>
    protected void btnSearch_Click(object sender, EventArgs e)
    {
        LoadReviews();
    }

    /// <summary>
    /// GridView行数据绑定事件
    /// </summary>
    protected void gvReviews_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            // 获取评分
            DataRowView rowView = (DataRowView)e.Row.DataItem;
            int rating = Convert.ToInt32(rowView["Rating"]);
            
            // 生成星星HTML
            Literal litRatingStars = (Literal)e.Row.FindControl("litRatingStars");
            if (litRatingStars != null)
            {
                StringBuilder stars = new StringBuilder();
                for (int i = 1; i <= 5; i++)
                {
                    if (i <= rating)
                    {
                        stars.Append("<i class=\"fas fa-star\"></i> ");
                    }
                    else
                    {
                        stars.Append("<i class=\"far fa-star\"></i> ");
                    }
                }
                
                litRatingStars.Text = stars.ToString();
            }
        }
    }

    /// <summary>
    /// GridView行命令事件
    /// </summary>
    protected void gvReviews_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "DeleteReview")
        {
            int reviewID = Convert.ToInt32(e.CommandArgument);
            
            // 删除评论
            bool success = ShopManager.DeleteReview(reviewID, null, true);
            
            if (success)
            {
                // 删除成功，显示成功消息
                pnlMessage.Visible = true;
                litMessage.Text = "评论已成功删除。";
                pnlMessage.CssClass = "alert alert-success";
                
                // 重新加载评论列表
                LoadReviews();
            }
            else
            {
                // 删除失败，显示错误消息
                pnlMessage.Visible = true;
                litMessage.Text = "删除评论失败，请稍后再试。";
                pnlMessage.CssClass = "alert alert-danger";
            }
        }
    }

    /// <summary>
    /// GridView分页事件
    /// </summary>
    protected void gvReviews_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvReviews.PageIndex = e.NewPageIndex;
        LoadReviews();
    }

    /// <summary>
    /// 返回控制台按钮点击事件
    /// </summary>
    protected void btnBackToDashboard_Click(object sender, EventArgs e)
    {
        Response.Redirect("~/Admin/Dashboard.aspx");
    }
} 