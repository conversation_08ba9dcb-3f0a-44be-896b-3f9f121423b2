using System;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Diagnostics;
using System.Configuration;

/// <summary>
/// 预约管理类
/// </summary>
public class AppointmentManager
{
    /// <summary>
    /// 获取车主的所有预约
    /// </summary>
    /// <param name="ownerID">车主ID</param>
    /// <returns>预约DataTable</returns>
    public static DataTable GetAppointmentsByOwnerID(int ownerID)
    {
        string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description,
                        c.CarID, c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        rs.ShopID, rs.ShopName, s.ServiceID, s.ServiceName
                        FROM Appointments a
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE c.OwnerID = @OwnerID
                        ORDER BY a.AppointmentDate DESC";
        SqlParameter parameter = new SqlParameter("@OwnerID", ownerID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取车主未完成的预约
    /// </summary>
    /// <param name="ownerID">车主ID</param>
    /// <returns>预约DataTable</returns>
    public static DataTable GetActiveAppointmentsByOwnerID(int ownerID)
    {
        string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description,
                        c.CarID, c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        rs.ShopID, rs.ShopName, s.ServiceID, s.ServiceName
                        FROM Appointments a
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE c.OwnerID = @OwnerID AND a.Status IN ('Pending', 'Confirmed')
                        ORDER BY a.AppointmentDate";
        SqlParameter parameter = new SqlParameter("@OwnerID", ownerID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 根据预约ID获取预约信息
    /// </summary>
    /// <param name="appointmentID">预约ID</param>
    /// <returns>预约DataTable</returns>
    public static DataTable GetAppointmentByID(int appointmentID)
    {
        try
        {
            // 记录日志
            System.Diagnostics.Debug.WriteLine("获取预约信息，ID: " + appointmentID);
            
            if (appointmentID <= 0)
            {
                System.Diagnostics.Debug.WriteLine("预约ID无效: " + appointmentID);
                return null;
            }
            
            string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description,
                            c.CarID, c.Make, c.Model, c.LicensePlate, c.OwnerID,
                            rs.ShopID, rs.ShopName, rs.Address,
                            s.ServiceID, s.ServiceName, s.Description AS ServiceDescription, s.EstimatedTime, s.BasePrice
                            FROM Appointments a
                            INNER JOIN Cars c ON a.CarID = c.CarID
                            INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                            INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                            WHERE a.AppointmentID = @AppointmentID";
            SqlParameter parameter = new SqlParameter("@AppointmentID", appointmentID);
            
            DataTable result = DatabaseHelper.ExecuteQuery(query, parameter);
            System.Diagnostics.Debug.WriteLine("查询结果行数: " + (result != null ? result.Rows.Count : 0));
            return result;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine("获取预约信息时出错: " + ex.Message);
            return null;
        }
    }

    /// <summary>
    /// 添加新预约
    /// </summary>
    /// <param name="carID">车辆ID</param>
    /// <param name="shopID">维修店ID</param>
    /// <param name="serviceID">服务ID</param>
    /// <param name="appointmentDate">预约时间</param>
    /// <param name="description">描述</param>
    /// <returns>成功返回预约ID，失败返回-1</returns>
    public static int AddAppointment(int carID, int shopID, int serviceID, DateTime appointmentDate, string description)
    {
        try
        {
            // 记录参数信息
            System.Diagnostics.Debug.WriteLine($"===== 开始创建预约 =====");
            System.Diagnostics.Debug.WriteLine($"参数：carID={carID}, shopID={shopID}, serviceID={serviceID}");
            System.Diagnostics.Debug.WriteLine($"预约时间：{appointmentDate.ToString("yyyy-MM-dd HH:mm:ss")}");
            System.Diagnostics.Debug.WriteLine($"描述：{(string.IsNullOrEmpty(description) ? "无" : description)}");
            
            // 验证车辆存在
            string checkCarQuery = "SELECT COUNT(1) FROM Cars WHERE CarID = @CarID";
            SqlParameter checkCarParam = new SqlParameter("@CarID", carID);
            int carExists = Convert.ToInt32(DatabaseHelper.ExecuteScalar(checkCarQuery, checkCarParam));
            System.Diagnostics.Debug.WriteLine($"车辆验证结果：存在={carExists > 0}");
            
            if (carExists == 0)
            {
                System.Diagnostics.Debug.WriteLine("错误：指定的车辆不存在，carID=" + carID);
                return -1;
            }
            
            // 验证维修店存在
            string checkShopQuery = "SELECT COUNT(1) FROM RepairShops WHERE ShopID = @ShopID";
            SqlParameter checkShopParam = new SqlParameter("@ShopID", shopID);
            int shopExists = Convert.ToInt32(DatabaseHelper.ExecuteScalar(checkShopQuery, checkShopParam));
            System.Diagnostics.Debug.WriteLine($"维修店验证结果：存在={shopExists > 0}");
            
            if (shopExists == 0)
            {
                System.Diagnostics.Debug.WriteLine("错误：指定的维修店不存在，shopID=" + shopID);
                return -1;
            }
            
            // 验证服务存在
            string checkServiceQuery = "SELECT COUNT(1) FROM RepairServices WHERE ServiceID = @ServiceID AND ShopID = @ShopID";
            SqlParameter[] checkServiceParams =
            {
                new SqlParameter("@ServiceID", serviceID),
                new SqlParameter("@ShopID", shopID)
            };
            int serviceExists = Convert.ToInt32(DatabaseHelper.ExecuteScalar(checkServiceQuery, checkServiceParams));
            System.Diagnostics.Debug.WriteLine($"服务验证结果：存在于指定维修店={serviceExists > 0}");
            
            if (serviceExists == 0)
            {
                System.Diagnostics.Debug.WriteLine($"错误：指定的服务不存在于该维修店，serviceID={serviceID}, shopID={shopID}");
                return -1;
            }
            
            // 验证通过，创建预约
            string query = @"INSERT INTO Appointments (CarID, ShopID, ServiceID, AppointmentDate, Status, Description, CreatedDate) 
                            VALUES (@CarID, @ShopID, @ServiceID, @AppointmentDate, 'Pending', @Description, GETDATE());
                            SELECT SCOPE_IDENTITY()";

            SqlParameter[] parameters =
            {
                new SqlParameter("@CarID", carID),
                new SqlParameter("@ShopID", shopID),
                new SqlParameter("@ServiceID", serviceID),
                new SqlParameter("@AppointmentDate", appointmentDate),
                new SqlParameter("@Description", description ?? (object)DBNull.Value)
            };

            System.Diagnostics.Debug.WriteLine("执行插入预约SQL");
            System.Diagnostics.Debug.WriteLine($"SQL语句: {query}");
            System.Diagnostics.Debug.WriteLine($"参数值: CarID={carID}, ShopID={shopID}, ServiceID={serviceID}, AppointmentDate={appointmentDate.ToString("yyyy-MM-dd HH:mm:ss")}");
            
            // 使用事务包装操作
            using (SqlConnection connection = new SqlConnection(ConfigurationManager.ConnectionStrings["CarRepairServiceDB"].ConnectionString))
            {
                connection.Open();
                using (SqlTransaction transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // 创建命令对象
                        using (SqlCommand command = new SqlCommand(query, connection, transaction))
                        {
                            // 添加参数
                            command.Parameters.AddRange(parameters);
                            
                            // 执行命令
                            object result = command.ExecuteScalar();
                            
                            System.Diagnostics.Debug.WriteLine($"SQL执行结果：{(result != null ? result.ToString() : "null")}");
                            
                            if (result != null && result != DBNull.Value)
                            {
                                int appointmentID = Convert.ToInt32(result);
                                
                                // 提交事务
                                transaction.Commit();
                                
                                System.Diagnostics.Debug.WriteLine($"预约创建成功：ID={appointmentID}");
                                System.Diagnostics.Debug.WriteLine($"===== 预约创建完成 =====");
                                return appointmentID;
                            }
                            else
                            {
                                // 回滚事务
                                transaction.Rollback();
                                System.Diagnostics.Debug.WriteLine("预约创建失败：SQL没有返回有效的ID");
                                System.Diagnostics.Debug.WriteLine($"===== 预约创建失败 =====");
                                return -1;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // 回滚事务
                        transaction.Rollback();
                        System.Diagnostics.Debug.WriteLine($"事务执行异常：{ex.Message}");
                        throw; // 重新抛出异常，让上层捕获
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"===== 预约创建异常 =====");
            System.Diagnostics.Debug.WriteLine($"创建预约时发生异常：{ex.Message}");
            System.Diagnostics.Debug.WriteLine($"异常堆栈：{ex.StackTrace}");
            if (ex.InnerException != null)
            {
                System.Diagnostics.Debug.WriteLine($"内部异常：{ex.InnerException.Message}");
                System.Diagnostics.Debug.WriteLine($"内部异常堆栈：{ex.InnerException.StackTrace}");
            }
            throw; // 重新抛出异常，让调用方处理
        }
    }

    /// <summary>
    /// 更新预约状态
    /// </summary>
    /// <param name="appointmentID">预约ID</param>
    /// <param name="status">状态</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool UpdateAppointmentStatus(int appointmentID, string status)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"更新预约状态 - ID: {appointmentID}, 新状态: {status}");
            
            string query = "UPDATE Appointments SET Status = @Status WHERE AppointmentID = @AppointmentID";
            SqlParameter[] parameters =
            {
                new SqlParameter("@AppointmentID", appointmentID),
                new SqlParameter("@Status", status)
            };
            int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
            
            System.Diagnostics.Debug.WriteLine($"更新预约状态结果 - 影响行数: {result}");
            return result > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新预约状态出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 取消预约
    /// </summary>
    /// <param name="appointmentID">预约ID</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool CancelAppointment(int appointmentID)
    {
        try
        {
            // 记录日志
            System.Diagnostics.Debug.WriteLine("开始取消预约，ID: " + appointmentID);
            
            // 首先检查预约是否存在且状态可以被取消（只有待确认或已确认状态才能取消）
            string checkQuery = "SELECT Status FROM Appointments WHERE AppointmentID = @AppointmentID";
            SqlParameter checkParam = new SqlParameter("@AppointmentID", appointmentID);
            object result = DatabaseHelper.ExecuteScalar(checkQuery, checkParam);
            
            System.Diagnostics.Debug.WriteLine("检查预约状态结果: " + (result != null ? result.ToString() : "null"));
            
            if (result == null || result == DBNull.Value)
            {
                System.Diagnostics.Debug.WriteLine("预约不存在，ID: " + appointmentID);
                return false; // 预约不存在
            }
            
            string status = result.ToString();
            System.Diagnostics.Debug.WriteLine("预约状态: " + status);
            
            if (status != "Pending" && status != "Confirmed")
            {
                System.Diagnostics.Debug.WriteLine("预约状态不允许取消: " + status);
                return false; // 预约状态不允许取消
            }
            
            // 执行取消操作
            bool updateResult = UpdateAppointmentStatus(appointmentID, "Cancelled");
            System.Diagnostics.Debug.WriteLine("更新状态结果: " + updateResult);
            return updateResult;
        }
        catch (Exception ex)
        {
            // 记录错误信息
            System.Diagnostics.Debug.WriteLine("取消预约时出错: " + ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 获取所有维修店
    /// </summary>
    /// <returns>维修店DataTable</returns>
    public static DataTable GetAllRepairShops()
    {
        string query = "SELECT ShopID, ShopName, Address, BusinessHours, ContactPerson, Rating FROM RepairShops ORDER BY ShopName";
        return DatabaseHelper.ExecuteQuery(query);
    }

    /// <summary>
    /// 获取维修店提供的服务
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>服务DataTable</returns>
    public static DataTable GetServicesByShopID(int shopID)
    {
        string query = @"SELECT rs.ServiceID, rs.ServiceName, rs.Description, rs.EstimatedTime, rs.BasePrice,
                        sc.CategoryName
                        FROM RepairServices rs
                        INNER JOIN ServiceCategories sc ON rs.CategoryID = sc.CategoryID
                        WHERE rs.ShopID = @ShopID
                        ORDER BY sc.CategoryName, rs.ServiceName";
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 根据车辆ID获取车辆维修记录
    /// </summary>
    /// <param name="carID">车辆ID</param>
    /// <returns>维修记录DataTable</returns>
    public static DataTable GetServiceRecordsByCarID(int carID)
    {
        string query = @"SELECT sr.RecordID, sr.CompletedDate, sr.DiagnosisDetails, sr.PartsReplaced,
                        sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName,
                        a.AppointmentDate, rs.ShopName, s.ServiceName,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE a.CarID = @CarID
                        ORDER BY sr.CompletedDate DESC";
        SqlParameter parameter = new SqlParameter("@CarID", carID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取车主的所有维修记录
    /// </summary>
    /// <param name="ownerID">车主ID</param>
    /// <returns>维修记录DataTable</returns>
    public static DataTable GetServiceRecordsByOwnerID(int ownerID)
    {
        string query = @"SELECT sr.RecordID, sr.CompletedDate, sr.DiagnosisDetails, sr.PartsReplaced,
                        sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName,
                        a.AppointmentDate, rs.ShopName, s.ServiceName,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE c.OwnerID = @OwnerID
                        ORDER BY sr.CompletedDate DESC";
        SqlParameter parameter = new SqlParameter("@OwnerID", ownerID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取车主未付款的已完成维修记录
    /// </summary>
    /// <param name="ownerID">车主ID</param>
    /// <returns>维修记录DataTable</returns>
    public static DataTable GetUnpaidServiceRecordsByOwnerID(int ownerID)
    {
        string query = @"SELECT sr.RecordID, sr.CompletedDate, sr.DiagnosisDetails, sr.PartsReplaced,
                        sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName, sr.IsPaid, sr.IsVerified,
                        a.AppointmentID, a.AppointmentDate, a.Status,
                        rs.ShopID, rs.ShopName, 
                        s.ServiceID, s.ServiceName,
                        c.CarID, c.Make, c.Model, c.LicensePlate
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE c.OwnerID = @OwnerID AND a.Status = 'Completed' AND sr.IsPaid = 0
                        ORDER BY sr.CompletedDate DESC";
        SqlParameter parameter = new SqlParameter("@OwnerID", ownerID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取未验收的已完成维修记录
    /// </summary>
    /// <param name="ownerID">车主ID</param>
    /// <returns>维修记录DataTable</returns>
    public static DataTable GetUnverifiedServiceRecordsByOwnerID(int ownerID)
    {
        string query = @"SELECT sr.RecordID, sr.CompletedDate, sr.DiagnosisDetails, sr.PartsReplaced,
                        sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName, sr.IsPaid, sr.IsVerified,
                        a.AppointmentID, a.AppointmentDate, a.Status,
                        rs.ShopID, rs.ShopName, 
                        s.ServiceID, s.ServiceName,
                        c.CarID, c.Make, c.Model, c.LicensePlate
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE c.OwnerID = @OwnerID AND a.Status = 'Completed' AND sr.IsVerified = 0
                        ORDER BY sr.CompletedDate DESC";
        SqlParameter parameter = new SqlParameter("@OwnerID", ownerID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取服务记录详情
    /// </summary>
    /// <param name="recordID">记录ID</param>
    /// <returns>服务记录DataTable</returns>
    public static DataTable GetServiceRecordByID(int recordID)
    {
        // 检查PhoneNumber列是否存在
        bool phoneNumberExists = CheckPhoneNumberColumnExists();
        
        // 根据PhoneNumber列是否存在动态构建查询
        string shopColumns = phoneNumberExists ? 
            "rs.ShopID, rs.ShopName, rs.Address, rs.PhoneNumber," : 
            "rs.ShopID, rs.ShopName, rs.Address,";
            
        string query = $@"SELECT sr.RecordID, sr.CompletedDate, sr.DiagnosisDetails, sr.PartsReplaced,
                        sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName, sr.IsPaid, sr.IsVerified,
                        a.AppointmentID, a.AppointmentDate, a.Status, a.Description,
                        {shopColumns}
                        s.ServiceID, s.ServiceName, s.Description AS ServiceDescription,
                        c.CarID, c.Make, c.Model, c.LicensePlate, c.OwnerID
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE sr.RecordID = @RecordID";
        SqlParameter parameter = new SqlParameter("@RecordID", recordID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 车主确认验收服务
    /// </summary>
    /// <param name="recordID">服务记录ID</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool VerifyServiceRecord(int recordID)
    {
        try
        {
            Debug.WriteLine($"确认验收服务记录 - ID: {recordID}");
            
            string query = "UPDATE ServiceRecords SET IsVerified = 1 WHERE RecordID = @RecordID";
            SqlParameter parameter = new SqlParameter("@RecordID", recordID);
            int result = DatabaseHelper.ExecuteNonQuery(query, parameter);
            
            Debug.WriteLine($"确认验收结果 - 影响行数: {result}");
            return result > 0;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"确认验收服务出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 支付服务费用
    /// </summary>
    /// <param name="recordID">服务记录ID</param>
    /// <param name="appointmentID">预约ID</param>
    /// <param name="paymentMethod">支付方式</param>
    /// <param name="amount">支付金额</param>
    /// <param name="transactionNumber">交易号</param>
    /// <param name="notes">备注</param>
    /// <returns>成功返回PaymentID，失败返回-1</returns>
    public static int ProcessPayment(int recordID, int appointmentID, string paymentMethod, decimal amount, string transactionNumber = null, string notes = null)
    {
        try
        {
            Debug.WriteLine($"处理付款 - 记录ID: {recordID}, 预约ID: {appointmentID}, 金额: {amount}, 支付方式: {paymentMethod}");
            
            // 获取数据库连接字符串
            string connectionString = ConfigurationManager.ConnectionStrings["CarRepairServiceDB"].ConnectionString;
            
            // 开始事务
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                SqlTransaction transaction = connection.BeginTransaction();
                
                try
                {
                    // 1. 更新ServiceRecord的IsPaid状态
                    string updateQuery = "UPDATE ServiceRecords SET IsPaid = 1 WHERE RecordID = @RecordID";
                    SqlCommand updateCmd = new SqlCommand(updateQuery, connection, transaction);
                    updateCmd.Parameters.AddWithValue("@RecordID", recordID);
                    int updateResult = updateCmd.ExecuteNonQuery();
                    
                    if (updateResult <= 0)
                    {
                        Debug.WriteLine("更新服务记录状态失败");
                        transaction.Rollback();
                        return -1;
                    }
                    
                    // 2. 插入支付记录
                    string insertQuery = @"INSERT INTO PaymentRecords (AppointmentID, ServiceRecordID, Amount, PaymentMethod, PaymentDate, TransactionNumber, Notes)
                                        VALUES (@AppointmentID, @ServiceRecordID, @Amount, @PaymentMethod, GETDATE(), @TransactionNumber, @Notes);
                                        SELECT SCOPE_IDENTITY()";
                    
                    SqlCommand insertCmd = new SqlCommand(insertQuery, connection, transaction);
                    insertCmd.Parameters.AddWithValue("@AppointmentID", appointmentID);
                    insertCmd.Parameters.AddWithValue("@ServiceRecordID", recordID);
                    insertCmd.Parameters.AddWithValue("@Amount", amount);
                    insertCmd.Parameters.AddWithValue("@PaymentMethod", paymentMethod);
                    insertCmd.Parameters.AddWithValue("@TransactionNumber", transactionNumber ?? (object)DBNull.Value);
                    insertCmd.Parameters.AddWithValue("@Notes", notes ?? (object)DBNull.Value);
                    
                    object result = insertCmd.ExecuteScalar();
                    
                    if (result != null && result != DBNull.Value)
                    {
                        int paymentID = Convert.ToInt32(result);
                        transaction.Commit();
                        Debug.WriteLine($"支付成功 - PaymentID: {paymentID}");
                        return paymentID;
                    }
                    
                    Debug.WriteLine("插入支付记录失败");
                    transaction.Rollback();
                    return -1;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"事务执行出错: {ex.Message}");
                    transaction.Rollback();
                    throw;
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"处理付款时出错: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 检查服务记录是否已支付
    /// </summary>
    /// <param name="recordID">服务记录ID</param>
    /// <returns>已支付返回true，未支付返回false</returns>
    public static bool IsServiceRecordPaid(int recordID)
    {
        string query = "SELECT IsPaid FROM ServiceRecords WHERE RecordID = @RecordID";
        SqlParameter parameter = new SqlParameter("@RecordID", recordID);
        object result = DatabaseHelper.ExecuteScalar(query, parameter);
        return result != null && result != DBNull.Value && Convert.ToBoolean(result);
    }

    /// <summary>
    /// 检查服务记录是否已验收
    /// </summary>
    /// <param name="recordID">服务记录ID</param>
    /// <returns>已验收返回true，未验收返回false</returns>
    public static bool IsServiceRecordVerified(int recordID)
    {
        string query = "SELECT IsVerified FROM ServiceRecords WHERE RecordID = @RecordID";
        SqlParameter parameter = new SqlParameter("@RecordID", recordID);
        object result = DatabaseHelper.ExecuteScalar(query, parameter);
        return result != null && result != DBNull.Value && Convert.ToBoolean(result);
    }

    /// <summary>
    /// 获取支付记录
    /// </summary>
    /// <param name="ownerID">车主ID</param>
    /// <returns>支付记录DataTable</returns>
    public static DataTable GetPaymentRecordsByOwnerID(int ownerID)
    {
        string query = @"SELECT p.PaymentID, p.PaymentDate, p.Amount, p.PaymentMethod, p.TransactionNumber, p.Status,
                        sr.RecordID, sr.CompletedDate, sr.TotalCost,
                        a.AppointmentID, a.Status AS AppointmentStatus,
                        rs.ShopName,
                        s.ServiceName,
                        c.Make, c.Model, c.LicensePlate
                        FROM PaymentRecords p
                        INNER JOIN ServiceRecords sr ON p.ServiceRecordID = sr.RecordID
                        INNER JOIN Appointments a ON p.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE c.OwnerID = @OwnerID
                        ORDER BY p.PaymentDate DESC";
        SqlParameter parameter = new SqlParameter("@OwnerID", ownerID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取所有预约记录（管理员使用）
    /// </summary>
    /// <returns>所有预约记录</returns>
    public static DataTable GetAllAppointments()
    {
        string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description, a.CreatedDate,
                        c.CarID, c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.OwnerID, co.FullName AS OwnerName,
                        rs.ShopID, rs.ShopName, 
                        s.ServiceID, s.ServiceName, s.BasePrice
                        FROM Appointments a
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        ORDER BY a.AppointmentDate DESC";
        return DatabaseHelper.ExecuteQuery(query);
    }
    
    /// <summary>
    /// 获取所有预约记录（管理员使用，带分页）
    /// </summary>
    /// <param name="pageIndex">页码（从0开始）</param>
    /// <param name="pageSize">每页记录数</param>
    /// <returns>指定页的预约记录</returns>
    public static DataTable GetAllAppointmentsPaged(int pageIndex, int pageSize)
    {
        int offset = pageIndex * pageSize;
        
        string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description, a.CreatedDate,
                        c.CarID, c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.OwnerID, co.FullName AS OwnerName,
                        rs.ShopID, rs.ShopName, 
                        s.ServiceID, s.ServiceName, s.BasePrice
                        FROM Appointments a
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        ORDER BY a.AppointmentDate DESC
                        OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";
        
        SqlParameter[] parameters = {
            new SqlParameter("@Offset", offset),
            new SqlParameter("@PageSize", pageSize)
        };
        
        return DatabaseHelper.ExecuteQuery(query, parameters);
    }
    
    /// <summary>
    /// 获取预约总数
    /// </summary>
    /// <returns>预约总数</returns>
    public static int GetAppointmentsCount()
    {
        string query = "SELECT COUNT(*) FROM Appointments";
        object result = DatabaseHelper.ExecuteScalar(query);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        return 0;
    }
    
    /// <summary>
    /// 按条件搜索预约
    /// </summary>
    /// <param name="status">预约状态</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="ownerName">车主姓名（模糊搜索）</param>
    /// <param name="shopName">维修店名称（模糊搜索）</param>
    /// <returns>符合条件的预约记录</returns>
    public static DataTable SearchAppointments(string status = null, DateTime? startDate = null, DateTime? endDate = null, string ownerName = null, string shopName = null)
    {
        List<SqlParameter> parameters = new List<SqlParameter>();
        
        string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description, a.CreatedDate,
                        c.CarID, c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.OwnerID, co.FullName AS OwnerName,
                        rs.ShopID, rs.ShopName, 
                        s.ServiceID, s.ServiceName, s.BasePrice
                        FROM Appointments a
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE 1=1";
                        
        if (!string.IsNullOrEmpty(status))
        {
            query += " AND a.Status = @Status";
            parameters.Add(new SqlParameter("@Status", status));
        }
        
        if (startDate.HasValue)
        {
            query += " AND a.AppointmentDate >= @StartDate";
            parameters.Add(new SqlParameter("@StartDate", startDate.Value));
        }
        
        if (endDate.HasValue)
        {
            query += " AND a.AppointmentDate <= @EndDate";
            parameters.Add(new SqlParameter("@EndDate", endDate.Value));
        }
        
        if (!string.IsNullOrEmpty(ownerName))
        {
            query += " AND co.FullName LIKE @OwnerName";
            parameters.Add(new SqlParameter("@OwnerName", $"%{ownerName}%"));
        }
        
        if (!string.IsNullOrEmpty(shopName))
        {
            query += " AND rs.ShopName LIKE @ShopName";
            parameters.Add(new SqlParameter("@ShopName", $"%{shopName}%"));
        }
        
        query += " ORDER BY a.AppointmentDate DESC";
        
        return DatabaseHelper.ExecuteQuery(query, parameters.ToArray());
    }
    
    /// <summary>
    /// 删除预约（管理员使用）
    /// </summary>
    /// <param name="appointmentID">预约ID</param>
    /// <returns>删除成功返回true，失败返回false</returns>
    public static bool DeleteAppointment(int appointmentID)
    {
        try
        {
            // 首先检查是否有关联的维修记录
            string checkQuery = "SELECT COUNT(*) FROM ServiceRecords WHERE AppointmentID = @AppointmentID";
            SqlParameter checkParam = new SqlParameter("@AppointmentID", appointmentID);
            int recordCount = Convert.ToInt32(DatabaseHelper.ExecuteScalar(checkQuery, checkParam));
            
            if (recordCount > 0)
            {
                // 有关联的维修记录，不能直接删除
                System.Diagnostics.Debug.WriteLine($"无法删除预约ID {appointmentID}，有关联的维修记录");
                return false;
            }
            
            string query = "DELETE FROM Appointments WHERE AppointmentID = @AppointmentID";
            SqlParameter parameter = new SqlParameter("@AppointmentID", appointmentID);
            
            int result = DatabaseHelper.ExecuteNonQuery(query, parameter);
            return result > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"删除预约时出错: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// 更新预约信息（管理员使用）
    /// </summary>
    /// <param name="appointmentID">预约ID</param>
    /// <param name="appointmentDate">预约时间</param>
    /// <param name="status">预约状态</param>
    /// <param name="description">描述</param>
    /// <param name="carID">车辆ID</param>
    /// <param name="shopID">维修店ID</param>
    /// <param name="serviceID">服务ID</param>
    /// <returns>更新成功返回true，失败返回false</returns>
    public static bool UpdateAppointment(int appointmentID, DateTime appointmentDate, string status, string description, int carID, int shopID, int serviceID)
    {
        try
        {
            string query = @"UPDATE Appointments 
                           SET AppointmentDate = @AppointmentDate,
                               Status = @Status,
                               Description = @Description,
                               CarID = @CarID,
                               ShopID = @ShopID,
                               ServiceID = @ServiceID
                           WHERE AppointmentID = @AppointmentID";
                           
            SqlParameter[] parameters = {
                new SqlParameter("@AppointmentID", appointmentID),
                new SqlParameter("@AppointmentDate", appointmentDate),
                new SqlParameter("@Status", status),
                new SqlParameter("@Description", string.IsNullOrEmpty(description) ? (object)DBNull.Value : description),
                new SqlParameter("@CarID", carID),
                new SqlParameter("@ShopID", shopID),
                new SqlParameter("@ServiceID", serviceID)
            };
            
            int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
            return result > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新预约信息时出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 检查RepairShops表中是否存在PhoneNumber列
    /// </summary>
    /// <returns>如果存在返回true，否则返回false</returns>
    private static bool CheckPhoneNumberColumnExists()
    {
        try
        {
            string query = @"SELECT COUNT(1) 
                           FROM INFORMATION_SCHEMA.COLUMNS 
                           WHERE TABLE_NAME = 'RepairShops' 
                           AND COLUMN_NAME = 'PhoneNumber'";
            
            object result = DatabaseHelper.ExecuteScalar(query);
            return result != null && Convert.ToInt32(result) > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"检查PhoneNumber列时出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 根据预约ID获取服务记录
    /// </summary>
    /// <param name="appointmentID">预约ID</param>
    /// <returns>服务记录DataTable</returns>
    public static DataTable GetServiceRecordsByAppointmentID(int appointmentID)
    {
        // 检查PhoneNumber列是否存在
        bool phoneNumberExists = CheckPhoneNumberColumnExists();
        
        // 根据PhoneNumber列是否存在动态构建查询
        string shopColumns = phoneNumberExists ? 
            "rs.ShopID, rs.ShopName, rs.Address, rs.PhoneNumber," : 
            "rs.ShopID, rs.ShopName, rs.Address,";
            
        string query = $@"SELECT sr.RecordID, sr.CompletedDate, sr.DiagnosisDetails, sr.PartsReplaced,
                        sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName, sr.IsPaid, sr.IsVerified,
                        a.AppointmentID, a.AppointmentDate, a.Status, a.Description,
                        {shopColumns}
                        s.ServiceID, s.ServiceName, s.Description AS ServiceDescription,
                        c.CarID, c.Make, c.Model, c.LicensePlate, c.OwnerID
                        FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE a.AppointmentID = @AppointmentID";
        SqlParameter parameter = new SqlParameter("@AppointmentID", appointmentID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 添加评价
    /// </summary>
    /// <param name="serviceRecordID">维修记录ID</param>
    /// <param name="ownerID">车主ID</param>
    /// <param name="shopID">维修店ID</param>
    /// <param name="rating">评分</param>
    /// <param name="comments">评价内容</param>
    /// <returns>成功返回评价ID，失败返回-1</returns>
    public static int AddReview(int serviceRecordID, int ownerID, int shopID, int rating, string comments)
    {
        try
        {
            // 首先验证维修记录ID是否存在
            string verifyQuery = "SELECT COUNT(1) FROM ServiceRecords WHERE RecordID = @RecordID";
            SqlParameter verifyParam = new SqlParameter("@RecordID", serviceRecordID);
            int recordExists = Convert.ToInt32(DatabaseHelper.ExecuteScalar(verifyQuery, verifyParam));
            
            if (recordExists == 0)
            {
                // 记录不存在，记录错误并返回
                System.Diagnostics.Debug.WriteLine($"添加评价失败：维修记录ID {serviceRecordID} 不存在");
                return -1;
            }
            
            // 检查是否已经评价过
            string checkReviewQuery = "SELECT COUNT(1) FROM Reviews WHERE ServiceRecordID = @ServiceRecordID";
            SqlParameter checkParam = new SqlParameter("@ServiceRecordID", serviceRecordID);
            int reviewExists = Convert.ToInt32(DatabaseHelper.ExecuteScalar(checkReviewQuery, checkParam));
            
            if (reviewExists > 0)
            {
                // 已经评价过，记录错误并返回
                System.Diagnostics.Debug.WriteLine($"添加评价失败：维修记录ID {serviceRecordID} 已经评价过");
                return -1;
            }
            
            // 插入新评价
            string query = @"INSERT INTO Reviews (ServiceRecordID, OwnerID, ShopID, Rating, Comments, ReviewDate) 
                            VALUES (@ServiceRecordID, @OwnerID, @ShopID, @Rating, @Comments, GETDATE());
                            SELECT SCOPE_IDENTITY()";
            
            SqlParameter[] parameters =
            {
                new SqlParameter("@ServiceRecordID", serviceRecordID),
                new SqlParameter("@OwnerID", ownerID),
                new SqlParameter("@ShopID", shopID),
                new SqlParameter("@Rating", rating),
                new SqlParameter("@Comments", comments ?? (object)DBNull.Value)
            };
            
            object result = DatabaseHelper.ExecuteScalar(query, parameters);
            
            if (result != null && result != DBNull.Value)
            {
                int reviewID = Convert.ToInt32(result);
                
                // 更新维修店平均评分
                UpdateShopRating(shopID);
                
                System.Diagnostics.Debug.WriteLine($"成功添加评价，评价ID: {reviewID}");
                return reviewID;
            }
            
            System.Diagnostics.Debug.WriteLine("添加评价失败：未能获取新创建的评价ID");
            return -1;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"添加评价时出错: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 更新维修店平均评分
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    private static void UpdateShopRating(int shopID)
    {
        string query = "SELECT AVG(CAST(Rating AS DECIMAL(3,2))) FROM Reviews WHERE ShopID = @ShopID";
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        object result = DatabaseHelper.ExecuteScalar(query, parameter);
        if (result != null && result != DBNull.Value)
        {
            decimal avgRating = Convert.ToDecimal(result);
            string updateQuery = "UPDATE RepairShops SET Rating = @Rating WHERE ShopID = @ShopID";
            SqlParameter[] parameters =
            {
                new SqlParameter("@ShopID", shopID),
                new SqlParameter("@Rating", avgRating)
            };
            DatabaseHelper.ExecuteNonQuery(updateQuery, parameters);
        }
    }

    /// <summary>
    /// 自动取消已过期未确认的预约
    /// </summary>
    /// <returns>取消的预约数量</returns>
    public static int AutoCancelExpiredAppointments()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("开始检查并取消过期未确认的预约...");
            
            // 首先查看当前有哪些待确认的预约
            string pendingQuery = @"SELECT AppointmentID, AppointmentDate, GETDATE() as CurrentTime 
                                   FROM Appointments 
                                   WHERE Status = 'Pending'";
            var pendingAppointments = DatabaseHelper.ExecuteQuery(pendingQuery);
            
            if (pendingAppointments != null && pendingAppointments.Rows.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"发现 {pendingAppointments.Rows.Count} 个待确认的预约");
                foreach (System.Data.DataRow row in pendingAppointments.Rows)
                {
                    System.Diagnostics.Debug.WriteLine($"预约ID: {row["AppointmentID"]}, 预约时间: {row["AppointmentDate"]}, 当前时间: {row["CurrentTime"]}");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("没有找到任何待确认的预约");
                return 0;
            }
            
            // 查找所有状态为Pending（待确认）且预约时间已过的预约
            // 注意：转换日期为不带时区的UTC时间进行比较
            string query = @"UPDATE Appointments 
                            SET Status = 'Cancelled' 
                            WHERE Status = 'Pending' 
                            AND CONVERT(datetime, CONVERT(varchar, AppointmentDate, 120)) < CONVERT(datetime, CONVERT(varchar, GETDATE(), 120));
                            SELECT @@ROWCOUNT";
            
            int cancelledCount = Convert.ToInt32(DatabaseHelper.ExecuteScalar(query));
            
            if (cancelledCount > 0)
            {
                System.Diagnostics.Debug.WriteLine($"成功自动取消了 {cancelledCount} 个过期未确认的预约");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("没有需要取消的过期未确认预约");
            }
            
            // 检查剩余的待确认预约
            var remainingPending = DatabaseHelper.ExecuteQuery(pendingQuery);
            if (remainingPending != null && remainingPending.Rows.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"执行完成后，还有 {remainingPending.Rows.Count} 个待确认的预约");
            }
            
            return cancelledCount;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"自动取消过期预约时出错: {ex.Message}\n{ex.StackTrace}");
            return 0;
        }
    }
} 