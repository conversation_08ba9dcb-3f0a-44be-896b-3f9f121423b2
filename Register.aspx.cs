using System;
using System.Web.Security;
using System.Data.SqlClient;

public partial class Register : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // 如果用户已经登录，则重定向到首页
        if (User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Default.aspx");
        }

        // 更新密码要求提示
        if (!IsPostBack)
        {
            UpdatePasswordRequirements();
            
            // 如果URL中有type参数，设置用户类型
            string userType = Request.QueryString["type"];
            if (!string.IsNullOrEmpty(userType))
            {
                if (userType.Equals("owner", StringComparison.OrdinalIgnoreCase))
                {
                    hdnUserType.Value = "CarOwner";
                }
                else if (userType.Equals("shop", StringComparison.OrdinalIgnoreCase))
                {
                    hdnUserType.Value = "RepairShop";
                }
            }
        }
    }

    /// <summary>
    /// 更新密码要求提示
    /// </summary>
    private void UpdatePasswordRequirements()
    {
        try
        {
            // 获取密码安全设置
            string minLengthStr = SecurityHelper.GetSystemSetting("MinPasswordLength", "8");
            bool requireUppercaseValue = SecurityHelper.GetSystemSetting("RequireUppercase", "true") == "true";
            bool requireDigitValue = SecurityHelper.GetSystemSetting("RequireDigit", "true") == "true";
            bool requireSpecialCharValue = SecurityHelper.GetSystemSetting("RequireSpecialChar", "false") == "true";
            
            // 更新密码要求提示
            minLength.Text = minLengthStr;
            requireUppercase.Visible = requireUppercaseValue;
            requireDigit.Visible = requireDigitValue;
            requireSpecialChar.Visible = requireSpecialCharValue;
        }
        catch (Exception ex)
        {
            // 如果更新密码要求提示失败，忽略错误，不影响注册功能
            System.Diagnostics.Debug.WriteLine("更新密码要求提示失败: " + ex.Message);
        }
    }

    protected void btnRegister_Click(object sender, EventArgs e)
    {
        string username = txtUsername.Text.Trim();
        string password = txtPassword.Text.Trim();
        string confirmPassword = txtConfirmPassword.Text.Trim();
        string email = txtEmail.Text.Trim();
        string phoneNumber = txtPhoneNumber.Text.Trim();
        string userType = hdnUserType.Value; // 使用隐藏字段获取用户类型

        // 检查密码和确认密码是否一致
        if (password != confirmPassword)
        {
            lblMessage.Text = "两次输入的密码不一致，请重新输入。";
            lblMessage.CssClass = "text-danger";
            return;
        }

        // 验证密码强度
        string errorMessage;
        if (!SecurityHelper.ValidatePasswordStrength(password, out errorMessage))
        {
            lblMessage.Text = errorMessage;
            lblMessage.CssClass = "text-danger";
            return;
        }

        // 检查用户名是否已存在
        if (UserManager.IsUsernameExists(username))
        {
            lblMessage.Text = "用户名已被使用，请更换其他用户名。";
            lblMessage.CssClass = "text-danger";
            return;
        }

        // 检查邮箱是否已存在
        if (UserManager.IsEmailExists(email))
        {
            lblMessage.Text = "此邮箱已被注册，请使用其他邮箱。";
            lblMessage.CssClass = "text-danger";
            return;
        }

        // 收集类型特定信息
        string fullName = string.Empty;
        string gender = string.Empty;
        string shopName = string.Empty;
        string businessLicense = string.Empty;
        string shopAddress = string.Empty;
        string shopType = string.Empty;

        // 根据用户类型收集不同的额外信息
        if (userType == "CarOwner")
        {
            fullName = txtFullName.Text.Trim();
            gender = ddlGender.SelectedValue;
            
            // 验证车主特有字段
            if (string.IsNullOrEmpty(fullName))
            {
                lblMessage.Text = "请输入您的姓名。";
                lblMessage.CssClass = "text-danger";
                return;
            }
        }
        else if (userType == "RepairShop")
        {
            shopName = txtShopName.Text.Trim();
            businessLicense = txtBusinessLicense.Text.Trim();
            shopAddress = txtShopAddress.Text.Trim();
            shopType = ddlShopType.SelectedValue;
            
            // 验证维修店特有字段
            if (string.IsNullOrEmpty(shopName))
            {
                lblMessage.Text = "请输入维修店名称。";
                lblMessage.CssClass = "text-danger";
                return;
            }
            
            if (string.IsNullOrEmpty(businessLicense))
            {
                lblMessage.Text = "请输入营业执照号。";
                lblMessage.CssClass = "text-danger";
                return;
            }
            
            if (string.IsNullOrEmpty(shopAddress))
            {
                lblMessage.Text = "请输入店铺地址。";
                lblMessage.CssClass = "text-danger";
                return;
            }
            
            if (string.IsNullOrEmpty(shopType))
            {
                lblMessage.Text = "请选择店铺类型。";
                lblMessage.CssClass = "text-danger";
                return;
            }
        }

        // 注册用户
        int userId = UserManager.RegisterUser(username, password, email, phoneNumber, userType);
        if (userId > 0)
        {
            // 保存额外信息
            if (userType == "CarOwner")
            {
                // 直接实现保存车主信息的逻辑，而不是调用UserManager.SaveCarOwnerInfo
                try
                {
                    string query = @"UPDATE CarOwners SET 
                                    FullName = @FullName,
                                    Gender = @Gender
                                    WHERE UserID = @UserID";

                    SqlParameter[] parameters =
                    {
                        new SqlParameter("@UserID", userId),
                        new SqlParameter("@FullName", fullName),
                        new SqlParameter("@Gender", string.IsNullOrEmpty(gender) ? (object)DBNull.Value : gender)
                    };

                    DatabaseHelper.ExecuteNonQuery(query, parameters);
                }
                catch (Exception ex)
                {
                    // 记录错误但继续执行
                    System.Diagnostics.Debug.WriteLine("保存车主信息失败: " + ex.Message);
                }
            }
            else if (userType == "RepairShop")
            {
                // 直接实现保存维修店信息的逻辑，而不是调用UserManager.SaveRepairShopInfo
                try
                {
                    string query = @"UPDATE RepairShops SET 
                                    ShopName = @ShopName,
                                    BusinessLicense = @BusinessLicense,
                                    Address = @Address,
                                    ShopType = @ShopType
                                    WHERE UserID = @UserID";

                    SqlParameter[] parameters =
                    {
                        new SqlParameter("@UserID", userId),
                        new SqlParameter("@ShopName", shopName),
                        new SqlParameter("@BusinessLicense", businessLicense),
                        new SqlParameter("@Address", shopAddress),
                        new SqlParameter("@ShopType", string.IsNullOrEmpty(shopType) ? (object)DBNull.Value : shopType)
                    };

                    DatabaseHelper.ExecuteNonQuery(query, parameters);
                }
                catch (Exception ex)
                {
                    // 记录错误但继续执行
                    System.Diagnostics.Debug.WriteLine("保存维修店信息失败: " + ex.Message);
                }
            }

            // 注册成功，自动登录
            UserInfo userInfo = new UserInfo
            {
                UserID = userId,
                Username = username,
                Email = email,
                UserType = userType
            };

            // 记录用户登录状态
            FormsAuthentication.SetAuthCookie(username, false);
            // 在Session中保存用户信息
            Session["UserID"] = userInfo.UserID;
            Session["UserType"] = userInfo.UserType;
            Session["Username"] = userInfo.Username;

            // 根据用户类型重定向到不同的页面
            switch (userType)
            {
                case "CarOwner":
                    Response.Redirect("~/CarOwner/Dashboard.aspx");
                    break;
                case "RepairShop":
                    Response.Redirect("~/RepairShop/Dashboard.aspx");
                    break;
                default:
                    Response.Redirect("~/Default.aspx");
                    break;
            }
        }
        else
        {
            // 注册失败
            lblMessage.Text = "注册失败，请稍后再试。";
            lblMessage.CssClass = "text-danger";
        }
    }
} 