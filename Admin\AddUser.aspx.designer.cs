//------------------------------------------------------------------------------
// <自动生成>
//     此代码由工具生成。
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。 
// </自动生成>
//------------------------------------------------------------------------------

public partial class Admin_AddUser {
    
    /// <summary>
    /// ValidationSummary1 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.ValidationSummary ValidationSummary1;
    
    /// <summary>
    /// lblMessage 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label lblMessage;
    
    /// <summary>
    /// ddlUserType 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.DropDownList ddlUserType;
    
    /// <summary>
    /// txtUsername 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtUsername;
    
    /// <summary>
    /// rfvUsername 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvUsername;
    
    /// <summary>
    /// cvUsername 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.CustomValidator cvUsername;
    
    /// <summary>
    /// txtPassword 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtPassword;
    
    /// <summary>
    /// rfvPassword 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvPassword;
    
    /// <summary>
    /// revPassword 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RegularExpressionValidator revPassword;
    
    /// <summary>
    /// txtEmail 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtEmail;
    
    /// <summary>
    /// rfvEmail 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvEmail;
    
    /// <summary>
    /// revEmail 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RegularExpressionValidator revEmail;
    
    /// <summary>
    /// txtPhoneNumber 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtPhoneNumber;
    
    /// <summary>
    /// cvPhoneNumber 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.CustomValidator cvPhoneNumber;
    
    /// <summary>
    /// chkIsActive 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.CheckBox chkIsActive;
    
    /// <summary>
    /// pnlCarOwner 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel pnlCarOwner;
    
    /// <summary>
    /// txtFullName 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtFullName;
    
    /// <summary>
    /// rfvFullName 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvFullName;
    
    /// <summary>
    /// txtAddress 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtAddress;
    
    /// <summary>
    /// pnlRepairShop 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel pnlRepairShop;
    
    /// <summary>
    /// txtShopName 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtShopName;
    
    /// <summary>
    /// rfvShopName 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvShopName;
    
    /// <summary>
    /// txtShopAddress 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtShopAddress;
    
    /// <summary>
    /// rfvShopAddress 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvShopAddress;
    
    /// <summary>
    /// txtContactPerson 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtContactPerson;
    
    /// <summary>
    /// txtDescription 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtDescription;
    
    /// <summary>
    /// pnlAdmin 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel pnlAdmin;
    
    /// <summary>
    /// btnSave 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnSave;
} 