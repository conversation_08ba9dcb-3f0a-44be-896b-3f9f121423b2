<%@ Page Title="车主主页" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="CarOwner_Dashboard" Codebehind="Dashboard.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-tachometer-alt"></i> 车主控制台</h2>
                <hr />
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-car"></i> 我的车辆</h5>
                    </div>
                    <div class="card-body">
                        <p>管理您的车辆信息，添加、编辑或删除车辆。</p>
                        <asp:Label ID="lblCarCount" runat="server" CssClass="h4 d-block mb-3"></asp:Label>
                        <asp:HyperLink ID="HyperLink1" runat="server" CssClass="btn btn-primary btn-block" NavigateUrl="~/CarOwner/MyCars.aspx">车辆管理</asp:HyperLink>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> 维修预约</h5>
                    </div>
                    <div class="card-body">
                        <p>查看您的维修预约，预约新的维修服务。</p>
                        <asp:Label ID="lblAppointmentCount" runat="server" CssClass="h4 d-block mb-3"></asp:Label>
                        <asp:HyperLink ID="HyperLink2" runat="server" CssClass="btn btn-success btn-block" NavigateUrl="~/CarOwner/Appointments.aspx">预约管理</asp:HyperLink>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0"><i class="fas fa-search"></i> 智能服务搜索</h5>
                    </div>
                    <div class="card-body">
                        <p>根据服务类型、价格、地址和评分多条件筛选维修服务。</p>
                        <br class="mb-3" />
                        <asp:HyperLink ID="HyperLink5" runat="server" CssClass="btn btn-danger btn-block" NavigateUrl="~/CarOwner/ServiceSearch.aspx">搜索服务</asp:HyperLink>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-history"></i> 维修记录</h5>
                    </div>
                    <div class="card-body">
                        <p>查看您的车辆维修历史记录。</p>
                        <asp:Label ID="lblServiceCount" runat="server" CssClass="h4 d-block mb-3"></asp:Label>
                        <asp:HyperLink ID="HyperLink3" runat="server" CssClass="btn btn-info btn-block" NavigateUrl="~/CarOwner/ServiceHistory.aspx">查看记录</asp:HyperLink>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="fas fa-user-edit"></i> 个人信息</h5>
                    </div>
                    <div class="card-body">
                        <p>管理您的个人资料、联系方式和偏好设置。</p>
                        <br class="mb-3" />
                        <asp:HyperLink ID="HyperLink4" runat="server" CssClass="btn btn-secondary btn-block" NavigateUrl="~/CarOwner/Profile.aspx">编辑信息</asp:HyperLink>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning">
                        <h5 class="mb-0"><i class="fas fa-bell"></i> 近期预约</h5>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvUpcomingAppointments" runat="server" AutoGenerateColumns="False" CssClass="table table-striped table-hover"
                            EmptyDataText="暂无近期预约">
                            <Columns>
                                <asp:BoundField DataField="AppointmentDate" HeaderText="预约时间" DataFormatString="{0:yyyy-MM-dd HH:mm}" />
                                <asp:BoundField DataField="CarInfo" HeaderText="车辆" />
                                <asp:BoundField DataField="ShopName" HeaderText="维修店" />
                                <asp:BoundField DataField="ServiceName" HeaderText="服务项目" />
                                <asp:BoundField DataField="Status" HeaderText="状态" />
                            </Columns>
                        </asp:GridView>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-bullhorn"></i> 系统通知</h5>
                    </div>
                    <div class="card-body">
                        <asp:Repeater ID="rptNotifications" runat="server">
                            <ItemTemplate>
                                <div class="alert alert-info mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> <%# Eval("Title") %></h6>
                                        <small class="text-muted"><%# Convert.ToDateTime(Eval("CreatedDate")).ToString("yyyy-MM-dd") %></small>
                                    </div>
                                    <p class="mb-0"><%# Eval("Content") %></p>
                                </div>
                            </ItemTemplate>
                            <FooterTemplate>
                                <% if (rptNotifications.Items.Count == 0) { %>
                                    <div class="alert alert-light">
                                        暂无系统通知
                                    </div>
                                <% } %>
                            </FooterTemplate>
                        </asp:Repeater>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content> 