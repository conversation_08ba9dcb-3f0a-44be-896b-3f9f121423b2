using System;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// 车辆管理类
/// </summary>
public class CarManager
{
    /// <summary>
    /// 获取车主的所有车辆
    /// </summary>
    /// <param name="ownerID">车主ID</param>
    /// <returns>车辆DataTable</returns>
    public static DataTable GetCarsByOwnerID(int ownerID)
    {
        string query = @"SELECT c.CarID, c.Make, c.Model, c.Year, c.LicensePlate, c.VIN, c.Color, c.PhotoUrl 
                        FROM Cars c
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        WHERE co.OwnerID = @OwnerID
                        ORDER BY c.Make, c.Model";
        SqlParameter parameter = new SqlParameter("@OwnerID", ownerID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 根据车辆ID获取车辆信息
    /// </summary>
    /// <param name="carID">车辆ID</param>
    /// <returns>车辆DataTable</returns>
    public static DataTable GetCarByID(int carID)
    {
        string query = "SELECT * FROM Cars WHERE CarID = @CarID";
        SqlParameter parameter = new SqlParameter("@CarID", carID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 添加新车辆
    /// </summary>
    /// <param name="ownerID">车主ID</param>
    /// <param name="make">厂商</param>
    /// <param name="model">型号</param>
    /// <param name="year">年份</param>
    /// <param name="licensePlate">车牌号</param>
    /// <param name="vin">车架号</param>
    /// <param name="color">颜色</param>
    /// <param name="photoUrl">照片URL</param>
    /// <returns>成功返回车辆ID，失败返回-1</returns>
    public static int AddCar(int ownerID, string make, string model, int year, string licensePlate, string vin, string color, string photoUrl)
    {
        string query = @"INSERT INTO Cars (OwnerID, Make, Model, Year, LicensePlate, VIN, Color, PhotoUrl)
                        VALUES (@OwnerID, @Make, @Model, @Year, @LicensePlate, @VIN, @Color, @PhotoUrl);
                        SELECT SCOPE_IDENTITY()";

        SqlParameter[] parameters =
        {
            new SqlParameter("@OwnerID", ownerID),
            new SqlParameter("@Make", make),
            new SqlParameter("@Model", model),
            new SqlParameter("@Year", year),
            new SqlParameter("@LicensePlate", licensePlate),
            new SqlParameter("@VIN", vin ?? (object)DBNull.Value),
            new SqlParameter("@Color", color ?? (object)DBNull.Value),
            new SqlParameter("@PhotoUrl", photoUrl ?? (object)DBNull.Value)
        };

        object result = DatabaseHelper.ExecuteScalar(query, parameters);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        return -1;
    }

    /// <summary>
    /// 更新车辆信息
    /// </summary>
    /// <param name="carID">车辆ID</param>
    /// <param name="make">厂商</param>
    /// <param name="model">型号</param>
    /// <param name="year">年份</param>
    /// <param name="licensePlate">车牌号</param>
    /// <param name="vin">车架号</param>
    /// <param name="color">颜色</param>
    /// <param name="photoUrl">照片URL</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool UpdateCar(int carID, string make, string model, int year, string licensePlate, string vin, string color, string photoUrl)
    {
        string query = @"UPDATE Cars
                        SET Make = @Make, Model = @Model, Year = @Year, LicensePlate = @LicensePlate, VIN = @VIN, Color = @Color, PhotoUrl = @PhotoUrl
                        WHERE CarID = @CarID";

        SqlParameter[] parameters =
        {
            new SqlParameter("@CarID", carID),
            new SqlParameter("@Make", make),
            new SqlParameter("@Model", model),
            new SqlParameter("@Year", year),
            new SqlParameter("@LicensePlate", licensePlate),
            new SqlParameter("@VIN", vin ?? (object)DBNull.Value),
            new SqlParameter("@Color", color ?? (object)DBNull.Value),
            new SqlParameter("@PhotoUrl", photoUrl ?? (object)DBNull.Value)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }

    /// <summary>
    /// 删除车辆
    /// </summary>
    /// <param name="carID">车辆ID</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool DeleteCar(int carID)
    {
        // 首先检查是否有相关的预约
        string checkQuery = "SELECT COUNT(1) FROM Appointments WHERE CarID = @CarID";
        SqlParameter checkParam = new SqlParameter("@CarID", carID);
        int count = Convert.ToInt32(DatabaseHelper.ExecuteScalar(checkQuery, checkParam));
        if (count > 0)
        {
            // 如果存在相关预约，不允许删除
            return false;
        }

        string query = "DELETE FROM Cars WHERE CarID = @CarID";
        SqlParameter parameter = new SqlParameter("@CarID", carID);
        int result = DatabaseHelper.ExecuteNonQuery(query, parameter);
        return result > 0;
    }

    /// <summary>
    /// 获取车主ID
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <returns>车主ID</returns>
    public static int GetOwnerIDByUserID(int userID)
    {
        string query = "SELECT OwnerID FROM CarOwners WHERE UserID = @UserID";
        SqlParameter parameter = new SqlParameter("@UserID", userID);
        object result = DatabaseHelper.ExecuteScalar(query, parameter);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        return -1;
    }
    
    /// <summary>
    /// 获取车主信息
    /// </summary>
    /// <param name="ownerID">车主ID</param>
    /// <returns>车主信息DataTable</returns>
    public static DataTable GetOwnerInfo(int ownerID)
    {
        // 检查AvatarUrl列是否存在
        bool avatarColumnExists = CheckIfColumnExists("CarOwners", "AvatarUrl");
        
        string query;
        if (avatarColumnExists)
        {
            query = @"SELECT co.OwnerID, co.FullName, co.Address, co.AvatarUrl,
                      u.UserID, u.Username, u.Email, u.PhoneNumber as UserPhoneNumber
                      FROM CarOwners co
                      INNER JOIN Users u ON co.UserID = u.UserID
                      WHERE co.OwnerID = @OwnerID";
        }
        else
        {
            // 如果AvatarUrl列不存在，添加该列
            string addColumnQuery = "ALTER TABLE CarOwners ADD AvatarUrl NVARCHAR(255) NULL";
            try
            {
                DatabaseHelper.ExecuteNonQuery(addColumnQuery);
                
                // 列已添加，使用包含AvatarUrl的查询
                query = @"SELECT co.OwnerID, co.FullName, co.Address, co.AvatarUrl,
                          u.UserID, u.Username, u.Email, u.PhoneNumber as UserPhoneNumber
                          FROM CarOwners co
                          INNER JOIN Users u ON co.UserID = u.UserID
                          WHERE co.OwnerID = @OwnerID";
            }
            catch
            {
                // 如果添加列失败，使用不包含AvatarUrl的查询
                query = @"SELECT co.OwnerID, co.FullName, co.Address,
                          u.UserID, u.Username, u.Email, u.PhoneNumber as UserPhoneNumber
                          FROM CarOwners co
                          INNER JOIN Users u ON co.UserID = u.UserID
                          WHERE co.OwnerID = @OwnerID";
            }
        }
                        
        SqlParameter parameter = new SqlParameter("@OwnerID", ownerID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }
    
    /// <summary>
    /// 更新车主信息
    /// </summary>
    /// <param name="ownerID">车主ID</param>
    /// <param name="fullName">姓名</param>
    /// <param name="address">地址</param>
    /// <param name="city">城市</param>
    /// <param name="state">省/州</param>
    /// <param name="zipCode">邮编</param>
    /// <param name="phoneNumber">电话</param>
    /// <param name="avatarUrl">头像URL</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool UpdateOwnerInfo(int ownerID, string fullName, string address, string city, string state, string zipCode, string phoneNumber, string avatarUrl)
    {
        string query = @"UPDATE CarOwners
                        SET FullName = @FullName,
                            Address = @Address
                        WHERE OwnerID = @OwnerID";

        SqlParameter[] parameters =
        {
            new SqlParameter("@OwnerID", ownerID),
            new SqlParameter("@FullName", fullName),
            new SqlParameter("@Address", address ?? (object)DBNull.Value)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }
    
    /// <summary>
    /// 更新用户基本信息
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <param name="email">邮箱</param>
    /// <param name="phoneNumber">电话</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool UpdateUserInfo(int userID, string email, string phoneNumber)
    {
        string query = @"UPDATE Users
                        SET Email = @Email,
                            PhoneNumber = @PhoneNumber
                        WHERE UserID = @UserID";

        SqlParameter[] parameters =
        {
            new SqlParameter("@UserID", userID),
            new SqlParameter("@Email", email),
            new SqlParameter("@PhoneNumber", phoneNumber ?? (object)DBNull.Value)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }

    /// <summary>
    /// 更新车主头像
    /// </summary>
    /// <param name="ownerID">车主ID</param>
    /// <param name="avatarUrl">头像URL</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool UpdateOwnerAvatar(int ownerID, string avatarUrl)
    {
        // 首先检查AvatarUrl列是否存在
        bool columnExists = CheckIfColumnExists("CarOwners", "AvatarUrl");
        
        if (!columnExists)
        {
            // 如果列不存在，添加AvatarUrl列
            string addColumnQuery = "ALTER TABLE CarOwners ADD AvatarUrl NVARCHAR(255) NULL";
            DatabaseHelper.ExecuteNonQuery(addColumnQuery);
        }
        
        // 更新头像URL
        string query = @"UPDATE CarOwners
                        SET AvatarUrl = @AvatarUrl
                        WHERE OwnerID = @OwnerID";

        SqlParameter[] parameters =
        {
            new SqlParameter("@OwnerID", ownerID),
            new SqlParameter("@AvatarUrl", avatarUrl ?? (object)DBNull.Value)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }
    
    /// <summary>
    /// 检查表中是否存在指定列
    /// </summary>
    /// <param name="tableName">表名</param>
    /// <param name="columnName">列名</param>
    /// <returns>存在返回true，不存在返回false</returns>
    private static bool CheckIfColumnExists(string tableName, string columnName)
    {
        string query = @"SELECT COUNT(1) 
                        FROM INFORMATION_SCHEMA.COLUMNS 
                        WHERE TABLE_NAME = @TableName 
                        AND COLUMN_NAME = @ColumnName";
                        
        SqlParameter[] parameters = 
        {
            new SqlParameter("@TableName", tableName),
            new SqlParameter("@ColumnName", columnName)
        };
        
        object result = DatabaseHelper.ExecuteScalar(query, parameters);
        return Convert.ToInt32(result) > 0;
    }
} 