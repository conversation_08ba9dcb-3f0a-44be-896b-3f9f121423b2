﻿++解决方案 'WebApplication1' ‎ (1 个项目，共 1 个)
i:{00000000-0000-0000-0000-000000000000}:WebApplication1.sln
++WebApplication1
i:{00000000-0000-0000-0000-000000000000}:WebApplication1
++Properties
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\properties\
++引用
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++Admin
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\
++AddUser.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\adduser.aspx
++CategoryManagement.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\categorymanagement.aspx
++Dashboard.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\dashboard.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\dashboard.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\dashboard.aspx
++Dashboard.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\dashboard.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\dashboard.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\dashboard.aspx.cs
++Dashboard.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\dashboard.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\dashboard.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\dashboard.aspx.designer.cs
++Dashboard.aspx.bak
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\dashboard.aspx.bak
++Dashboard.aspx.old
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\dashboard.aspx.old
++ReviewManagement.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\reviewmanagement.aspx
++ReviewManagement.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\reviewmanagement.aspx.cs
++ReviewManagement.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\reviewmanagement.aspx.designer.cs
++ShopManagement.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\shopmanagement.aspx
++ShopManagement.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\shopmanagement.aspx.cs
++ShopManagement.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\shopmanagement.aspx.designer.cs
++SystemSettings.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\systemsettings.aspx
++SystemSettings.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\systemsettings.aspx.cs
++SystemSettings.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\systemsettings.aspx.designer.cs
++UserManagement.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\usermanagement.aspx
++UserManagement.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\usermanagement.aspx.cs
++UserManagement.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\usermanagement.aspx.designer.cs
++App_Data
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\app_data\
++Backups
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\app_data\backups\
++ReviewPhotosTable.sql
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\app_data\reviewphotostable.sql
++App_Start
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\app_start\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\content\app_start\
++bin
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\
++CarOwner
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\
++Appointments.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\appointments.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\appointments.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\appointments.aspx
++MyCars.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\mycars.aspx
++Profile.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\profile.aspx
++Profile.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\profile.aspx.cs
++Profile.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\profile.aspx.designer.cs
++ServiceHistory.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\servicehistory.aspx
++ServiceHistory.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\servicehistory.aspx.cs
++ServiceHistory.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\servicehistory.aspx.designer.cs
++Content
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\content\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\content\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\modernizr.2.8.3\content\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\
++Images
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\
++Cars
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\cars\
++2f9545bb-7503-4008-b6f2-0f02f75e69eb.jpg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\cars\2f9545bb-7503-4008-b6f2-0f02f75e69eb.jpg
++6ab2a7e8-36a6-43ab-94eb-31c867962ac0.jpg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\cars\6ab2a7e8-36a6-43ab-94eb-31c867962ac0.jpg
++ReviewPhotos
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\reviewphotos\
++Shops
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\shops\
++4fbe72ff-a5bc-4484-8b20-afe17c571bae.jpg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\shops\4fbe72ff-a5bc-4484-8b20-afe17c571bae.jpg
++c6140801-2823-4e83-9075-6d12493c4fbb.gif
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\shops\c6140801-2823-4e83-9075-6d12493c4fbb.gif
++da68802f-e6be-4f36-a4da-907f14b597ef.gif
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\shops\da68802f-e6be-4f36-a4da-907f14b597ef.gif
++obj
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\
++Old_App_Code
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\
++AdminManager.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\adminmanager.cs
++AppointmentManager.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\appointmentmanager.cs
++CarManager.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\carmanager.cs
++DatabaseHelper.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\databasehelper.cs
++FileUploadHelper.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\fileuploadhelper.cs
++PhotoManager.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\photomanager.cs
++SecurityHelper.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\securityhelper.cs
++ShopManager.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\shopmanager.cs
++UserInfo.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\userinfo.cs
++UserManager.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\usermanager.cs
++VerificationHelper.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\old_app_code\verificationhelper.cs
++packages
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\
++RepairShop
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\
++ServiceRecords.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\servicerecords.aspx
++ServiceRecords.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\servicerecords.aspx.cs
++ServiceRecords.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\servicerecords.aspx.designer.cs
++Services.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\services.aspx
++Services.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\services.aspx.cs
++Services.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\services.aspx.designer.cs
++Scripts
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\content\scripts\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\modernizr.2.8.3\content\scripts\
++SQL
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\sql\
++CreateVerificationCodesTable.sql
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\sql\createverificationcodestable.sql
++Uploads
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\uploads\
++Avatars
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\uploads\avatars\
++About.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\about.aspx
++Bundle.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bundle.config
++Contact.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\contact.aspx
++Default.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\default.aspx
++Default.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\default.aspx.cs
++Default.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\default.aspx.designer.cs
++favicon.ico
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\favicon.ico
++ForgotPassword.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\forgotpassword.aspx
++ForgotPassword.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\forgotpassword.aspx.cs
++ForgotPassword.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\forgotpassword.aspx.designer.cs
++Global.asax
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\global.asax
++Login.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\login.aspx
++Login.aspx.bak
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\login.aspx.bak
++Login.aspx.old
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\login.aspx.old
++MasterPage.master
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\masterpage.master
++MasterPage.master.bak
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\masterpage.master.bak
++packages.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages.config
++Register.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\register.aspx
++Register.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\register.aspx.cs
++Register.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\register.aspx.designer.cs
++Register.aspx.bak
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\register.aspx.bak
++Register.aspx.old
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\register.aspx.old
++Search.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\search.aspx
++Search.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\search.aspx.cs
++Search.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\search.aspx.designer.cs
++Search.aspx.old
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\search.aspx.old
++ShopDetails.aspx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\shopdetails.aspx
++ShopDetails.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\shopdetails.aspx.cs
++ShopDetails.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\shopdetails.aspx.designer.cs
++Site.Master
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\site.master
++Site.Master.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\site.master.cs
++Site.Master.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\site.master.designer.cs
++Site.Mobile.Master
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\site.mobile.master
++Site.Mobile.Master.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\site.mobile.master.cs
++Site.Mobile.Master.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\site.mobile.master.designer.cs
++ViewSwitcher.ascx
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\viewswitcher.ascx
++ViewSwitcher.ascx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\viewswitcher.ascx.cs
++ViewSwitcher.ascx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\viewswitcher.ascx.designer.cs
++Web.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\web.config
++AssemblyInfo.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\properties\assemblyinfo.cs
++Antlr3.Runtime
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++Microsoft.AspNet.FriendlyUrls
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++Microsoft.AspNet.Web.Optimization.WebForms
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++Microsoft.CodeDom.Providers.DotNetCompilerPlatform
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++Microsoft.CSharp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++Microsoft.ScriptManager.MSAjax
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++Microsoft.ScriptManager.WebForms
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++Microsoft.Web.Infrastructure
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++Newtonsoft.Json
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.ComponentModel.DataAnnotations
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Configuration
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Core
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Data
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Data.DataSetExtensions
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Drawing
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.EnterpriseServices
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Web
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Web.ApplicationServices
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Web.DynamicData
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Web.Entity
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Web.Extensions
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Web.Optimization
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Web.Services
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++System.Xml.Linq
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++WebGrease
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:
++AddUser.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\adduser.aspx.cs
++AddUser.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\adduser.aspx.designer.cs
++CategoryManagement.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\categorymanagement.aspx.cs
++CategoryManagement.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\admin\categorymanagement.aspx.designer.cs
++CarRepairServiceDB_20250622_180921.bak
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\app_data\backups\carrepairservicedb_20250622_180921.bak
++CarRepairServiceDB_20250624_222721.bak
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\app_data\backups\carrepairservicedb_20250624_222721.bak
++BundleConfig.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\app_start\bundleconfig.cs
++RouteConfig.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\app_start\routeconfig.cs
++roslyn
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\
++zh-Hans
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\zh-hans\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net40\zh-hans\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net45\zh-hans\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\lib\net40\zh-hans\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\lib\net45\zh-hans\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.1.1.3\lib\net40\zh-hans\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.zh-hans.1.1.3\lib\net40\zh-hans\
++Antlr3.Runtime.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\antlr3.runtime.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\antlr.*******\lib\antlr3.runtime.dll
++Antlr3.Runtime.pdb
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\antlr3.runtime.pdb
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\antlr.*******\lib\antlr3.runtime.pdb
++Microsoft.AspNet.FriendlyUrls.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\microsoft.aspnet.friendlyurls.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net40\microsoft.aspnet.friendlyurls.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net45\microsoft.aspnet.friendlyurls.dll
++Microsoft.AspNet.FriendlyUrls.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\microsoft.aspnet.friendlyurls.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net40\microsoft.aspnet.friendlyurls.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net45\microsoft.aspnet.friendlyurls.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net40\zh-hans\microsoft.aspnet.friendlyurls.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net45\zh-hans\microsoft.aspnet.friendlyurls.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\lib\net40\zh-hans\microsoft.aspnet.friendlyurls.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\lib\net45\zh-hans\microsoft.aspnet.friendlyurls.xml
++Microsoft.AspNet.Web.Optimization.WebForms.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\microsoft.aspnet.web.optimization.webforms.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.webforms.1.1.3\lib\net45\microsoft.aspnet.web.optimization.webforms.dll
++Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\microsoft.codedom.providers.dotnetcompilerplatform.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\lib\net45\microsoft.codedom.providers.dotnetcompilerplatform.dll
++Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\microsoft.codedom.providers.dotnetcompilerplatform.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\lib\net45\microsoft.codedom.providers.dotnetcompilerplatform.xml
++Microsoft.ScriptManager.MSAjax.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\microsoft.scriptmanager.msajax.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\lib\net45\microsoft.scriptmanager.msajax.dll
++Microsoft.ScriptManager.WebForms.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\microsoft.scriptmanager.webforms.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\lib\net45\microsoft.scriptmanager.webforms.dll
++Microsoft.Web.Infrastructure.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\microsoft.web.infrastructure.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.web.infrastructure.2.0.0\lib\net40\microsoft.web.infrastructure.dll
++Newtonsoft.Json.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\newtonsoft.json.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net20\newtonsoft.json.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net35\newtonsoft.json.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net40\newtonsoft.json.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net45\newtonsoft.json.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net6.0\newtonsoft.json.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\netstandard1.0\newtonsoft.json.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\netstandard1.3\newtonsoft.json.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\netstandard2.0\newtonsoft.json.dll
++Newtonsoft.Json.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\newtonsoft.json.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net20\newtonsoft.json.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net35\newtonsoft.json.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net40\newtonsoft.json.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net45\newtonsoft.json.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net6.0\newtonsoft.json.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\netstandard1.0\newtonsoft.json.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\netstandard1.3\newtonsoft.json.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\netstandard2.0\newtonsoft.json.xml
++System.Web.Optimization.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\system.web.optimization.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.1.1.3\lib\net40\system.web.optimization.dll
++System.Web.Optimization.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\system.web.optimization.xml
++WebApplication1.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\webapplication1.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\debug\webapplication1.dll
++WebApplication1.dll.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\webapplication1.dll.config
++WebApplication1.pdb
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\webapplication1.pdb
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\debug\webapplication1.pdb
++WebGrease.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\webgrease.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\webgrease.1.6.0\lib\webgrease.dll
++Appointments.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\appointments.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\appointments.aspx.cs
++Appointments.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\appointments.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\repairshop\appointments.aspx.designer.cs
++MyCars.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\mycars.aspx.cs
++MyCars.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\carowner\mycars.aspx.designer.cs
++bootstrap-grid.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-grid.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-grid.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-grid.css
++bootstrap-grid.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-grid.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-grid.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-grid.css.map
++bootstrap-grid.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-grid.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-grid.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-grid.min.css
++bootstrap-grid.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-grid.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-grid.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-grid.min.css.map
++bootstrap-grid.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-grid.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-grid.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-grid.rtl.css
++bootstrap-grid.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-grid.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-grid.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-grid.rtl.css.map
++bootstrap-grid.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-grid.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-grid.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-grid.rtl.min.css
++bootstrap-grid.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-grid.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-grid.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-grid.rtl.min.css.map
++bootstrap-reboot.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-reboot.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-reboot.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-reboot.css
++bootstrap-reboot.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-reboot.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-reboot.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-reboot.css.map
++bootstrap-reboot.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-reboot.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-reboot.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-reboot.min.css
++bootstrap-reboot.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-reboot.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-reboot.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-reboot.min.css.map
++bootstrap-reboot.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-reboot.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-reboot.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-reboot.rtl.css
++bootstrap-reboot.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-reboot.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-reboot.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-reboot.rtl.css.map
++bootstrap-reboot.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-reboot.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-reboot.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-reboot.rtl.min.css
++bootstrap-reboot.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-reboot.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-reboot.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-reboot.rtl.min.css.map
++bootstrap-utilities.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-utilities.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-utilities.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-utilities.css
++bootstrap-utilities.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-utilities.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-utilities.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-utilities.css.map
++bootstrap-utilities.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-utilities.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-utilities.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-utilities.min.css
++bootstrap-utilities.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-utilities.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-utilities.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-utilities.min.css.map
++bootstrap-utilities.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-utilities.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-utilities.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-utilities.rtl.css
++bootstrap-utilities.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-utilities.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-utilities.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-utilities.rtl.css.map
++bootstrap-utilities.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-utilities.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-utilities.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-utilities.rtl.min.css
++bootstrap-utilities.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap-utilities.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap-utilities.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap-utilities.rtl.min.css.map
++bootstrap.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap.css
++bootstrap.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap.css.map
++bootstrap.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap.min.css
++bootstrap.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap.min.css.map
++bootstrap.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap.rtl.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap.rtl.css
++bootstrap.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap.rtl.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap.rtl.css.map
++bootstrap.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap.rtl.min.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap.rtl.min.css
++bootstrap.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\bootstrap.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\content\bootstrap.rtl.min.css.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\bootstrap.rtl.min.css.map
++Site.css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\content\site.css
++review_5_638863919384320721.jpg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\reviewphotos\review_5_638863919384320721.jpg
++review_6_638863940113623522.jpg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\reviewphotos\review_6_638863940113623522.jpg
++review_7_638865827476393338.gif
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\images\reviewphotos\review_7_638865827476393338.gif
++Debug
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\debug\
++Release
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\release\
++Antlr.*******
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\antlr.*******\
++bootstrap.5.2.3
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\
++jQuery.3.7.0
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\
++Microsoft.AspNet.FriendlyUrls.1.0.2
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\
++Microsoft.AspNet.FriendlyUrls.Core.1.0.2
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\
++Microsoft.AspNet.FriendlyUrls.Core.zh-Hans.1.0.2
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\
++Microsoft.AspNet.ScriptManager.MSAjax.5.0.0
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\
++Microsoft.AspNet.ScriptManager.WebForms.5.0.0
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\
++Microsoft.AspNet.Web.Optimization.1.1.3
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.1.1.3\
++Microsoft.AspNet.Web.Optimization.WebForms.1.1.3
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.webforms.1.1.3\
++Microsoft.AspNet.Web.Optimization.zh-Hans.1.1.3
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.zh-hans.1.1.3\
++Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\
++Microsoft.Web.Infrastructure.2.0.0
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.web.infrastructure.2.0.0\
++Modernizr.2.8.3
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\modernizr.2.8.3\
++Newtonsoft.Json.13.0.3
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\
++WebGrease.1.6.0
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\webgrease.1.6.0\
++WebForms
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\webforms\
++bootstrap.bundle.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.bundle.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.bundle.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.bundle.js
++bootstrap.bundle.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.bundle.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.bundle.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.bundle.js.map
++bootstrap.bundle.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.bundle.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.bundle.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.bundle.min.js
++bootstrap.bundle.min.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.bundle.min.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.bundle.min.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.bundle.min.js.map
++bootstrap.esm.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.esm.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.esm.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.esm.js
++bootstrap.esm.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.esm.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.esm.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.esm.js.map
++bootstrap.esm.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.esm.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.esm.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.esm.min.js
++bootstrap.esm.min.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.esm.min.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.esm.min.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.esm.min.js.map
++bootstrap.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.js
++bootstrap.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.js.map
++bootstrap.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.min.js
++bootstrap.min.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\bootstrap.min.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\scripts\bootstrap.min.js.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\bootstrap.min.js.map
++jquery-3.7.0.intellisense.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\jquery-3.7.0.intellisense.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\tools\jquery-3.7.0.intellisense.js
++jquery-3.7.0.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\jquery-3.7.0.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\content\scripts\jquery-3.7.0.js
++jquery-3.7.0.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\jquery-3.7.0.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\content\scripts\jquery-3.7.0.min.js
++jquery-3.7.0.min.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\jquery-3.7.0.min.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\content\scripts\jquery-3.7.0.min.map
++jquery-3.7.0.slim.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\jquery-3.7.0.slim.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\content\scripts\jquery-3.7.0.slim.js
++jquery-3.7.0.slim.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\jquery-3.7.0.slim.min.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\content\scripts\jquery-3.7.0.slim.min.js
++jquery-3.7.0.slim.min.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\jquery-3.7.0.slim.min.map
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\content\scripts\jquery-3.7.0.slim.min.map
++modernizr-2.8.3.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\modernizr-2.8.3.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\modernizr.2.8.3\content\scripts\modernizr-2.8.3.js
++7f439b73-d16c-434f-8469-c16d45437703.gif
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\uploads\avatars\7f439b73-d16c-434f-8469-c16d45437703.gif
++About.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\about.aspx.cs
++About.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\about.aspx.designer.cs
++Contact.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\contact.aspx.cs
++Contact.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\contact.aspx.designer.cs
++Global.asax.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\global.asax.cs
++Login.aspx.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\login.aspx.cs
++Login.aspx.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\login.aspx.designer.cs
++MasterPage.master.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\masterpage.master.cs
++MasterPage.master.designer.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\masterpage.master.designer.cs
++Web.Debug.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\web.debug.config
++Web.Release.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\web.release.config
++csc.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\csc.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\csc.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\csc.exe
++csc.exe.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\csc.exe.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\csc.exe.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\csc.exe.config
++csc.rsp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\csc.rsp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\csc.rsp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\csc.rsp
++csi.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\csi.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\csi.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\csi.exe
++csi.exe.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\csi.exe.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\csi.exe.config
++csi.rsp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\csi.rsp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\csi.rsp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\csi.rsp
++Microsoft.Build.Tasks.CodeAnalysis.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.build.tasks.codeanalysis.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\microsoft.build.tasks.codeanalysis.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.build.tasks.codeanalysis.dll
++Microsoft.CodeAnalysis.CSharp.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.codeanalysis.csharp.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\microsoft.codeanalysis.csharp.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.codeanalysis.csharp.dll
++Microsoft.CodeAnalysis.CSharp.Scripting.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.codeanalysis.csharp.scripting.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\microsoft.codeanalysis.csharp.scripting.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.codeanalysis.csharp.scripting.dll
++Microsoft.CodeAnalysis.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.codeanalysis.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\microsoft.codeanalysis.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.codeanalysis.dll
++Microsoft.CodeAnalysis.Scripting.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.codeanalysis.scripting.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\microsoft.codeanalysis.scripting.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.codeanalysis.scripting.dll
++Microsoft.CodeAnalysis.VisualBasic.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.codeanalysis.visualbasic.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\microsoft.codeanalysis.visualbasic.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.codeanalysis.visualbasic.dll
++Microsoft.CSharp.Core.targets
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.csharp.core.targets
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\microsoft.csharp.core.targets
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.csharp.core.targets
++Microsoft.DiaSymReader.Native.amd64.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.diasymreader.native.amd64.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\microsoft.diasymreader.native.amd64.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.diasymreader.native.amd64.dll
++Microsoft.DiaSymReader.Native.x86.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.diasymreader.native.x86.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\microsoft.diasymreader.native.x86.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.diasymreader.native.x86.dll
++Microsoft.Managed.Core.targets
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.managed.core.targets
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.managed.core.targets
++Microsoft.VisualBasic.Core.targets
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.visualbasic.core.targets
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\microsoft.visualbasic.core.targets
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.visualbasic.core.targets
++Microsoft.Win32.Primitives.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\microsoft.win32.primitives.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\microsoft.win32.primitives.dll
++System.AppContext.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.appcontext.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\system.appcontext.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.appcontext.dll
++System.Collections.Immutable.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.collections.immutable.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\system.collections.immutable.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.collections.immutable.dll
++System.Console.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.console.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.console.dll
++System.Diagnostics.DiagnosticSource.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.diagnostics.diagnosticsource.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.diagnostics.diagnosticsource.dll
++System.Diagnostics.FileVersionInfo.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.diagnostics.fileversioninfo.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.diagnostics.fileversioninfo.dll
++System.Diagnostics.StackTrace.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.diagnostics.stacktrace.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\system.diagnostics.stacktrace.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.diagnostics.stacktrace.dll
++System.Globalization.Calendars.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.globalization.calendars.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.globalization.calendars.dll
++System.IO.Compression.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.io.compression.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.io.compression.dll
++System.IO.Compression.ZipFile.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.io.compression.zipfile.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.io.compression.zipfile.dll
++System.IO.FileSystem.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.io.filesystem.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\system.io.filesystem.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.io.filesystem.dll
++System.IO.FileSystem.Primitives.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.io.filesystem.primitives.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\system.io.filesystem.primitives.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.io.filesystem.primitives.dll
++System.Net.Http.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.net.http.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.net.http.dll
++System.Net.Sockets.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.net.sockets.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.net.sockets.dll
++System.Reflection.Metadata.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.reflection.metadata.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\system.reflection.metadata.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.reflection.metadata.dll
++System.Runtime.InteropServices.RuntimeInformation.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.runtime.interopservices.runtimeinformation.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.runtime.interopservices.runtimeinformation.dll
++System.Security.Cryptography.Algorithms.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.security.cryptography.algorithms.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.security.cryptography.algorithms.dll
++System.Security.Cryptography.Encoding.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.security.cryptography.encoding.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.security.cryptography.encoding.dll
++System.Security.Cryptography.Primitives.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.security.cryptography.primitives.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.security.cryptography.primitives.dll
++System.Security.Cryptography.X509Certificates.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.security.cryptography.x509certificates.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.security.cryptography.x509certificates.dll
++System.Text.Encoding.CodePages.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.text.encoding.codepages.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.text.encoding.codepages.dll
++System.Threading.Tasks.Extensions.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.threading.tasks.extensions.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.threading.tasks.extensions.dll
++System.ValueTuple.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.valuetuple.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.valuetuple.dll
++System.Xml.ReaderWriter.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.xml.readerwriter.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.xml.readerwriter.dll
++System.Xml.XmlDocument.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.xml.xmldocument.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.xml.xmldocument.dll
++System.Xml.XPath.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.xml.xpath.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.xml.xpath.dll
++System.Xml.XPath.XDocument.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\system.xml.xpath.xdocument.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\system.xml.xpath.xdocument.dll
++vbc.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\vbc.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\vbc.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\vbc.exe
++vbc.exe.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\vbc.exe.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\vbc.exe.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\vbc.exe.config
++vbc.rsp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\vbc.rsp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\vbc.rsp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\vbc.rsp
++VBCSCompiler.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\vbcscompiler.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\vbcscompiler.exe
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\vbcscompiler.exe
++VBCSCompiler.exe.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\roslyn\vbcscompiler.exe.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\vbcscompiler.exe.config
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\vbcscompiler.exe.config
++Microsoft.AspNet.FriendlyUrls.resources.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\zh-hans\microsoft.aspnet.friendlyurls.resources.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net40\zh-hans\microsoft.aspnet.friendlyurls.resources.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net45\zh-hans\microsoft.aspnet.friendlyurls.resources.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\lib\net40\zh-hans\microsoft.aspnet.friendlyurls.resources.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\lib\net45\zh-hans\microsoft.aspnet.friendlyurls.resources.dll
++System.Web.Optimization.resources.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\bin\zh-hans\system.web.optimization.resources.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.1.1.3\lib\net40\zh-hans\system.web.optimization.resources.dll
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.zh-hans.1.1.3\lib\net40\zh-hans\system.web.optimization.resources.dll
++TempPE
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\debug\temppe\
++.NETFramework,Version=v4.8.AssemblyAttributes.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\debug\.netframework,version=v4.8.assemblyattributes.cs
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\release\.netframework,version=v4.8.assemblyattributes.cs
++DesignTimeResolveAssemblyReferences.cache
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\debug\designtimeresolveassemblyreferences.cache
++DesignTimeResolveAssemblyReferencesInput.cache
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\debug\designtimeresolveassemblyreferencesinput.cache
++WebAppli.7D14BD69.Up2Date
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\debug\webappli.7d14bd69.up2date
++WebApplication1.csproj.AssemblyReference.cache
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\debug\webapplication1.csproj.assemblyreference.cache
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\release\webapplication1.csproj.assemblyreference.cache
++WebApplication1.csproj.CoreCompileInputs.cache
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\debug\webapplication1.csproj.corecompileinputs.cache
++WebApplication1.csproj.FileListAbsolute.txt
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\obj\debug\webapplication1.csproj.filelistabsolute.txt
++lib
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\antlr.*******\lib\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\lib\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\lib\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\lib\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.1.1.3\lib\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.webforms.1.1.3\lib\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.zh-hans.1.1.3\lib\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\lib\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.web.infrastructure.2.0.0\lib\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\webgrease.1.6.0\lib\
++.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\antlr.*******\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.1.1.3\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.webforms.1.1.3\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.zh-hans.1.1.3\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.web.infrastructure.2.0.0\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\modernizr.2.8.3\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\.signature.p7s
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\webgrease.1.6.0\.signature.p7s
++Antlr.*******.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\antlr.*******\antlr.*******.nupkg
++content
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\content\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.webforms.1.1.3\content\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\content\
++contentFiles
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\
++bootstrap.5.2.3.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\bootstrap.5.2.3.nupkg
++bootstrap.png
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\bootstrap.png
++Tools
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\tools\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\modernizr.2.8.3\tools\
++jQuery.3.7.0.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\jquery.3.7.0.nupkg
++Microsoft.AspNet.FriendlyUrls.1.0.2.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\microsoft.aspnet.friendlyurls.1.0.2.nupkg
++readme.txt
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\readme.txt
++Microsoft.AspNet.FriendlyUrls.Core.1.0.2.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\microsoft.aspnet.friendlyurls.core.1.0.2.nupkg
++Microsoft.AspNet.FriendlyUrls.Core.zh-Hans.1.0.2.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2.nupkg
++Microsoft.AspNet.ScriptManager.MSAjax.5.0.0.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\microsoft.aspnet.scriptmanager.msajax.5.0.0.nupkg
++Microsoft.AspNet.ScriptManager.WebForms.5.0.0.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\microsoft.aspnet.scriptmanager.webforms.5.0.0.nupkg
++Microsoft.AspNet.Web.Optimization.1.1.3.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.1.1.3\microsoft.aspnet.web.optimization.1.1.3.nupkg
++Microsoft.AspNet.Web.Optimization.WebForms.1.1.3.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.webforms.1.1.3\microsoft.aspnet.web.optimization.webforms.1.1.3.nupkg
++Microsoft.AspNet.Web.Optimization.zh-Hans.1.1.3.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.zh-hans.1.1.3\microsoft.aspnet.web.optimization.zh-hans.1.1.3.nupkg
++build
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\build\
++tools
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\webgrease.1.6.0\tools\
++Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1.nupkg
++Microsoft.Web.Infrastructure.2.0.0.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.web.infrastructure.2.0.0\microsoft.web.infrastructure.2.0.0.nupkg
++NET.icon.png
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.web.infrastructure.2.0.0\net.icon.png
++NET_Library_EULA_ENU.txt
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.web.infrastructure.2.0.0\net_library_eula_enu.txt
++Modernizr.2.8.3.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\modernizr.2.8.3\modernizr.2.8.3.nupkg
++LICENSE.md
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\license.md
++Newtonsoft.Json.13.0.3.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\newtonsoft.json.13.0.3.nupkg
++packageIcon.png
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\packageicon.png
++README.md
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\readme.md
++WebGrease.1.6.0.nupkg
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\webgrease.1.6.0\webgrease.1.6.0.nupkg
++MSAjax
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\
++MicrosoftAjax.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\microsoftajax.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\microsoftajax.js
++MicrosoftAjaxApplicationServices.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\microsoftajaxapplicationservices.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\microsoftajaxapplicationservices.js
++MicrosoftAjaxComponentModel.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\microsoftajaxcomponentmodel.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\microsoftajaxcomponentmodel.js
++MicrosoftAjaxCore.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\microsoftajaxcore.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\microsoftajaxcore.js
++MicrosoftAjaxGlobalization.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\microsoftajaxglobalization.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\microsoftajaxglobalization.js
++MicrosoftAjaxHistory.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\microsoftajaxhistory.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\microsoftajaxhistory.js
++MicrosoftAjaxNetwork.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\microsoftajaxnetwork.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\microsoftajaxnetwork.js
++MicrosoftAjaxSerialization.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\microsoftajaxserialization.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\microsoftajaxserialization.js
++MicrosoftAjaxTimer.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\microsoftajaxtimer.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\microsoftajaxtimer.js
++MicrosoftAjaxWebForms.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\microsoftajaxwebforms.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\microsoftajaxwebforms.js
++MicrosoftAjaxWebServices.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\msajax\microsoftajaxwebservices.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\content\scripts\webforms\msajax\microsoftajaxwebservices.js
++DetailsView.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\detailsview.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\webforms\detailsview.js
++Focus.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\focus.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\webforms\focus.js
++GridView.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\gridview.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\webforms\gridview.js
++Menu.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\menu.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\webforms\menu.js
++MenuStandards.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\menustandards.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\webforms\menustandards.js
++SmartNav.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\smartnav.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\webforms\smartnav.js
++TreeView.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\treeview.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\webforms\treeview.js
++WebForms.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\webforms.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\webforms\webforms.js
++WebParts.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\webparts.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\webforms\webparts.js
++WebUIValidation.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\scripts\webforms\webuivalidation.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\content\scripts\webforms\webuivalidation.js
++any
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\
++common.ps1
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\tools\common.ps1
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\modernizr.2.8.3\tools\common.ps1
++install.ps1
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\tools\install.ps1
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\modernizr.2.8.3\tools\install.ps1
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\net45\install.ps1
++uninstall.ps1
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\tools\uninstall.ps1
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\modernizr.2.8.3\tools\uninstall.ps1
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\net45\uninstall.ps1
++Site.Mobile.Master.cs.pp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\content\site.mobile.master.cs.pp
++Site.Mobile.Master.designer.cs.pp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\content\site.mobile.master.designer.cs.pp
++Site.Mobile.Master.pp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\content\site.mobile.master.pp
++ViewSwitcher.ascx.cs.pp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\content\viewswitcher.ascx.cs.pp
++ViewSwitcher.ascx.designer.cs.pp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\content\viewswitcher.ascx.designer.cs.pp
++ViewSwitcher.ascx.pp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\content\viewswitcher.ascx.pp
++net40
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net40\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\lib\net40\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.1.1.3\lib\net40\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.zh-hans.1.1.3\lib\net40\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.web.infrastructure.2.0.0\lib\net40\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net40\
++net45
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.1.0.2\lib\net45\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.core.zh-hans.1.0.2\lib\net45\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.msajax.5.0.0\lib\net45\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.scriptmanager.webforms.5.0.0\lib\net45\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.webforms.1.1.3\lib\net45\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\build\net45\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\content\net45\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\lib\net45\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\net45\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net45\
++web.config.transform
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.webforms.1.1.3\content\web.config.transform
++net46
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\build\net46\
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\content\net46\
++Roslyn45
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslyn45\
++RoslynLatest
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\tools\roslynlatest\
++net20
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net20\
++net35
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net35\
++net6.0
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\net6.0\
++netstandard1.0
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\netstandard1.0\
++netstandard1.3
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\netstandard1.3\
++netstandard2.0
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\newtonsoft.json.13.0.3\lib\netstandard2.0\
++WG.EXE
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\webgrease.1.6.0\tools\wg.exe
++jquery-3.7.0-vsdoc.js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\jquery.3.7.0\content\scripts\jquery-3.7.0-vsdoc.js
++RouteConfig.cs.pp
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.friendlyurls.1.0.2\content\app_start\routeconfig.cs.pp
++system.web.optimization.xml
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.aspnet.web.optimization.1.1.3\lib\net40\system.web.optimization.xml
++Microsoft.CodeDom.Providers.DotNetCompilerPlatform.Extensions.props
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\build\net45\microsoft.codedom.providers.dotnetcompilerplatform.extensions.props
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\build\net46\microsoft.codedom.providers.dotnetcompilerplatform.extensions.props
++Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\build\net45\microsoft.codedom.providers.dotnetcompilerplatform.props
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\build\net46\microsoft.codedom.providers.dotnetcompilerplatform.props
++app.config.install.xdt
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\content\net45\app.config.install.xdt
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\content\net46\app.config.install.xdt
++app.config.uninstall.xdt
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\content\net45\app.config.uninstall.xdt
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\content\net46\app.config.uninstall.xdt
++web.config.install.xdt
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\content\net45\web.config.install.xdt
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\content\net46\web.config.install.xdt
++web.config.uninstall.xdt
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\content\net45\web.config.uninstall.xdt
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\microsoft.codedom.providers.dotnetcompilerplatform.2.0.1\content\net46\web.config.uninstall.xdt
++wwwroot
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\
++css
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\css\
++js
i:{db9c54ea-e63e-4b12-b11c-7ba6a6883436}:d:\tryweb\webapplication1\packages\bootstrap.5.2.3\contentfiles\any\any\wwwroot\js\
