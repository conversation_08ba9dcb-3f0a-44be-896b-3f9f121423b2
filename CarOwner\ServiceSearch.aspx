<%@ Page Title="服务搜索" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="ServiceSearch.aspx.cs" Inherits="CarOwner_ServiceSearch" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .filter-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .service-card {
            transition: transform 0.3s;
            cursor: pointer;
            height: 100%;
        }
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .shop-rating {
            color: #ffc107;
        }
        .price-range-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .price-range-container input {
            width: 45%;
        }
        .range-separator {
            width: 10%;
            text-align: center;
        }
        .filter-badge {
            background-color: #e9ecef;
            color: #495057;
            padding: 5px 10px;
            border-radius: 20px;
            margin-right: 5px;
            margin-bottom: 5px;
            display: inline-block;
        }
        .filter-badge .close-btn {
            margin-left: 5px;
            font-weight: bold;
            cursor: pointer;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <!-- 添加ScriptManager控件 -->
    <asp:ScriptManager ID="ScriptManager1" runat="server" EnablePageMethods="true"></asp:ScriptManager>
    
    <div class="container">
        <h2 class="mb-4"><i class="fas fa-search"></i> 智能筛选服务</h2>
        
        <!-- 消息提示区 -->
        <asp:Panel ID="pnlMessage" runat="server" CssClass="alert mb-4" Visible="false">
            <asp:Literal ID="litMessage" runat="server"></asp:Literal>
        </asp:Panel>
        
        <div class="row">
            <!-- 筛选区 -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-filter"></i> 筛选条件</h5>
                    </div>
                    <div class="card-body">
                        <!-- 服务类型 -->
                        <div class="mb-3">
                            <label for="ddlServiceCategory" class="form-label">服务类型</label>
                            <asp:DropDownList ID="ddlServiceCategory" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlServiceCategory_SelectedIndexChanged">
                            </asp:DropDownList>
                        </div>
                        
                        <!-- 价格范围 -->
                        <div class="mb-3">
                            <label class="form-label">价格范围</label>
                            <div class="price-range-container">
                                <asp:TextBox ID="txtMinPrice" runat="server" CssClass="form-control" placeholder="最低" TextMode="Number"></asp:TextBox>
                                <span class="range-separator">至</span>
                                <asp:TextBox ID="txtMaxPrice" runat="server" CssClass="form-control" placeholder="最高" TextMode="Number"></asp:TextBox>
                            </div>
                        </div>
                        
                        <!-- 地址 -->
                        <div class="mb-3">
                            <label for="txtLocation" class="form-label">维修店地址</label>
                            <asp:TextBox ID="txtLocation" runat="server" CssClass="form-control" placeholder="输入地址关键词"></asp:TextBox>
                        </div>
                        
                        <!-- 评分星级 -->
                        <div class="mb-3">
                            <label class="form-label">最低评分星级</label>
                            <div>
                                <asp:RadioButtonList ID="rblRating" runat="server" RepeatDirection="Horizontal" CssClass="rating-options">
                                    <asp:ListItem Value="0" Selected="True">不限</asp:ListItem>
                                    <asp:ListItem Value="1">1星</asp:ListItem>
                                    <asp:ListItem Value="2">2星</asp:ListItem>
                                    <asp:ListItem Value="3">3星</asp:ListItem>
                                    <asp:ListItem Value="4">4星</asp:ListItem>
                                    <asp:ListItem Value="5">5星</asp:ListItem>
                                </asp:RadioButtonList>
                            </div>
                        </div>
                        
                        <!-- 筛选按钮 -->
                        <div class="d-grid gap-2">
                            <asp:Button ID="btnFilter" runat="server" CssClass="btn btn-primary" Text="应用筛选" OnClick="btnFilter_Click" />
                            <asp:Button ID="btnReset" runat="server" CssClass="btn btn-outline-secondary" Text="重置筛选" OnClick="btnReset_Click" />
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 结果区 -->
            <div class="col-lg-8">
                <!-- 当前筛选条件 -->
                <asp:Panel ID="pnlActiveFilters" runat="server" CssClass="mb-3" Visible="false">
                    <h6>当前筛选条件：</h6>
                    <div class="active-filters mb-3">
                        <asp:PlaceHolder ID="phActiveFilters" runat="server"></asp:PlaceHolder>
                    </div>
                </asp:Panel>
                
                <!-- 搜索结果统计 -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <asp:Label ID="lblResultCount" runat="server" CssClass="text-muted"></asp:Label>
                    <asp:DropDownList ID="ddlSortOrder" runat="server" CssClass="form-control form-control-sm w-auto" AutoPostBack="true" OnSelectedIndexChanged="ddlSortOrder_SelectedIndexChanged">
                        <asp:ListItem Value="price_asc">价格从低到高</asp:ListItem>
                        <asp:ListItem Value="price_desc">价格从高到低</asp:ListItem>
                        <asp:ListItem Value="rating_desc" Selected="True">评分从高到低</asp:ListItem>
                    </asp:DropDownList>
                </div>
                
                <!-- 搜索结果列表 -->
                <asp:ListView ID="lvServices" runat="server">
                    <LayoutTemplate>
                        <div class="row row-cols-1 g-4">
                            <asp:PlaceHolder ID="itemPlaceholder" runat="server"></asp:PlaceHolder>
                        </div>
                    </LayoutTemplate>
                    <ItemTemplate>
                        <div class="col">
                            <div class="card service-card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-9">
                                            <h5 class="card-title"><%# Eval("ServiceName") %></h5>
                                            <h6 class="card-subtitle mb-2 text-muted">
                                                <i class="fas fa-store"></i> <%# Eval("ShopName") %>
                                                <small class="ms-2 shop-rating">
                                                    <%# GetStarRating(Convert.ToDecimal(Eval("AvgRating"))) %>
                                                    (<%# String.Format("{0:0.0}", Eval("AvgRating")) %>)
                                                </small>
                                            </h6>
                                            <p class="card-text text-truncate"><%# Eval("Description") %></p>
                                            <div class="mb-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-map-marker-alt"></i> <%# Eval("Address") %>
                                                </small>
                                            </div>
                                            <div class="mb-2">
                                                <span class="badge bg-light text-dark"><%# Eval("CategoryName") %></span>
                                                <small class="ms-2 text-muted">
                                                    <i class="fas fa-clock"></i> 预计 <%# Eval("EstimatedTime") %> 分钟
                                                </small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 d-flex flex-column justify-content-between align-items-end">
                                            <h4 class="text-primary mb-3">¥<%# String.Format("{0:0.00}", Eval("BasePrice")) %></h4>
                                            <asp:LinkButton ID="lbtnBook" runat="server" CssClass="btn btn-sm btn-primary" 
                                                CommandArgument='<%# Eval("ServiceID") + "," + Eval("ShopID") %>'
                                                OnClick="lbtnBook_Click"
                                                OnClientClick="console.log('预约按钮被点击'); debugLog('预约按钮被点击'); return true;">
                                                <i class="fas fa-calendar-plus"></i> 预约服务
                                            </asp:LinkButton>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ItemTemplate>
                    <EmptyDataTemplate>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> 没有找到符合条件的服务，请尝试调整筛选条件。
                        </div>
                    </EmptyDataTemplate>
                </asp:ListView>
                
                <!-- 分页控件 -->
                <div class="mt-4 d-flex justify-content-center">
                    <asp:DataPager ID="dpServices" runat="server" PagedControlID="lvServices" PageSize="10">
                        <Fields>
                            <asp:NumericPagerField ButtonType="Link" ButtonCount="5" 
                                CurrentPageLabelCssClass="btn btn-primary" 
                                NumericButtonCssClass="btn btn-outline-secondary"
                                NextPageText="下一页 &raquo;" 
                                PreviousPageText="&laquo; 上一页" />
                        </Fields>
                    </asp:DataPager>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建预约模态框 -->
    <div class="modal fade" id="bookServiceModal" tabindex="-1" aria-labelledby="bookServiceModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="bookServiceModalLabel">预约服务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                    </div>
                    <div class="modal-body">
                        <!-- 模态框内容由后端代码填充 -->
                        <asp:UpdatePanel ID="upBooking" runat="server" UpdateMode="Conditional">
                            <Triggers>
                                <asp:AsyncPostBackTrigger ControlID="btnModalSubmit" EventName="Click" />
                            </Triggers>
                            <ContentTemplate>
                            <asp:Label ID="lblModalServiceInfo" runat="server"></asp:Label>
                            
                            <div class="mb-3">
                                <label for="ddlModalCar" class="form-label">选择车辆</label>
                                <asp:DropDownList ID="ddlModalCar" runat="server" CssClass="form-select"></asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvModalCar" runat="server" ControlToValidate="ddlModalCar"
                                    InitialValue="0" ErrorMessage="请选择车辆" Display="Dynamic"
                                    CssClass="text-danger" ValidationGroup="BookingModal"></asp:RequiredFieldValidator>
                            </div>
                            
                            <div class="mb-3">
                                <label for="txtModalAppointmentDate" class="form-label">预约时间</label>
                                <asp:TextBox ID="txtModalAppointmentDate" runat="server" CssClass="form-control" TextMode="DateTimeLocal"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvModalDate" runat="server" ControlToValidate="txtModalAppointmentDate"
                                    ErrorMessage="请选择预约时间" Display="Dynamic"
                                    CssClass="text-danger" ValidationGroup="BookingModal"></asp:RequiredFieldValidator>
                                <asp:CustomValidator ID="cvModalDate" runat="server" ControlToValidate="txtModalAppointmentDate"
                                    ErrorMessage="预约时间必须是将来的时间" Display="Dynamic"
                                    CssClass="text-danger" ValidationGroup="BookingModal"
                                    OnServerValidate="cvModalAppointmentDate_ServerValidate"></asp:CustomValidator>
                            </div>
                            
                            <div class="mb-3">
                                <label for="txtModalDescription" class="form-label">问题描述</label>
                                <asp:TextBox ID="txtModalDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" placeholder="请描述您车辆的问题"></asp:TextBox>
                            </div>

                            <!-- 错误信息显示区 -->
                            <div class="alert alert-danger" id="modalErrorMessage" runat="server" visible="false">
                                <asp:Literal ID="litModalError" runat="server"></asp:Literal>
                            </div>
                            
                            <asp:HiddenField ID="hfServiceID" runat="server" />
                            <asp:HiddenField ID="hfShopID" runat="server" />
                            
                            <!-- 将提交按钮移到UpdatePanel内部 -->
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <asp:Button ID="btnModalSubmit" runat="server" CssClass="btn btn-primary" Text="提交预约" 
                                    OnClick="btnModalSubmit_Click" ValidationGroup="BookingModal" />
                            </div>
                        </ContentTemplate>
                    </asp:UpdatePanel>
                </div>
                <!-- 移除调试控制台 -->
            </div>
        </div>
    </div>

    <script type="text/javascript">
        // 简化版调试函数 - 只在控制台输出，不显示在页面上
        function debugLog(message) {
            // 在生产环境中禁用调试日志
            // console.log(message);
        }
    
        $(document).ready(function () {
            // 设置预约时间的最小值为当前时间
            function setMinDateTime() {
                var now = new Date();
                var year = now.getFullYear();
                var month = (now.getMonth() + 1).toString().padStart(2, '0');
                var day = now.getDate().toString().padStart(2, '0');
                var hours = now.getHours().toString().padStart(2, '0');
                var minutes = now.getMinutes().toString().padStart(2, '0');
                
                var minDateTime = year + '-' + month + '-' + day + 'T' + hours + ':' + minutes;
                $("[id$='txtModalAppointmentDate']").attr('min', minDateTime);
            }
            
            // 页面加载完成和预约模态框显示时设置最小时间
            setMinDateTime();

            // 监听模态框显示事件 - 使用Bootstrap 5语法
            var bookServiceModal = document.getElementById('bookServiceModal');
            if (bookServiceModal) {
                bookServiceModal.addEventListener('shown.bs.modal', function () {
                setMinDateTime();
            });
            }
            
            // 监听ASP.NET UpdatePanel异步回发完成事件
            if (typeof(Sys) !== 'undefined' && typeof(Sys.WebForms) !== 'undefined') {
                var prm = Sys.WebForms.PageRequestManager.getInstance();
                
                prm.add_endRequest(function (sender, args) {
                    setMinDateTime();
                    
                    // 检查是否有错误
                    if (args.get_error() !== undefined && args.get_error() !== null) {
                        args.set_errorHandled(true); // 标记为已处理
                    }
                    
                    // 如果模态框应该显示，确保它保持显示状态
                    if (window.shouldShowModal) {
                        var modal = new bootstrap.Modal(document.getElementById('bookServiceModal'));
                        modal.show();
                        window.shouldShowModal = false;
                    }
                });
                
                // 添加开始请求事件处理
                prm.add_beginRequest(function(sender, args) {
                    // 禁用提交按钮，防止重复提交
                    var button = args.get_postBackElement();
                    if (button && button.id === '<%= btnModalSubmit.ClientID %>') {
                        button.disabled = true;
                        button.value = "处理中...";
                    }
                });
                
                // 监听初始化完成
                prm.add_initializeRequest(function(sender, args) {
                    // 检查是否有正在进行的请求
                    if (prm.get_isInAsyncPostBack()) {
                        args.set_cancel(true);
                    }
                });
            }
        });

        // 显示预约模态框的函数 - 使用Bootstrap 5语法
        function showBookingModal() {
            var bookServiceModal = document.getElementById('bookServiceModal');
            if (bookServiceModal) {
                var modal = new bootstrap.Modal(bookServiceModal);
                modal.show();
            window.shouldShowModal = true;
            }
        }
    </script>
</asp:Content> 








