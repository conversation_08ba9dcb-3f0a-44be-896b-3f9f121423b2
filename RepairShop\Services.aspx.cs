using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class RepairShop_Services : System.Web.UI.Page
{
    private int userID;
    private int shopID;
    private int? editingServiceID = null;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取当前用户信息
        if (Session["UserID"] == null || Session["UserType"] == null)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        string userType = Session["UserType"].ToString();

        if (userType != "RepairShop")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取维修店ID
        shopID = ShopManager.GetShopIDByUserID(userID);
        if (shopID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 从ViewState恢复编辑ID
        if (ViewState["EditingServiceID"] != null)
        {
            editingServiceID = Convert.ToInt32(ViewState["EditingServiceID"]);
        }

        if (!IsPostBack)
        {
            // 加载服务类别
            LoadServiceCategories();
            // 加载服务列表
            LoadServices();
        }
    }

    /// <summary>
    /// 加载服务类别
    /// </summary>
    private void LoadServiceCategories()
    {
        DataTable categories = ShopManager.GetServiceCategories();
        ddlCategory.DataSource = categories;
        ddlCategory.DataBind();
        
        // 添加默认选项
        if (categories.Rows.Count > 0)
        {
            ddlCategory.Items.Insert(0, new ListItem("-- 请选择服务类别 --", ""));
            ddlCategory.SelectedIndex = 0;
        }
    }

    /// <summary>
    /// 加载服务列表
    /// </summary>
    private void LoadServices()
    {
        DataTable services = ShopManager.GetShopServices(shopID);
        gvServices.DataSource = services;
        gvServices.DataBind();
    }

    /// <summary>
    /// GridView行命令事件
    /// </summary>
    protected void gvServices_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        int serviceID = Convert.ToInt32(e.CommandArgument);

        if (e.CommandName == "EditService")
        {
            // 加载服务信息进行编辑
            EditService(serviceID);
        }
        else if (e.CommandName == "ToggleStatus")
        {
            // 获取当前服务信息
            DataTable serviceTable = ShopManager.GetShopServices(shopID);
            DataRow[] rows = serviceTable.Select("ServiceID = " + serviceID);
            
            if (rows.Length > 0)
            {
                DataRow row = rows[0];
                bool currentStatus = (bool)row["IsActive"];
                
                // 切换状态
                bool newStatus = !currentStatus;
                
                // 调用修改方法更新服务状态
                bool success = ShopManager.UpdateService(
                    serviceID,
                    Convert.ToInt32(row["CategoryID"]),
                    row["ServiceName"].ToString(),
                    row["Description"] != DBNull.Value ? row["Description"].ToString() : null,
                    Convert.ToInt32(row["EstimatedTime"]),
                    Convert.ToDecimal(row["BasePrice"]),
                    newStatus
                );
                
                if (success)
                {
                    // 更新成功，重新加载服务列表
                    LoadServices();
                    lblMessage.Text = string.Format("服务\"{0}\"已{1}。",
                             row["ServiceName"],
                             newStatus ? "启用" : "停用");
                }
                else
                {
                    lblMessage.Text = "操作失败，请稍后再试。";
                }
            }
        }
        else if (e.CommandName == "DeleteService")
        {
            // 删除服务
            bool success = ShopManager.DeleteService(serviceID);
            if (success)
            {
                LoadServices();
                lblMessage.Text = "服务已成功删除。";
            }
            else
            {
                lblMessage.Text = "删除失败，请稍后再试。";
            }
        }
    }

    /// <summary>
    /// 编辑服务
    /// </summary>
    private void EditService(int serviceID)
    {
        // 获取服务信息
        DataTable serviceTable = ShopManager.GetShopServices(shopID);
        DataRow[] rows = serviceTable.Select("ServiceID = " + serviceID);
        
        if (rows.Length > 0)
        {
            DataRow row = rows[0];
            
            // 保存当前编辑的服务ID
            editingServiceID = serviceID;
            ViewState["EditingServiceID"] = serviceID;
            
            // 设置表单标题
            litFormTitle.Text = "<i class=\"fas fa-edit\"></i> 编辑服务";
            
            // 填充表单数据
            ddlCategory.SelectedValue = row["CategoryID"].ToString();
            txtServiceName.Text = row["ServiceName"].ToString();
            txtDescription.Text = row["Description"] != DBNull.Value ? row["Description"].ToString() : string.Empty;
            txtEstimatedTime.Text = row["EstimatedTime"].ToString();
            txtBasePrice.Text = Convert.ToDecimal(row["BasePrice"]).ToString("F2");
            chkIsActive.Checked = (bool)row["IsActive"];
            
            // 显示编辑面板
            pnlAddEdit.Visible = true;
        }
    }

    /// <summary>
    /// 添加服务按钮点击事件
    /// </summary>
    protected void btnAddService_Click(object sender, EventArgs e)
    {
        // 清空当前编辑的服务ID
        editingServiceID = null;
        ViewState.Remove("EditingServiceID");
        
        // 设置表单标题
        litFormTitle.Text = "<i class=\"fas fa-plus\"></i> 添加新服务";
        
        // 清空表单数据
        ddlCategory.SelectedIndex = 0;
        txtServiceName.Text = string.Empty;
        txtDescription.Text = string.Empty;
        txtEstimatedTime.Text = "30";
        txtBasePrice.Text = "0.00";
        chkIsActive.Checked = true;
        
        // 显示添加面板
        pnlAddEdit.Visible = true;
    }

    /// <summary>
    /// 保存服务按钮点击事件
    /// </summary>
    protected void btnSave_Click(object sender, EventArgs e)
    {
        if (!Page.IsValid)
        {
            return;
        }

        // 获取表单数据
        int categoryID = Convert.ToInt32(ddlCategory.SelectedValue);
        string serviceName = txtServiceName.Text.Trim();
        string description = txtDescription.Text.Trim();
        int estimatedTime = Convert.ToInt32(txtEstimatedTime.Text);
        decimal basePrice = Convert.ToDecimal(txtBasePrice.Text);
        bool isActive = chkIsActive.Checked;

        bool success;
        if (editingServiceID.HasValue)
        {
            // 更新现有服务
            success = ShopManager.UpdateService(editingServiceID.Value, categoryID, serviceName, description, estimatedTime, basePrice, isActive);
            
            if (success)
            {
                pnlAddEdit.Visible = false;
                // 清空编辑ID
                editingServiceID = null;
                ViewState.Remove("EditingServiceID");
                LoadServices();
                lblMessage.Text = "服务已成功更新。";
            }
            else
            {
                lblMessage.Text = "更新失败，请稍后再试。";
            }
        }
        else
        {
            // 添加新服务
            int newServiceID = ShopManager.AddService(shopID, categoryID, serviceName, description, estimatedTime, basePrice);
            
            if (newServiceID > 0)
            {
                pnlAddEdit.Visible = false;
                LoadServices();
                lblMessage.Text = "新服务已成功添加。";
            }
            else
            {
                lblMessage.Text = "添加失败，请稍后再试。";
            }
        }
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    protected void btnCancel_Click(object sender, EventArgs e)
    {
        // 隐藏添加/编辑面板
        pnlAddEdit.Visible = false;
        
        // 清空错误信息
        lblMessage.Text = string.Empty;
        
        // 清空编辑ID
        editingServiceID = null;
        ViewState.Remove("EditingServiceID");
    }
} 