using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Web;

/// <summary>
/// 照片管理类，用于处理评价照片的上传和查询
/// </summary>
public class PhotoManager
{
    // 允许的图片类型
    private static readonly string[] AllowedFileTypes = { "image/jpeg", "image/png", "image/gif" };
    
    // 最大文件大小（单位：字节）- 5MB
    private static readonly int MaxFileSize = 5 * 1024 * 1024;
    
    // 每个评价最多上传的照片数
    private static readonly int MaxPhotosPerReview = 3;
    
    // 照片保存的目录
    private static readonly string PhotosDirectory = "~/Images/ReviewPhotos/";

    /// <summary>
    /// 保存评价照片
    /// </summary>
    /// <param name="reviewID">评价ID</param>
    /// <param name="files">上传的文件集合</param>
    /// <returns>成功上传的照片数量</returns>
    public static int SaveReviewPhotos(int reviewID, HttpFileCollection files)
    {
        int successCount = 0;
        
        // 检查评价ID的有效性
        if (reviewID <= 0)
        {
            System.Diagnostics.Debug.WriteLine($"无效的评价ID: {reviewID}");
            return 0;
        }
        
        // 获取已上传的照片数量
        int existingPhotosCount = GetReviewPhotosCount(reviewID);
        int remainingSlots = MaxPhotosPerReview - existingPhotosCount;
        
        if (remainingSlots <= 0)
        {
            System.Diagnostics.Debug.WriteLine($"评价ID {reviewID} 已达到最大照片数量 {MaxPhotosPerReview}");
            return 0;
        }

        // 确保保存目录存在
        string physicalPath = HttpContext.Current.Server.MapPath(PhotosDirectory);
        if (!Directory.Exists(physicalPath))
        {
            Directory.CreateDirectory(physicalPath);
        }

        // 处理上传的文件
        List<string> uploadedFiles = new List<string>();
        
        for (int i = 0; i < files.Count && successCount < remainingSlots; i++)
        {
            HttpPostedFile file = files[i];
            
            // 检查文件是否有效
            if (file == null || file.ContentLength == 0)
            {
                continue;
            }
            
            // 验证文件类型
            if (!IsValidFileType(file.ContentType))
            {
                System.Diagnostics.Debug.WriteLine($"文件类型不允许: {file.ContentType}");
                continue;
            }
            
            // 验证文件大小
            if (file.ContentLength > MaxFileSize)
            {
                System.Diagnostics.Debug.WriteLine($"文件太大: {file.ContentLength} 字节, 最大允许: {MaxFileSize} 字节");
                continue;
            }
            
            try
            {
                // 生成唯一文件名
                string fileExtension = Path.GetExtension(file.FileName);
                string uniqueFileName = $"review_{reviewID}_{DateTime.Now.Ticks}{fileExtension}";
                string filePath = Path.Combine(physicalPath, uniqueFileName);
                
                // 保存文件
                file.SaveAs(filePath);
                
                // 将文件信息保存到数据库
                if (SavePhotoToDatabase(reviewID, file.FileName, uniqueFileName))
                {
                    uploadedFiles.Add(uniqueFileName);
                    successCount++;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存照片时出错: {ex.Message}");
            }
        }
        
        System.Diagnostics.Debug.WriteLine($"成功上传 {successCount} 张照片，评价ID: {reviewID}");
        return successCount;
    }

    /// <summary>
    /// 将照片信息保存到数据库
    /// </summary>
    /// <param name="reviewID">评价ID</param>
    /// <param name="originalFileName">原始文件名</param>
    /// <param name="savedFileName">保存的文件名</param>
    /// <returns>保存结果</returns>
    private static bool SavePhotoToDatabase(int reviewID, string originalFileName, string savedFileName)
    {
        try
        {
            string query = @"INSERT INTO ReviewPhotos (ReviewID, FileName, FilePath, UploadDate) 
                            VALUES (@ReviewID, @FileName, @FilePath, GETDATE());
                            SELECT SCOPE_IDENTITY()";

            SqlParameter[] parameters =
            {
                new SqlParameter("@ReviewID", reviewID),
                new SqlParameter("@FileName", originalFileName),
                new SqlParameter("@FilePath", PhotosDirectory + savedFileName)
            };

            object result = DatabaseHelper.ExecuteScalar(query, parameters);
            return (result != null && result != DBNull.Value);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"保存照片信息到数据库时出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 获取评价的照片数量
    /// </summary>
    /// <param name="reviewID">评价ID</param>
    /// <returns>照片数量</returns>
    public static int GetReviewPhotosCount(int reviewID)
    {
        try
        {
            string query = "SELECT COUNT(1) FROM ReviewPhotos WHERE ReviewID = @ReviewID";
            SqlParameter parameter = new SqlParameter("@ReviewID", reviewID);
            object result = DatabaseHelper.ExecuteScalar(query, parameter);
            return (result != null && result != DBNull.Value) ? Convert.ToInt32(result) : 0;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// 获取评价的照片列表
    /// </summary>
    /// <param name="reviewID">评价ID</param>
    /// <returns>照片数据表</returns>
    public static DataTable GetReviewPhotos(int reviewID)
    {
        try
        {
            string query = @"SELECT PhotoID, FileName, FilePath, UploadDate 
                           FROM ReviewPhotos 
                           WHERE ReviewID = @ReviewID 
                           ORDER BY UploadDate";
            
            SqlParameter parameter = new SqlParameter("@ReviewID", reviewID);
            return DatabaseHelper.ExecuteQuery(query, parameter);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取评价照片时出错: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 检查文件类型是否有效
    /// </summary>
    /// <param name="contentType">文件内容类型</param>
    /// <returns>是否是允许的文件类型</returns>
    private static bool IsValidFileType(string contentType)
    {
        foreach (string allowedType in AllowedFileTypes)
        {
            if (contentType.ToLower() == allowedType.ToLower())
            {
                return true;
            }
        }
        return false;
    }
} 