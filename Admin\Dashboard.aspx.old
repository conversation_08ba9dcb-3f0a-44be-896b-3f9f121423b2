<%@ Page Title="管理员控制台" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="Admin_Dashboard" Codebehind="Dashboard.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-tachometer-alt"></i> 管理员控制台</h2>
                <hr />
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><i class="fas fa-users"></i> 总用户数</h5>
                                <h2><asp:Label ID="lblTotalUsers" runat="server" Text="0"></asp:Label></h2>
                            </div>
                            <div>
                                <i class="fas fa-users fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between bg-primary">
                        <a class="small text-white stretched-link" href="UserManagement.aspx">查看详情</a>
                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><i class="fas fa-store"></i> 维修店数</h5>
                                <h2><asp:Label ID="lblTotalShops" runat="server" Text="0"></asp:Label></h2>
                            </div>
                            <div>
                                <i class="fas fa-store fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between bg-success">
                        <a class="small text-white stretched-link" href="ShopManagement.aspx">查看详情</a>
                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><i class="fas fa-car"></i> 车主数</h5>
                                <h2><asp:Label ID="lblTotalCarOwners" runat="server" Text="0"></asp:Label></h2>
                            </div>
                            <div>
                                <i class="fas fa-car fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between bg-warning">
                        <a class="small text-dark stretched-link" href="UserManagement.aspx?type=CarOwner">查看详情</a>
                        <div class="small text-dark"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><i class="fas fa-calendar-check"></i> 预约数</h5>
                                <h2><asp:Label ID="lblTotalAppointments" runat="server" Text="0"></asp:Label></h2>
                            </div>
                            <div>
                                <i class="fas fa-calendar-check fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between bg-info">
                        <a class="small text-white stretched-link" href="SystemSettings.aspx">系统设置</a>
                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-line"></i> 系统统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="small text-muted mb-1">总车辆数</div>
                                <div class="h4"><asp:Label ID="lblTotalCars" runat="server" Text="0"></asp:Label></div>
                            </div>
                            <div class="col-md-6">
                                <div class="small text-muted mb-1">总服务项目数</div>
                                <div class="h4"><asp:Label ID="lblTotalServices" runat="server" Text="0"></asp:Label></div>
                            </div>
                        </div>
                        <hr />
                        <div class="row">
                            <div class="col-md-6">
                                <div class="small text-muted mb-1">已完成预约</div>
                                <div class="h4 text-success"><asp:Label ID="lblCompletedAppointments" runat="server" Text="0"></asp:Label></div>
                            </div>
                            <div class="col-md-6">
                                <div class="small text-muted mb-1">总评价数</div>
                                <div class="h4"><asp:Label ID="lblTotalReviews" runat="server" Text="0"></asp:Label></div>
                            </div>
                        </div>
                        <hr />
                        <div class="row">
                            <div class="col-md-6">
                                <div class="small text-muted mb-1">平均评分</div>
                                <div class="h4 text-warning">
                                    <asp:Label ID="lblAverageRating" runat="server" Text="0.0"></asp:Label>
                                    <small>/5</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="small text-muted mb-1">管理员数</div>
                                <div class="h4"><asp:Label ID="lblTotalAdmins" runat="server" Text="0"></asp:Label></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-bell"></i> 系统通知</h5>
                            <asp:LinkButton ID="lbtnAddNotification" runat="server" CssClass="btn btn-sm btn-light" OnClick="lbtnAddNotification_Click">
                                <i class="fas fa-plus"></i> 添加通知
                            </asp:LinkButton>
                        </div>
                    </div>
                    <div class="card-body">
                        <asp:Panel ID="pnlNotifications" runat="server">
                            <asp:Repeater ID="rptNotifications" runat="server" OnItemCommand="rptNotifications_ItemCommand">
                                <ItemTemplate>
                                    <div class="card mb-2 <%# (bool)Eval("IsActive") ? "border-success" : "border-secondary" %>">
                                        <div class="card-header py-2 <%# (bool)Eval("IsActive") ? "bg-success text-white" : "bg-light" %>">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0"><%# Eval("Title") %></h6>
                                                <div>
                                                    <small><%# Convert.ToDateTime(Eval("CreatedDate")).ToString("yyyy-MM-dd") %></small>
                                                    <asp:LinkButton ID="lbtnEdit" runat="server" CssClass="btn btn-sm btn-light ml-2" 
                                                        CommandName="EditNotification" CommandArgument='<%# Eval("NotificationID") %>'>
                                                        <i class="fas fa-edit"></i>
                                                    </asp:LinkButton>
                                                    <asp:LinkButton ID="lbtnDelete" runat="server" CssClass="btn btn-sm btn-danger ml-1" 
                                                        CommandName="DeleteNotification" CommandArgument='<%# Eval("NotificationID") %>'
                                                        OnClientClick="return confirm('确定要删除此通知吗？');">
                                                        <i class="fas fa-trash"></i>
                                                    </asp:LinkButton>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body py-2">
                                            <p class="card-text"><%# Eval("Content") %></p>
                                            <span class="badge <%# (bool)Eval("IsActive") ? "badge-success" : "badge-secondary" %>">
                                                <%# (bool)Eval("IsActive") ? "已激活" : "未激活" %>
                                            </span>
                                        </div>
                                    </div>
                                </ItemTemplate>
                                <FooterTemplate>
                                    <% if (rptNotifications.Items.Count == 0) { %>
                                        <div class="alert alert-info">
                                            暂无系统通知
                                        </div>
                                    <% } %>
                                </FooterTemplate>
                            </asp:Repeater>
                        </asp:Panel>

                        <asp:Panel ID="pnlEditNotification" runat="server" Visible="false">
                            <div class="form-group">
                                <label for="txtNotificationTitle">通知标题：</label>
                                <asp:TextBox ID="txtNotificationTitle" runat="server" CssClass="form-control"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvNotificationTitle" runat="server" ControlToValidate="txtNotificationTitle"
                                    ErrorMessage="通知标题不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="Notification"></asp:RequiredFieldValidator>
                            </div>
                            <div class="form-group">
                                <label for="txtNotificationContent">通知内容：</label>
                                <asp:TextBox ID="txtNotificationContent" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="4"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvNotificationContent" runat="server" ControlToValidate="txtNotificationContent"
                                    ErrorMessage="通知内容不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="Notification"></asp:RequiredFieldValidator>
                            </div>
                            <div class="form-group">
                                <div class="form-check">
                                    <asp:CheckBox ID="chkNotificationActive" runat="server" Checked="true" />
                                    <label class="form-check-label" for="chkNotificationActive">
                                        激活通知
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <asp:Button ID="btnSaveNotification" runat="server" Text="保存" CssClass="btn btn-primary" ValidationGroup="Notification" OnClick="btnSaveNotification_Click" />
                                <asp:Button ID="btnCancelNotification" runat="server" Text="取消" CssClass="btn btn-secondary ml-2" OnClick="btnCancelNotification_Click" CausesValidation="false" />
                                <asp:HiddenField ID="hfNotificationID" runat="server" Value="0" />
                            </div>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-tasks"></i> 快捷操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="UserManagement.aspx" class="btn btn-outline-primary btn-block">
                                    <i class="fas fa-users fa-2x mb-2"></i><br />
                                    用户管理
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="ShopManagement.aspx" class="btn btn-outline-success btn-block">
                                    <i class="fas fa-store fa-2x mb-2"></i><br />
                                    维修店管理
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="CategoryManagement.aspx" class="btn btn-outline-info btn-block">
                                    <i class="fas fa-tags fa-2x mb-2"></i><br />
                                    服务类别管理
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="SystemSettings.aspx" class="btn btn-outline-dark btn-block">
                                    <i class="fas fa-cogs fa-2x mb-2"></i><br />
                                    系统设置
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
            </div>
        </div>
    </div>
</asp:Content>
