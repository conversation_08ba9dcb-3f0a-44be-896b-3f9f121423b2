using System;
using System.Collections.Generic;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebApplication1.Admin
{
    public partial class ServiceRecordManagement : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // 检查用户是否为管理员
            if (!User.Identity.IsAuthenticated || Session["UserType"] == null || Session["UserType"].ToString() != "Admin")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                // 加载维修记录列表
                LoadServiceRecords();
                
                // 设置默认完成日期为当前时间
                txtNewCompletedDate.Text = DateTime.Now.ToString("yyyy-MM-ddTHH:mm");
                
                // 加载待确认的预约
                LoadPendingAppointments();
            }
        }

        #region 维修记录列表和搜索

        /// <summary>
        /// 加载维修记录列表
        /// </summary>
        private void LoadServiceRecords()
        {
            try
            {
                // 获取搜索参数
                DateTime? startDate = null;
                DateTime? endDate = null;
                decimal? minCost = null;
                decimal? maxCost = null;
                string searchText = txtSearch.Text.Trim();

                if (!string.IsNullOrEmpty(txtStartDate.Text))
                {
                    startDate = DateTime.Parse(txtStartDate.Text);
                }

                if (!string.IsNullOrEmpty(txtEndDate.Text))
                {
                    endDate = DateTime.Parse(txtEndDate.Text + " 23:59:59");
                }

                if (!string.IsNullOrEmpty(txtMinCost.Text))
                {
                    minCost = decimal.Parse(txtMinCost.Text);
                }

                if (!string.IsNullOrEmpty(txtMaxCost.Text))
                {
                    maxCost = decimal.Parse(txtMaxCost.Text);
                }

                // 搜索维修记录
                DataTable records;
                if (!string.IsNullOrEmpty(searchText) || startDate != null || endDate != null || minCost != null || maxCost != null)
                {
                    records = ShopManager.SearchServiceRecords(startDate, endDate, searchText, searchText, minCost, maxCost);
                }
                else
                {
                    // 获取全部维修记录
                    int pageIndex = gvRecords.PageIndex;
                    int pageSize = gvRecords.PageSize;
                    records = ShopManager.GetAllServiceRecordsPaged(pageIndex, pageSize);
                }

                // 绑定到GridView
                gvRecords.DataSource = records;
                gvRecords.DataBind();

                // 更新总数
                lblTotalCount.Text = ShopManager.GetServiceRecordsCount().ToString();

                // 如果没有找到记录，显示提示
                if (records == null || records.Rows.Count == 0)
                {
                    ShowMessage("没有找到符合条件的维修记录。", "info");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("加载维修记录时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        protected void btnSearch_Click(object sender, EventArgs e)
        {
            gvRecords.PageIndex = 0; // 重置到第一页
            LoadServiceRecords();
        }

        /// <summary>
        /// 重置搜索按钮点击事件
        /// </summary>
        protected void btnReset_Click(object sender, EventArgs e)
        {
            txtStartDate.Text = string.Empty;
            txtEndDate.Text = string.Empty;
            txtMinCost.Text = string.Empty;
            txtMaxCost.Text = string.Empty;
            txtSearch.Text = string.Empty;
            gvRecords.PageIndex = 0;
            LoadServiceRecords();
        }

        /// <summary>
        /// GridView分页事件
        /// </summary>
        protected void gvRecords_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            gvRecords.PageIndex = e.NewPageIndex;
            LoadServiceRecords();
        }

        #endregion

        #region GridView事件处理

        /// <summary>
        /// GridView行命令事件处理
        /// </summary>
        protected void gvRecords_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "ViewRecord" || e.CommandName == "EditRecord")
                {
                    int recordID = Convert.ToInt32(e.CommandArgument);
                    ShowRecordDetails(recordID, e.CommandName == "EditRecord");
                }
                else if (e.CommandName == "DeleteRecord")
                {
                    int recordID = Convert.ToInt32(e.CommandArgument);
                    DeleteServiceRecord(recordID);
                }
            }
            catch (Exception ex)
            {
                ShowMessage("处理命令时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// GridView行数据绑定事件
        /// </summary>
        protected void gvRecords_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                // 可以在这里添加行数据绑定逻辑
            }
        }

        #endregion

        #region 查看和编辑维修记录

        /// <summary>
        /// 显示维修记录详情
        /// </summary>
        private void ShowRecordDetails(int recordID, bool isEditMode = false)
        {
            try
            {
                DataTable recordData = ShopManager.GetServiceRecordByID(recordID);
                if (recordData != null && recordData.Rows.Count > 0)
                {
                    DataRow row = recordData.Rows[0];

                    // 保存记录ID和预约ID
                    hfRecordID.Value = recordID.ToString();
                    hfAppointmentID.Value = row["AppointmentID"].ToString();

                    // 设置表单标题
                    litFormTitle.Text = isEditMode ? "编辑维修记录" : "维修记录详情";

                    // 显示基本信息
                    lblCarInfo.Text = row["Make"].ToString() + " " + row["Model"].ToString() + " (" + row["LicensePlate"].ToString() + ")";
                    lblOwnerName.Text = row["OwnerName"].ToString();
                    lblShopName.Text = row["ShopName"].ToString();
                    lblServiceName.Text = row["ServiceName"].ToString();
                    lblAppointmentDate.Text = Convert.ToDateTime(row["AppointmentDate"]).ToString("yyyy-MM-dd HH:mm");
                    
                    DateTime completedDate = Convert.ToDateTime(row["CompletedDate"]);
                    lblCompletedDate.Text = completedDate.ToString("yyyy-MM-dd HH:mm");
                    txtCompletedDate.Text = completedDate.ToString("yyyy-MM-ddTHH:mm");
                    
                    lblDiagnosisDetails.Text = row["DiagnosisDetails"] != DBNull.Value ? row["DiagnosisDetails"].ToString() : "";
                    txtDiagnosisDetails.Text = lblDiagnosisDetails.Text;
                    
                    lblPartsReplaced.Text = row["PartsReplaced"] != DBNull.Value ? row["PartsReplaced"].ToString() : "";
                    txtPartsReplaced.Text = lblPartsReplaced.Text;
                    
                    decimal laborCost = Convert.ToDecimal(row["LaborCost"]);
                    lblLaborCost.Text = laborCost.ToString("N2");
                    txtLaborCost.Text = laborCost.ToString("N2");
                    
                    decimal partsCost = Convert.ToDecimal(row["PartsCost"]);
                    lblPartsCost.Text = partsCost.ToString("N2");
                    txtPartsCost.Text = partsCost.ToString("N2");
                    
                    lblTotalCost.Text = Convert.ToDecimal(row["TotalCost"]).ToString("N2");
                    lblTechnicianName.Text = row["TechnicianName"].ToString();
                    txtTechnicianName.Text = row["TechnicianName"].ToString();

                    // 编辑模式设置
                    ShowEditControls(isEditMode);

                    // 显示详情面板，隐藏其他面板
                    pnlRecordDetails.Visible = true;
                    pnlRecordList.Visible = false;
                    pnlCreateRecord.Visible = false;
                }
                else
                {
                    ShowMessage("找不到指定的维修记录。", "warning");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("加载维修记录详情时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 切换显示/编辑控件
        /// </summary>
        private void ShowEditControls(bool isEditMode)
        {
            // 显示模式控件
            lblCompletedDate.Visible = !isEditMode;
            lblDiagnosisDetails.Visible = !isEditMode;
            lblPartsReplaced.Visible = !isEditMode;
            lblLaborCost.Visible = !isEditMode;
            lblPartsCost.Visible = !isEditMode;
            lblTechnicianName.Visible = !isEditMode;
            btnEdit.Visible = !isEditMode;

            // 编辑模式控件
            txtCompletedDate.Visible = isEditMode;
            txtDiagnosisDetails.Visible = isEditMode;
            txtPartsReplaced.Visible = isEditMode;
            txtLaborCost.Visible = isEditMode;
            txtPartsCost.Visible = isEditMode;
            txtTechnicianName.Visible = isEditMode;
            btnSave.Visible = isEditMode;

            // 删除按钮始终可见
            btnDelete.Visible = true;
        }

        /// <summary>
        /// 编辑按钮点击事件
        /// </summary>
        protected void btnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                int recordID = Convert.ToInt32(hfRecordID.Value);
                ShowRecordDetails(recordID, true);
            }
            catch (Exception ex)
            {
                ShowMessage("进入编辑模式时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        protected void btnSave_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid)
                return;

            try
            {
                // 获取表单数据
                int recordID = Convert.ToInt32(hfRecordID.Value);
                DateTime completedDate = DateTime.Parse(txtCompletedDate.Text);
                string diagnosisDetails = txtDiagnosisDetails.Text.Trim();
                string partsReplaced = txtPartsReplaced.Text.Trim();
                decimal laborCost = decimal.Parse(txtLaborCost.Text);
                decimal partsCost = decimal.Parse(txtPartsCost.Text);
                string technicianName = txtTechnicianName.Text.Trim();

                // 更新维修记录
                bool success = ShopManager.UpdateServiceRecord(recordID, completedDate, diagnosisDetails, partsReplaced, laborCost, partsCost, technicianName);

                if (success)
                {
                    ShowMessage("维修记录已成功更新。", "success");
                    ShowRecordDetails(recordID); // 返回查看模式
                }
                else
                {
                    ShowMessage("更新维修记录失败。请稍后再试。", "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("保存维修记录时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        protected void btnCancel_Click(object sender, EventArgs e)
        {
            // 返回列表视图
            pnlRecordDetails.Visible = false;
            pnlRecordList.Visible = true;
            pnlCreateRecord.Visible = false;
            LoadServiceRecords();
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        protected void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                int recordID = Convert.ToInt32(hfRecordID.Value);
                DeleteServiceRecord(recordID);
            }
            catch (Exception ex)
            {
                ShowMessage("删除维修记录时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 删除维修记录
        /// </summary>
        private void DeleteServiceRecord(int recordID)
        {
            try
            {
                bool success = ShopManager.DeleteServiceRecord(recordID);

                if (success)
                {
                    ShowMessage("维修记录已成功删除。", "success");
                    
                    // 返回列表视图
                    pnlRecordDetails.Visible = false;
                    pnlRecordList.Visible = true;
                    pnlCreateRecord.Visible = false;
                    LoadServiceRecords();
                }
                else
                {
                    ShowMessage("删除维修记录失败。请稍后再试。", "warning");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("删除维修记录时出错: " + ex.Message, "danger");
            }
        }

        #endregion

        #region 创建维修记录

        /// <summary>
        /// 创建记录按钮点击事件
        /// </summary>
        protected void lbtnCreateRecord_Click(object sender, EventArgs e)
        {
            // 显示创建记录面板
            pnlRecordDetails.Visible = false;
            pnlRecordList.Visible = false;
            pnlCreateRecord.Visible = true;
            
            // 重新加载预约下拉列表
            LoadPendingAppointments();
            
            // 清空表单
            ddlAppointments.SelectedIndex = 0;
            lblNewCarInfo.Text = "";
            lblNewOwnerName.Text = "";
            lblNewShopName.Text = "";
            lblNewServiceName.Text = "";
            lblNewAppointmentDate.Text = "";
            txtNewCompletedDate.Text = DateTime.Now.ToString("yyyy-MM-ddTHH:mm");
            txtNewDiagnosisDetails.Text = "";
            txtNewPartsReplaced.Text = "";
            txtNewLaborCost.Text = "0.00";
            txtNewPartsCost.Text = "0.00";
            lblNewTotalCost.Text = "0.00";
            txtNewTechnicianName.Text = "";
        }

        /// <summary>
        /// 加载待确认和已确认的预约
        /// </summary>
        private void LoadPendingAppointments()
        {
            try
            {
                // 查询待确认和已确认状态的预约
                string query = @"SELECT a.AppointmentID, a.AppointmentDate, 
                                c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                                co.FullName AS OwnerName, rs.ShopName, s.ServiceName
                                FROM Appointments a
                                INNER JOIN Cars c ON a.CarID = c.CarID
                                INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                                INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                                INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                                WHERE a.Status IN ('Pending', 'Confirmed')
                                AND NOT EXISTS (SELECT 1 FROM ServiceRecords sr WHERE sr.AppointmentID = a.AppointmentID)
                                ORDER BY a.AppointmentDate DESC";
                
                DataTable appointments = DatabaseHelper.ExecuteQuery(query);

                ddlAppointments.Items.Clear();
                ddlAppointments.Items.Add(new ListItem("-- 请选择预约 --", "0"));

                if (appointments != null && appointments.Rows.Count > 0)
                {
                    foreach (DataRow row in appointments.Rows)
                    {
                        string appointmentInfo = string.Format("{0:yyyy-MM-dd HH:mm} - {1} - {2} - {3}",
                            Convert.ToDateTime(row["AppointmentDate"]),
                            row["OwnerName"],
                            row["CarInfo"],
                            row["ShopName"]);
                        
                        ListItem item = new ListItem(appointmentInfo, row["AppointmentID"].ToString());
                        ddlAppointments.Items.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage("加载预约列表时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 预约选择改变事件
        /// </summary>
        protected void ddlAppointments_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                int appointmentID = Convert.ToInt32(ddlAppointments.SelectedValue);
                if (appointmentID > 0)
                {
                    // 获取预约详情
                    DataTable appointmentData = AppointmentManager.GetAppointmentByID(appointmentID);
                    if (appointmentData != null && appointmentData.Rows.Count > 0)
                    {
                        DataRow row = appointmentData.Rows[0];

                        // 显示预约相关信息
                        lblNewCarInfo.Text = row["Make"].ToString() + " " + row["Model"].ToString() + " (" + row["LicensePlate"].ToString() + ")";
                        lblNewOwnerName.Text = GetOwnerName(Convert.ToInt32(row["OwnerID"]));
                        lblNewShopName.Text = row["ShopName"].ToString();
                        lblNewServiceName.Text = row["ServiceName"].ToString();
                        lblNewAppointmentDate.Text = Convert.ToDateTime(row["AppointmentDate"]).ToString("yyyy-MM-dd HH:mm");
                        
                        // 设置默认完成日期为当前时间
                        txtNewCompletedDate.Text = DateTime.Now.ToString("yyyy-MM-ddTHH:mm");
                    }
                }
                else
                {
                    // 清空表单
                    lblNewCarInfo.Text = "";
                    lblNewOwnerName.Text = "";
                    lblNewShopName.Text = "";
                    lblNewServiceName.Text = "";
                    lblNewAppointmentDate.Text = "";
                }
            }
            catch (Exception ex)
            {
                ShowMessage("加载预约详情时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 创建维修记录按钮点击事件
        /// </summary>
        protected void btnCreate_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid)
                return;

            try
            {
                // 获取表单数据
                int appointmentID = Convert.ToInt32(ddlAppointments.SelectedValue);
                DateTime completedDate = DateTime.Parse(txtNewCompletedDate.Text);
                string diagnosisDetails = txtNewDiagnosisDetails.Text.Trim();
                string partsReplaced = txtNewPartsReplaced.Text.Trim();
                decimal laborCost = decimal.Parse(txtNewLaborCost.Text);
                decimal partsCost = decimal.Parse(txtNewPartsCost.Text);
                string technicianName = txtNewTechnicianName.Text.Trim();

                // 创建维修记录
                int recordID = ShopManager.CreateServiceRecord(appointmentID, completedDate, diagnosisDetails, partsReplaced, laborCost, partsCost, technicianName);

                if (recordID > 0)
                {
                    ShowMessage("维修记录已成功创建。", "success");
                    
                    // 返回列表视图或显示新创建的记录详情
                    ShowRecordDetails(recordID);
                }
                else
                {
                    ShowMessage("创建维修记录失败。预约可能不存在或已有维修记录。", "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("创建维修记录时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 取消创建按钮点击事件
        /// </summary>
        protected void btnCancelCreate_Click(object sender, EventArgs e)
        {
            // 返回列表视图
            pnlRecordDetails.Visible = false;
            pnlRecordList.Visible = true;
            pnlCreateRecord.Visible = false;
            LoadServiceRecords();
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取车主姓名
        /// </summary>
        private string GetOwnerName(int ownerID)
        {
            try
            {
                string query = "SELECT FullName FROM CarOwners WHERE OwnerID = @OwnerID";
                System.Data.SqlClient.SqlParameter parameter = new System.Data.SqlClient.SqlParameter("@OwnerID", ownerID);
                object result = DatabaseHelper.ExecuteScalar(query, parameter);
                return result != null ? result.ToString() : "未知";
            }
            catch
            {
                return "未知";
            }
        }

        /// <summary>
        /// 显示消息
        /// </summary>
        private void ShowMessage(string message, string type)
        {
            lblMessage.Text = message;
            lblMessage.CssClass = "alert alert-" + type;
            pnlMessage.Visible = true;
        }

        /// <summary>
        /// 返回控制台按钮点击事件
        /// </summary>
        protected void lbtnBackToDashboard_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Admin/Dashboard.aspx");
        }

        #endregion
    }
} 