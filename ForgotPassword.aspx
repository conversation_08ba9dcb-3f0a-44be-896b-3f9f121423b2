<%@ Page Title="忘记密码" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeBehind="ForgotPassword.aspx.cs" Inherits="ForgotPassword" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="row">
        <div class="col-md-6 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">找回密码</h3>
                </div>
                <div class="card-body">
                    <asp:Panel ID="pnlStep1" runat="server">
                        <div class="form-group">
                            <label for="txtUsername">用户名</label>
                            <asp:TextBox ID="txtUsername" runat="server" CssClass="form-control" placeholder="请输入用户名"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvUsername" runat="server" ControlToValidate="txtUsername"
                                ErrorMessage="用户名不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="Step1"></asp:RequiredFieldValidator>
                        </div>
                        <div class="form-group">
                            <label for="txtPhoneNumber">手机号码</label>
                            <asp:TextBox ID="txtPhoneNumber" runat="server" CssClass="form-control" placeholder="请输入手机号码"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPhoneNumber" runat="server" ControlToValidate="txtPhoneNumber"
                                ErrorMessage="手机号码不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="Step1"></asp:RequiredFieldValidator>
                            <asp:RegularExpressionValidator ID="revPhoneNumber" runat="server" ControlToValidate="txtPhoneNumber"
                                ValidationExpression="^1[3-9]\d{9}$" ErrorMessage="请输入有效的手机号码" Display="Dynamic"
                                CssClass="text-danger" ValidationGroup="Step1"></asp:RegularExpressionValidator>
                        </div>
                        <div class="form-group">
                            <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
                        </div>
                        <div class="form-group">
                            <asp:Button ID="btnNext" runat="server" Text="下一步" CssClass="btn btn-primary" OnClick="btnNext_Click" ValidationGroup="Step1" />
                            <a href="Login.aspx" class="btn btn-outline-secondary ml-2">返回登录</a>
                        </div>
                    </asp:Panel>

                    <asp:Panel ID="pnlStep2" runat="server" Visible="false">
                        <div class="form-group">
                            <label for="txtVerificationCode">验证码</label>
                            <div class="input-group">
                                <asp:TextBox ID="txtVerificationCode" runat="server" CssClass="form-control" placeholder="请输入验证码"></asp:TextBox>
                                <div class="input-group-append">
                                    <asp:Button ID="btnSendCode" runat="server" Text="发送验证码" CssClass="btn btn-outline-secondary" OnClick="btnSendCode_Click" CausesValidation="false" />
                                </div>
                            </div>
                            <asp:RequiredFieldValidator ID="rfvVerificationCode" runat="server" ControlToValidate="txtVerificationCode"
                                ErrorMessage="验证码不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="Step2"></asp:RequiredFieldValidator>
                        </div>
                        <div class="form-group">
                            <asp:Label ID="lblVerifyMessage" runat="server" CssClass="text-danger" EnableViewState="true" EnableHtml="true"></asp:Label>
                        </div>
                        <div class="form-group">
                            <asp:Button ID="btnVerifyCode" runat="server" Text="验证" CssClass="btn btn-primary" OnClick="btnVerifyCode_Click" ValidationGroup="Step2" />
                            <asp:Button ID="btnBack" runat="server" Text="返回" CssClass="btn btn-outline-secondary ml-2" OnClick="btnBack_Click" CausesValidation="false" />
                        </div>
                    </asp:Panel>

                    <asp:Panel ID="pnlStep3" runat="server" Visible="false">
                        <div class="form-group">
                            <label for="txtNewPassword">新密码</label>
                            <asp:TextBox ID="txtNewPassword" runat="server" TextMode="Password" CssClass="form-control" placeholder="请输入新密码"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvNewPassword" runat="server" ControlToValidate="txtNewPassword"
                                ErrorMessage="新密码不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="Step3"></asp:RequiredFieldValidator>
                        </div>
                        <div class="form-group">
                            <label for="txtConfirmPassword">确认密码</label>
                            <asp:TextBox ID="txtConfirmPassword" runat="server" TextMode="Password" CssClass="form-control" placeholder="请再次输入新密码"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvConfirmPassword" runat="server" ControlToValidate="txtConfirmPassword"
                                ErrorMessage="确认密码不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="Step3"></asp:RequiredFieldValidator>
                            <asp:CompareValidator ID="cvPassword" runat="server" ControlToValidate="txtConfirmPassword" ControlToCompare="txtNewPassword"
                                ErrorMessage="两次输入的密码不一致" Display="Dynamic" CssClass="text-danger" ValidationGroup="Step3"></asp:CompareValidator>
                        </div>
                        <div class="form-group">
                            <div class="card bg-light">
                                <div class="card-body p-2">
                                    <small class="text-muted">密码要求：</small>
                                    <ul class="mb-0 pl-4 small text-muted">
                                        <li>至少 <asp:Literal ID="minLength" runat="server">8</asp:Literal> 个字符</li>
                                        <li id="requireUppercase" runat="server">至少含有一个大写字母</li>
                                        <li id="requireDigit" runat="server">至少含有一个数字</li>
                                        <li id="requireSpecialChar" runat="server" visible="false">至少含有一个特殊字符 (!@#$%^&*)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <asp:Label ID="lblResetMessage" runat="server" CssClass="text-danger"></asp:Label>
                        </div>
                        <div class="form-group">
                            <asp:Button ID="btnResetPassword" runat="server" Text="重置密码" CssClass="btn btn-primary" OnClick="btnResetPassword_Click" ValidationGroup="Step3" />
                        </div>
                    </asp:Panel>

                    <asp:Panel ID="pnlSuccess" runat="server" Visible="false">
                        <div class="alert alert-success">
                            <h4>密码重置成功！</h4>
                            <p>您的密码已经成功重置，现在可以使用新密码登录。</p>
                            <asp:Button ID="btnBackToLogin" runat="server" Text="返回登录" CssClass="btn btn-success" OnClick="btnBackToLogin_Click" CausesValidation="false" />
                        </div>
                    </asp:Panel>
                </div>
            </div>
        </div>
    </div>
</asp:Content> 