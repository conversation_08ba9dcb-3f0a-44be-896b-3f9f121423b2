<%@ Page Title="预约管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="RepairShop_Appointments" Codebehind="Appointments.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-calendar-alt"></i> 预约管理</h2>
                <hr />
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list"></i> 预约列表</h5>
                            <div>
                                <asp:DropDownList ID="ddlFilterStatus" runat="server" CssClass="form-control form-control-sm" AutoPostBack="true" OnSelectedIndexChanged="ddlFilterStatus_SelectedIndexChanged">
                                    <asp:ListItem Value="" Text="全部状态" Selected="True"></asp:ListItem>
                                    <asp:ListItem Value="Pending" Text="待处理"></asp:ListItem>
                                    <asp:ListItem Value="Confirmed" Text="已确认"></asp:ListItem>
                                    <asp:ListItem Value="Completed" Text="已完成"></asp:ListItem>
                                    <asp:ListItem Value="Cancelled" Text="已取消"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvAppointments" runat="server" AutoGenerateColumns="False"
                            CssClass="table table-striped table-hover" DataKeyNames="AppointmentID"
                            OnRowCommand="gvAppointments_RowCommand" EmptyDataText="暂无预约记录" AllowPaging="true"
                            PageSize="10" OnPageIndexChanging="gvAppointments_PageIndexChanging">
                            <Columns>
                                <asp:BoundField DataField="AppointmentID" HeaderText="ID" />
                                <asp:BoundField DataField="AppointmentDate" HeaderText="预约时间" DataFormatString="{0:yyyy-MM-dd HH:mm}" />
                                <asp:BoundField DataField="CarInfo" HeaderText="车辆信息" />
                                <asp:BoundField DataField="OwnerName" HeaderText="车主" />
                                <asp:BoundField DataField="ServiceName" HeaderText="服务项目" />
                                <asp:TemplateField HeaderText="状态">
                                    <ItemTemplate>
                                        <asp:Label ID="lblStatus" runat="server" Text='<%# GetStatusText(Eval("Status").ToString()) %>'
                                            CssClass='<%# GetStatusClass(Eval("Status").ToString()) %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <asp:Panel ID="pnlActions" runat="server" CssClass="btn-group">
                                            <asp:LinkButton ID="lbtnView" runat="server" CssClass="btn btn-sm btn-info"
                                                CommandName="ViewAppointment" CommandArgument='<%# Eval("AppointmentID") %>' ToolTip="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </asp:LinkButton>
                                            <asp:LinkButton ID="lbtnConfirm" runat="server" CssClass="btn btn-sm btn-success"
                                                CommandName="ConfirmAppointment" CommandArgument='<%# Eval("AppointmentID") %>' 
                                                Visible='<%# Eval("Status").ToString() == "Pending" %>' ToolTip="确认预约">
                                                <i class="fas fa-check"></i>
                                            </asp:LinkButton>
                                            <asp:LinkButton ID="lbtnComplete" runat="server" CssClass="btn btn-sm btn-primary"
                                                CommandName="CompleteAppointment" CommandArgument='<%# Eval("AppointmentID") %>' 
                                                Visible='<%# Eval("Status").ToString() == "Confirmed" %>' ToolTip="完成维修">
                                                <i class="fas fa-tools"></i>
                                            </asp:LinkButton>
                                        </asp:Panel>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <PagerSettings Mode="NumericFirstLast" FirstPageText="首页" LastPageText="末页" />
                            <PagerStyle HorizontalAlign="Center" CssClass="pagination-container" />
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>

        <asp:Panel ID="pnlAppointmentDetails" runat="server" Visible="false">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle"></i> 预约详情</h5>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-3">预约ID：</dt>
                                <dd class="col-sm-9"><asp:Label ID="lblAppointmentID" runat="server"></asp:Label></dd>
                                
                                <dt class="col-sm-3">预约时间：</dt>
                                <dd class="col-sm-9"><asp:Label ID="lblAppointmentDate" runat="server"></asp:Label></dd>
                                
                                <dt class="col-sm-3">车主：</dt>
                                <dd class="col-sm-9"><asp:Label ID="lblOwnerName" runat="server"></asp:Label></dd>
                                
                                <dt class="col-sm-3">车辆信息：</dt>
                                <dd class="col-sm-9"><asp:Label ID="lblCarInfo" runat="server"></asp:Label></dd>
                                
                                <dt class="col-sm-3">服务项目：</dt>
                                <dd class="col-sm-9"><asp:Label ID="lblServiceName" runat="server"></asp:Label></dd>
                                
                                <dt class="col-sm-3">预计耗时：</dt>
                                <dd class="col-sm-9"><asp:Label ID="lblEstimatedTime" runat="server"></asp:Label> 分钟</dd>
                                
                                <dt class="col-sm-3">基础价格：</dt>
                                <dd class="col-sm-9"><asp:Label ID="lblBasePrice" runat="server"></asp:Label></dd>
                                
                                <dt class="col-sm-3">状态：</dt>
                                <dd class="col-sm-9">
                                    <asp:Label ID="lblStatus" runat="server" CssClass="badge"></asp:Label>
                                </dd>
                                
                                <dt class="col-sm-3">描述：</dt>
                                <dd class="col-sm-9"><asp:Label ID="lblDescription" runat="server"></asp:Label></dd>
                            </dl>

                            <div class="text-center mt-3">
                                <asp:Button ID="btnConfirm" runat="server" Text="确认预约" CssClass="btn btn-success" OnClick="btnConfirm_Click" Visible="false" />
                                <asp:Button ID="btnComplete" runat="server" Text="完成维修" CssClass="btn btn-primary" OnClick="btnComplete_Click" Visible="false" />
                                <asp:Button ID="btnBack" runat="server" Text="返回列表" CssClass="btn btn-secondary" OnClick="btnBack_Click" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <asp:Panel ID="pnlCompleteForm" runat="server" Visible="false">
            <div class="row mt-4">
                <div class="col-md-8 offset-md-2">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-clipboard-check"></i> 完成维修表单</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-group row">
                                <label for="txtDiagnosisDetails" class="col-sm-3 col-form-label">诊断详情：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtDiagnosisDetails" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvDiagnosisDetails" runat="server" ControlToValidate="txtDiagnosisDetails"
                                        ErrorMessage="诊断详情不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="CompleteForm"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtPartsReplaced" class="col-sm-3 col-form-label">更换零件：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtPartsReplaced" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2"></asp:TextBox>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtLaborCost" class="col-sm-3 col-form-label">工时费(元)：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtLaborCost" runat="server" CssClass="form-control"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvLaborCost" runat="server" ControlToValidate="txtLaborCost"
                                        ErrorMessage="工时费不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="CompleteForm"></asp:RequiredFieldValidator>
                                    <asp:RegularExpressionValidator ID="revLaborCost" runat="server" ControlToValidate="txtLaborCost"
                                        ErrorMessage="请输入有效的金额" Display="Dynamic" CssClass="text-danger" ValidationGroup="CompleteForm"
                                        ValidationExpression="^\d+(\.\d{1,2})?$"></asp:RegularExpressionValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtPartsCost" class="col-sm-3 col-form-label">零件费(元)：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtPartsCost" runat="server" CssClass="form-control"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvPartsCost" runat="server" ControlToValidate="txtPartsCost"
                                        ErrorMessage="零件费不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="CompleteForm"></asp:RequiredFieldValidator>
                                    <asp:RegularExpressionValidator ID="revPartsCost" runat="server" ControlToValidate="txtPartsCost"
                                        ErrorMessage="请输入有效的金额" Display="Dynamic" CssClass="text-danger" ValidationGroup="CompleteForm"
                                        ValidationExpression="^\d+(\.\d{1,2})?$"></asp:RegularExpressionValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtTechnicianName" class="col-sm-3 col-form-label">技师姓名：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtTechnicianName" runat="server" CssClass="form-control"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvTechnicianName" runat="server" ControlToValidate="txtTechnicianName"
                                        ErrorMessage="技师姓名不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="CompleteForm"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-9 offset-sm-3">
                                    <asp:Button ID="btnSubmitComplete" runat="server" Text="提交完成记录" CssClass="btn btn-primary" ValidationGroup="CompleteForm" OnClick="btnSubmitComplete_Click" />
                                    <asp:Button ID="btnCancelComplete" runat="server" Text="取消" CssClass="btn btn-secondary ml-2" OnClick="btnCancelComplete_Click" CausesValidation="false" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <div class="row mt-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="alert" Visible="false"></asp:Label>
            </div>
        </div>
    </div>
</asp:Content> 