using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Admin_CategoryManagement : Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否已登录且为管理员
        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.RawUrl));
            return;
        }

        if (!IsPostBack)
        {
            // 加载服务类别列表
            LoadCategories();
        }
    }

    /// <summary>
    /// 加载服务类别列表
    /// </summary>
    private void LoadCategories()
    {
        try
        {
            // 获取服务类别列表
            DataTable dt = AdminManager.GetServiceCategories();
            gvCategories.DataSource = dt;
            gvCategories.DataBind();
        }
        catch (Exception ex)
        {
            lblMessage.Text = "加载服务类别列表时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 保存按钮点击事件
    /// </summary>
    protected void btnSave_Click(object sender, EventArgs e)
    {
        try
        {
            int categoryID = Convert.ToInt32(hfCategoryID.Value);
            string categoryName = txtCategoryName.Text.Trim();
            string description = txtDescription.Text.Trim();

            if (categoryID == 0)
            {
                // 添加新类别
                int result = AdminManager.AddServiceCategory(categoryName, description);
                if (result > 0)
                {
                    lblMessage.Text = "服务类别添加成功";
                    lblMessage.CssClass = "text-success";
                    ClearForm();
                }
                else
                {
                    lblMessage.Text = "服务类别添加失败";
                    lblMessage.CssClass = "text-danger";
                }
            }
            else
            {
                // 更新类别
                bool result = AdminManager.UpdateServiceCategory(categoryID, categoryName, description);
                if (result)
                {
                    lblMessage.Text = "服务类别更新成功";
                    lblMessage.CssClass = "text-success";
                    ClearForm();
                }
                else
                {
                    lblMessage.Text = "服务类别更新失败";
                    lblMessage.CssClass = "text-danger";
                }
            }

            // 重新加载类别列表
            LoadCategories();
        }
        catch (Exception ex)
        {
            lblMessage.Text = "保存服务类别时出错：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    protected void btnCancel_Click(object sender, EventArgs e)
    {
        ClearForm();
    }

    /// <summary>
    /// 清空表单
    /// </summary>
    private void ClearForm()
    {
        hfCategoryID.Value = "0";
        txtCategoryName.Text = "";
        txtDescription.Text = "";
        litCardTitle.Text = "添加服务类别";
        btnSave.Text = "保存";
        btnCancel.Visible = false;
    }

    /// <summary>
    /// 类别列表行命令事件
    /// </summary>
    protected void gvCategories_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        try
        {
            int categoryID = Convert.ToInt32(e.CommandArgument);

            switch (e.CommandName)
            {
                case "EditCategory":
                    // 编辑类别
                    EditCategory(categoryID);
                    break;

                case "DeleteCategory":
                    // 删除类别
                    DeleteCategory(categoryID);
                    break;
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "处理类别操作时出错：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 编辑类别
    /// </summary>
    private void EditCategory(int categoryID)
    {
        try
        {
            // 获取类别详情
            DataTable dt = AdminManager.GetServiceCategories();
            DataRow[] rows = dt.Select("CategoryID = " + categoryID);

            if (rows.Length > 0)
            {
                DataRow row = rows[0];

                // 填充表单
                hfCategoryID.Value = categoryID.ToString();
                txtCategoryName.Text = row["CategoryName"].ToString();
                txtDescription.Text = row["Description"].ToString();
                litCardTitle.Text = "编辑服务类别";
                btnSave.Text = "更新";
                btnCancel.Visible = true;
            }
            else
            {
                lblMessage.Text = "找不到指定的服务类别";
                lblMessage.CssClass = "text-danger";
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "编辑服务类别时出错：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 删除类别
    /// </summary>
    private void DeleteCategory(int categoryID)
    {
        try
        {
            // 删除类别
            bool result = AdminManager.DeleteServiceCategory(categoryID);
            if (result)
            {
                lblMessage.Text = "服务类别删除成功";
                lblMessage.CssClass = "text-success";
            }
            else
            {
                lblMessage.Text = "无法删除服务类别，该类别已被服务使用";
                lblMessage.CssClass = "text-danger";
            }

            // 重新加载类别列表
            LoadCategories();
        }
        catch (Exception ex)
        {
            lblMessage.Text = "删除服务类别时出错：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }
} 