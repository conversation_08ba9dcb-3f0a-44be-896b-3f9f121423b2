<%@ Page Title="支付管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Codebehind="PaymentSimulation.aspx.cs" Inherits="CarOwner_PaymentSimulation" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script type="text/javascript">
        function confirmPayment() {
            return confirm("确定要支付此笔费用吗？");
        }
        
        function confirmVerification() {
            return confirm("确认您已经验收了此次维修服务？验收后将无法取消。");
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-3">
            <div class="col">
                <h2><i class="fas fa-credit-card"></i> 支付管理</h2>
                <p class="lead">管理您的维修服务验收和支付</p>
            </div>
        </div>

        <!-- 消息提示区域 -->
        <div class="row mb-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="alert" Visible="false"></asp:Label>
            </div>
        </div>

        <asp:Panel ID="pnlMainContent" runat="server">
            <div class="row mb-3">
                <div class="col">
                    <ul class="nav nav-tabs mb-4">
                        <li class="nav-item">
                            <asp:LinkButton ID="lbtnUnverified" runat="server" CssClass="nav-link active" OnClick="lbtnUnverified_Click">待验收维修</asp:LinkButton>
                        </li>
                        <li class="nav-item">
                            <asp:LinkButton ID="lbtnUnpaid" runat="server" CssClass="nav-link" OnClick="lbtnUnpaid_Click">待支付维修</asp:LinkButton>
                        </li>
                        <li class="nav-item">
                            <asp:LinkButton ID="lbtnPaymentHistory" runat="server" CssClass="nav-link" OnClick="lbtnPaymentHistory_Click">支付记录</asp:LinkButton>
                        </li>
                    </ul>
                </div>
            </div>

            <asp:MultiView ID="mvContent" runat="server" ActiveViewIndex="0">
                <!-- 待验收维修视图 -->
                <asp:View ID="vwUnverified" runat="server">
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">待验收的维修服务</h5>
                        </div>
                        <div class="card-body">
                            <asp:GridView ID="gvUnverified" runat="server" AutoGenerateColumns="False"
                                CssClass="table table-striped table-hover" DataKeyNames="RecordID"
                                OnRowCommand="gvUnverified_RowCommand" EmptyDataText="暂无待验收的维修服务">
                                <Columns>
                                    <asp:BoundField DataField="ServiceName" HeaderText="维修项目" />
                                    <asp:BoundField DataField="ShopName" HeaderText="维修店" />
                                    <asp:BoundField DataField="Make" HeaderText="厂商" />
                                    <asp:BoundField DataField="Model" HeaderText="型号" />
                                    <asp:BoundField DataField="LicensePlate" HeaderText="车牌号" />
                                    <asp:BoundField DataField="CompletedDate" HeaderText="完成时间" DataFormatString="{0:yyyy-MM-dd HH:mm}" />
                                    <asp:BoundField DataField="TotalCost" HeaderText="总费用" DataFormatString="{0:C}" />
                                    <asp:TemplateField HeaderText="操作">
                                        <ItemTemplate>
                                            <asp:LinkButton ID="lbtnVerifyService" runat="server" CssClass="btn btn-sm btn-success"
                                                CommandName="VerifyService" CommandArgument='<%# Eval("RecordID") %>'
                                                OnClientClick="return confirmVerification();">
                                                <i class="fas fa-check"></i> 验收确认
                                            </asp:LinkButton>
                                            <asp:LinkButton ID="lbtnViewDetails" runat="server" CssClass="btn btn-sm btn-info ml-1"
                                                CommandName="ViewServiceDetails" CommandArgument='<%# Eval("RecordID") %>'>
                                                <i class="fas fa-eye"></i> 查看详情
                                            </asp:LinkButton>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                            </asp:GridView>
                        </div>
                    </div>
                </asp:View>

                <!-- 待支付维修视图 -->
                <asp:View ID="vwUnpaid" runat="server">
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">待支付的维修服务</h5>
                        </div>
                        <div class="card-body">
                            <asp:GridView ID="gvUnpaid" runat="server" AutoGenerateColumns="False"
                                CssClass="table table-striped table-hover" DataKeyNames="RecordID"
                                OnRowCommand="gvUnpaid_RowCommand" EmptyDataText="暂无待支付的维修服务">
                                <Columns>
                                    <asp:BoundField DataField="ServiceName" HeaderText="维修项目" />
                                    <asp:BoundField DataField="ShopName" HeaderText="维修店" />
                                    <asp:BoundField DataField="Make" HeaderText="厂商" />
                                    <asp:BoundField DataField="Model" HeaderText="型号" />
                                    <asp:BoundField DataField="LicensePlate" HeaderText="车牌号" />
                                    <asp:BoundField DataField="CompletedDate" HeaderText="完成时间" DataFormatString="{0:yyyy-MM-dd HH:mm}" />
                                    <asp:BoundField DataField="TotalCost" HeaderText="应付金额" DataFormatString="{0:C}" />
                                    <asp:TemplateField HeaderText="操作">
                                        <ItemTemplate>
                                            <asp:LinkButton ID="lbtnPayService" runat="server" CssClass="btn btn-sm btn-primary"
                                                CommandName="PayService" CommandArgument='<%# Eval("RecordID") %>'>
                                                <i class="fas fa-money-bill"></i> 支付
                                            </asp:LinkButton>
                                            <asp:LinkButton ID="lbtnViewDetails" runat="server" CssClass="btn btn-sm btn-info ml-1"
                                                CommandName="ViewServiceDetails" CommandArgument='<%# Eval("RecordID") %>'>
                                                <i class="fas fa-eye"></i> 查看详情
                                            </asp:LinkButton>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                            </asp:GridView>
                        </div>
                    </div>
                </asp:View>

                <!-- 支付历史视图 -->
                <asp:View ID="vwPaymentHistory" runat="server">
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">支付历史记录</h5>
                        </div>
                        <div class="card-body">
                            <asp:GridView ID="gvPaymentHistory" runat="server" AutoGenerateColumns="False"
                                CssClass="table table-striped table-hover" DataKeyNames="PaymentID"
                                OnRowCommand="gvPaymentHistory_RowCommand" EmptyDataText="暂无支付记录">
                                <Columns>
                                    <asp:BoundField DataField="ServiceName" HeaderText="维修项目" />
                                    <asp:BoundField DataField="ShopName" HeaderText="维修店" />
                                    <asp:BoundField DataField="Make" HeaderText="厂商" />
                                    <asp:BoundField DataField="Model" HeaderText="型号" />
                                    <asp:BoundField DataField="LicensePlate" HeaderText="车牌号" />
                                    <asp:BoundField DataField="PaymentDate" HeaderText="支付时间" DataFormatString="{0:yyyy-MM-dd HH:mm}" />
                                    <asp:BoundField DataField="Amount" HeaderText="支付金额" DataFormatString="{0:C}" />
                                    <asp:BoundField DataField="PaymentMethod" HeaderText="支付方式" />
                                    <asp:BoundField DataField="Status" HeaderText="状态" />
                                    <asp:TemplateField HeaderText="操作">
                                        <ItemTemplate>
                                            <asp:LinkButton ID="lbtnViewDetailsPayment" runat="server" CssClass="btn btn-sm btn-info"
                                                CommandName="ViewServiceDetails" CommandArgument='<%# Eval("RecordID") %>'>
                                                <i class="fas fa-eye"></i> 查看详情
                                            </asp:LinkButton>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                            </asp:GridView>
                        </div>
                    </div>
                </asp:View>
            </asp:MultiView>
        </asp:Panel>

        <!-- 服务详情面板 -->
        <asp:Panel ID="pnlServiceDetails" runat="server" Visible="false">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">维修服务详情</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>服务信息</h6>
                            <dl class="row">
                                <dt class="col-sm-4">维修项目：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblServiceName" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">完成时间：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblCompletedDate" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">维修诊断：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblDiagnosisDetails" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">更换零件：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblPartsReplaced" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">技师：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblTechnicianName" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6>费用信息</h6>
                            <dl class="row">
                                <dt class="col-sm-4">工时费：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblLaborCost" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">零件费：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblPartsCost" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">总费用：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblTotalCost" runat="server" CssClass="font-weight-bold"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">验收状态：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblIsVerified" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">支付状态：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblIsPaid" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <hr />

                    <div class="row">
                        <div class="col-md-6">
                            <h6>车辆信息</h6>
                            <dl class="row">
                                <dt class="col-sm-4">厂商型号：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblCarMakeModel" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">车牌号：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblLicensePlate" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6>维修店信息</h6>
                            <dl class="row">
                                <dt class="col-sm-4">维修店：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblShopName" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">地址：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblShopAddress" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">联系电话：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblShopPhone" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <asp:Panel ID="pnlServiceActions" runat="server" CssClass="mt-4">
                        <hr />
                        <div class="row justify-content-center">
                            <div class="col-md-8 text-center">
                                <div class="btn-group-lg d-flex justify-content-center">
                                    <asp:Panel ID="pnlVerifyAction" runat="server" Visible="false" CssClass="mx-2">
                                        <asp:Button ID="btnVerifyService" runat="server" Text="验收确认" CssClass="btn btn-success btn-lg px-4"
                                            OnClick="btnVerifyService_Click" OnClientClick="return confirmVerification();" />
                                    </asp:Panel>
                                    
                                    <asp:Panel ID="pnlPayAction" runat="server" Visible="false" CssClass="mx-2">
                                        <h6 class="mb-3">选择支付方式：</h6>
                                        <div class="form-group mb-3">
                                            <asp:RadioButtonList ID="rblPaymentMethod" runat="server" RepeatDirection="Horizontal" CssClass="form-check-inline justify-content-center">
                                                <asp:ListItem Text="微信支付" Value="WeChat" Selected="True"></asp:ListItem>
                                                <asp:ListItem Text="支付宝" Value="Alipay"></asp:ListItem>
                                                <asp:ListItem Text="信用卡" Value="CreditCard"></asp:ListItem>
                                                <asp:ListItem Text="现金" Value="Cash"></asp:ListItem>
                                            </asp:RadioButtonList>
                                        </div>
                                        <asp:Button ID="btnPayNow" runat="server" Text="立即支付" CssClass="btn btn-primary btn-lg px-4"
                                            OnClick="btnPayNow_Click" OnClientClick="return confirmPayment();" />
                                    </asp:Panel>
                                    
                                    <div class="mx-2 mt-2 mt-md-0">
                                        <asp:Button ID="btnBackToList" runat="server" Text="返回列表" CssClass="btn btn-secondary btn-lg px-4"
                                            OnClick="btnBackToList_Click" CausesValidation="false" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </asp:Panel>
                </div>
            </div>
        </asp:Panel>

        <!-- 支付确认面板 -->
        <asp:Panel ID="pnlPaymentConfirmation" runat="server" Visible="false">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">支付完成</h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                    </div>
                    <h4>支付成功！</h4>
                    <p class="lead">您已成功完成支付，交易号：<asp:Label ID="lblTransactionNumber" runat="server"></asp:Label></p>
                    <div class="mt-4">
                        <asp:Button ID="btnDonePayment" runat="server" Text="完成" CssClass="btn btn-success btn-lg px-5"
                            OnClick="btnDonePayment_Click" />
                    </div>
                </div>
            </div>
        </asp:Panel>
    </div>
</asp:Content> 