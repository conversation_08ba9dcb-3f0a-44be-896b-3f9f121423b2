using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;

public partial class CarOwner_ServiceHistory : System.Web.UI.Page
{
    private int userID;
    private int ownerID;
    
    // 通过属性从隐藏字段获取和设置值
    private int selectedRecordID
    {
        get { return Convert.ToInt32(hfSelectedRecordID.Value); }
        set { hfSelectedRecordID.Value = value.ToString(); }
    }
    
    private int shopID
    {
        get { return Convert.ToInt32(hfSelectedShopID.Value); }
        set { hfSelectedShopID.Value = value.ToString(); }
    }
    
    private DataRow currentRecord;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取当前用户信息
        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "CarOwner")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        
        // 获取车主ID
        ownerID = CarManager.GetOwnerIDByUserID(userID);
        if (ownerID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            // 加载车辆列表
            LoadCars();
            
            // 加载维修记录
            LoadServiceRecords();

            // 检查是否有查看记录的请求
            if (!string.IsNullOrEmpty(Request.QueryString["id"]))
            {
                int recordID;
                if (int.TryParse(Request.QueryString["id"], out recordID))
                {
                    ShowRecordDetails(recordID);
                }
            }
        }
    }

    /// <summary>
    /// 加载车辆列表
    /// </summary>
    private void LoadCars()
    {
        DataTable carsTable = CarManager.GetCarsByOwnerID(ownerID);
        ddlCarFilter.Items.Clear();
        ddlCarFilter.Items.Add(new ListItem("所有车辆", "0"));

        if (carsTable != null && carsTable.Rows.Count > 0)
        {
            foreach (DataRow row in carsTable.Rows)
            {
                string carInfo = row["Make"].ToString() + " " + row["Model"].ToString() + " (" + row["LicensePlate"].ToString() + ")";
                ddlCarFilter.Items.Add(new ListItem(carInfo, row["CarID"].ToString()));
            }
        }
    }

    /// <summary>
    /// 加载维修记录
    /// </summary>
    private void LoadServiceRecords()
    {
        int carID = 0;
        if (ddlCarFilter.SelectedValue != "0")
        {
            carID = Convert.ToInt32(ddlCarFilter.SelectedValue);
        }

        DataTable recordsTable;
        if (carID > 0)
        {
            recordsTable = AppointmentManager.GetServiceRecordsByCarID(carID);
        }
        else
        {
            recordsTable = AppointmentManager.GetServiceRecordsByOwnerID(ownerID);
        }

        gvServiceRecords.DataSource = recordsTable;
        gvServiceRecords.DataBind();
    }

    /// <summary>
    /// 车辆筛选下拉框选择改变事件
    /// </summary>
    protected void ddlCarFilter_SelectedIndexChanged(object sender, EventArgs e)
    {
        LoadServiceRecords();
    }

    /// <summary>
    /// GridView行命令事件
    /// </summary>
    protected void gvServiceRecords_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "ViewRecord")
        {
            int recordID = Convert.ToInt32(e.CommandArgument);
            ShowRecordDetails(recordID);
        }
    }

    /// <summary>
    /// 显示记录详情
    /// </summary>
    private void ShowRecordDetails(int recordID)
    {
        try
        {
            if (recordID <= 0)
            {
                lblMessage.Text = "无效的记录ID。";
                System.Diagnostics.Debug.WriteLine($"ShowRecordDetails: 无效的记录ID: {recordID}");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"显示维修记录详情，ID: {recordID}");

            // 查询记录详情
            string query = @"SELECT sr.RecordID, sr.CompletedDate, sr.DiagnosisDetails, sr.PartsReplaced,
                            sr.LaborCost, sr.PartsCost, sr.TotalCost, sr.TechnicianName,
                            a.AppointmentDate, a.ShopID,
                            c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                            rs.ShopName, s.ServiceName
                            FROM ServiceRecords sr
                            INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                            INNER JOIN Cars c ON a.CarID = c.CarID
                            INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                            INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                            WHERE sr.RecordID = @RecordID";
            SqlParameter parameter = new SqlParameter("@RecordID", recordID);
            DataTable recordTable = DatabaseHelper.ExecuteQuery(query, parameter);

            if (recordTable != null && recordTable.Rows.Count > 0)
            {
                currentRecord = recordTable.Rows[0];
                
                // 设置关键ID值
                selectedRecordID = recordID;
                System.Diagnostics.Debug.WriteLine($"设置selectedRecordID: {selectedRecordID}, HiddenField值: {hfSelectedRecordID.Value}");
                
                if (currentRecord["ShopID"] != DBNull.Value)
                {
                    shopID = Convert.ToInt32(currentRecord["ShopID"]);
                    System.Diagnostics.Debug.WriteLine($"设置shopID: {shopID}, HiddenField值: {hfSelectedShopID.Value}");
                }
                else
                {
                    // ShopID为空时记录错误
                    System.Diagnostics.Debug.WriteLine($"错误：记录ID {recordID} 的ShopID为空");
                    shopID = 0;
                }

                // 显示基本信息
                lblServiceName.Text = currentRecord["ServiceName"].ToString();
                lblAppointmentDate.Text = Convert.ToDateTime(currentRecord["AppointmentDate"]).ToString("yyyy-MM-dd HH:mm");
                lblCompletedDate.Text = Convert.ToDateTime(currentRecord["CompletedDate"]).ToString("yyyy-MM-dd HH:mm");

                // 显示车辆信息
                lblCarInfo.Text = currentRecord["CarInfo"].ToString();
                lblShopName.Text = currentRecord["ShopName"].ToString();
                lblTechnicianName.Text = currentRecord["TechnicianName"] != DBNull.Value ? currentRecord["TechnicianName"].ToString() : "未记录";

                // 显示维修内容
                lblDiagnosisDetails.Text = currentRecord["DiagnosisDetails"] != DBNull.Value ? currentRecord["DiagnosisDetails"].ToString() : "无";
                lblPartsReplaced.Text = currentRecord["PartsReplaced"] != DBNull.Value ? currentRecord["PartsReplaced"].ToString() : "无";

                // 显示费用明细
                lblLaborCost.Text = currentRecord["LaborCost"] != DBNull.Value ? string.Format("¥{0:N2}", currentRecord["LaborCost"]) : "¥0.00";
                lblPartsCost.Text = currentRecord["PartsCost"] != DBNull.Value ? string.Format("¥{0:N2}", currentRecord["PartsCost"]) : "¥0.00";
                lblTotalCost.Text = currentRecord["TotalCost"] != DBNull.Value ? string.Format("¥{0:N2}", currentRecord["TotalCost"]) : "¥0.00";

                // 仅当ShopID有效时才检查评价状态
                if (shopID > 0)
                {
                    // 检查是否已评价
                    CheckIfReviewed(recordID);
                    
                    // 加载该记录的评价照片
                    LoadReviewPhotos(recordID);
                }
                else
                {
                    pnlAddReview.Visible = false;
                }

                // 切换面板
                pnlServiceRecords.Visible = false;
                pnlRecordDetails.Visible = true;
                lblMessage.Text = string.Empty;
            }
            else
            {
                lblMessage.Text = "未找到维修记录详情。";
                lblMessage.CssClass = "text-danger";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine("显示维修记录详情时出错: " + ex.Message);
            lblMessage.Text = "加载维修记录详情时出错，请稍后再试。";
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 检查是否已评价
    /// </summary>
    private void CheckIfReviewed(int recordID)
    {
        try
        {
            if (recordID <= 0)
            {
                System.Diagnostics.Debug.WriteLine($"检查评价状态失败：无效的记录ID: {recordID}, 隐藏字段值: {hfSelectedRecordID.Value}");
                pnlAddReview.Visible = false;
                pnlMyReview.Visible = false;
                return;
            }

            string query = @"SELECT r.ReviewID, r.Rating, r.Comments, r.ReviewDate 
                           FROM Reviews r
                           WHERE r.ServiceRecordID = @RecordID";
            SqlParameter parameter = new SqlParameter("@RecordID", recordID);
            DataTable reviewTable = DatabaseHelper.ExecuteQuery(query, parameter);
            
            if (reviewTable != null && reviewTable.Rows.Count > 0)
            {
                // 已有评价，显示评价内容
                DataRow review = reviewTable.Rows[0];
                int rating = Convert.ToInt32(review["Rating"]);
                string comments = review["Comments"] != DBNull.Value ? review["Comments"].ToString() : "未填写评价内容";
                DateTime reviewDate = Convert.ToDateTime(review["ReviewDate"]);
                
                // 保存ReviewID到隐藏字段，用于删除操作
                int reviewID = Convert.ToInt32(review["ReviewID"]);
                hfReviewID.Value = reviewID.ToString();
                
                // 显示评价内容
                System.Text.StringBuilder stars = new System.Text.StringBuilder();
                for (int i = 1; i <= 5; i++)
                {
                    if (i <= rating)
                    {
                        stars.Append("<i class=\"fas fa-star\"></i> ");
                    }
                    else
                    {
                        stars.Append("<i class=\"far fa-star\"></i> ");
                    }
                }
                
                litMyRatingStars.Text = stars.ToString();
                lblMyRating.Text = rating.ToString();
                lblMyReviewComments.Text = comments;
                lblMyReviewDate.Text = reviewDate.ToString("yyyy-MM-dd");
                
                // 显示我的评价面板，隐藏添加评价面板
                pnlMyReview.Visible = true;
                pnlAddReview.Visible = false;
                
                System.Diagnostics.Debug.WriteLine($"已有评价，评分: {rating}, 日期: {reviewDate:yyyy-MM-dd}");
            }
            else
            {
                // 无评价，显示添加评价面板
                pnlMyReview.Visible = false;
                pnlAddReview.Visible = true;
                System.Diagnostics.Debug.WriteLine($"评价面板显示状态: {pnlAddReview.Visible}");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine("检查评价状态时出错: " + ex.Message);
            // 出错时不显示评价面板
            pnlAddReview.Visible = false;
            pnlMyReview.Visible = false;
        }
    }

    /// <summary>
    /// 提交评价按钮点击事件
    /// </summary>
    protected void btnSubmitReview_Click(object sender, EventArgs e)
    {
        if (!Page.IsValid)
        {
            return;
        }

        try
        {
            // 检查selectedRecordID是否有效
            int currentRecordID = selectedRecordID;  // 从属性获取值，实际是从隐藏字段获取
            System.Diagnostics.Debug.WriteLine($"提交评价 - 从隐藏字段获取记录ID: {currentRecordID}, 直接值: {hfSelectedRecordID.Value}");
            
            if (currentRecordID <= 0)
            {
                lblMessage.Text = "维修记录ID无效，无法提交评价。";
                System.Diagnostics.Debug.WriteLine($"提交评价失败 - 无效的记录ID: {currentRecordID}");
                return;
            }

            // 检查shopID是否有效
            int currentShopID = shopID;  // 从属性获取值
            System.Diagnostics.Debug.WriteLine($"提交评价 - 从隐藏字段获取商店ID: {currentShopID}, 直接值: {hfSelectedShopID.Value}");
            
            if (currentShopID <= 0)
            {
                lblMessage.Text = "维修店ID无效，无法提交评价。";
                System.Diagnostics.Debug.WriteLine($"提交评价失败 - 无效的商店ID: {currentShopID}");
                return;
            }

            int rating = Convert.ToInt32(rblRating.SelectedValue);
            string comments = txtComments.Text.Trim();
            System.Diagnostics.Debug.WriteLine($"提交评价 - 评分: {rating}, 评论: {comments}");

            // 提交评价
            int reviewID = AppointmentManager.AddReview(currentRecordID, ownerID, currentShopID, rating, comments);
            System.Diagnostics.Debug.WriteLine($"评价提交结果 - 评价ID: {reviewID}");
            
            if (reviewID > 0)
            {
                // 处理照片上传
                if (fuPhotos.HasFiles)
                {
                    System.Diagnostics.Debug.WriteLine($"发现上传的照片: {fuPhotos.PostedFiles.Count} 个文件");
                    int uploadedCount = PhotoManager.SaveReviewPhotos(reviewID, Request.Files);
                    System.Diagnostics.Debug.WriteLine($"成功上传照片数量: {uploadedCount}");
                    
                    if (uploadedCount > 0)
                    {
                        lblMessage.Text = $"评价提交成功！上传了 {uploadedCount} 张照片。";
                    }
                    else
                    {
                        lblMessage.Text = "评价提交成功！照片上传失败，请确保照片格式正确且不超过5MB。";
                    }
                }
                else
                {
                    lblMessage.Text = "评价提交成功！";
                }
                
                lblMessage.CssClass = "text-success";
                
                // 隐藏评价表单
                pnlAddReview.Visible = false;
                
                // 显示已提交的评价内容
                litMyRatingStars.Text = "";
                for (int i = 1; i <= 5; i++)
                {
                    if (i <= rating)
                    {
                        litMyRatingStars.Text += "<i class=\"fas fa-star\"></i> ";
                    }
                    else
                    {
                        litMyRatingStars.Text += "<i class=\"far fa-star\"></i> ";
                    }
                }
                
                lblMyRating.Text = rating.ToString();
                lblMyReviewComments.Text = comments;
                lblMyReviewDate.Text = DateTime.Now.ToString("yyyy-MM-dd");
                
                // 显示我的评价面板
                pnlMyReview.Visible = true;
                
                // 重新加载照片区域
                LoadReviewPhotos(currentRecordID);
                
                System.Diagnostics.Debug.WriteLine("评价提交成功");
            }
            else
            {
                lblMessage.Text = "评价提交失败，可能是该维修记录已经评价过或不存在。";
                lblMessage.CssClass = "text-danger";
                System.Diagnostics.Debug.WriteLine("评价提交失败 - AddReview返回-1");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine("提交评价时出错: " + ex.Message + "\n" + ex.StackTrace);
            lblMessage.Text = "评价提交时发生错误，请稍后再试。";
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 返回列表按钮点击事件
    /// </summary>
    protected void btnBackToList_Click(object sender, EventArgs e)
    {
        pnlRecordDetails.Visible = false;
        pnlServiceRecords.Visible = true;
        lblMessage.Text = string.Empty;
    }

    /// <summary>
    /// 返回控制台按钮点击事件
    /// </summary>
    protected void btnBackToDashboard_Click(object sender, EventArgs e)
    {
        Response.Redirect("~/CarOwner/Dashboard.aspx");
    }

    /// <summary>
    /// 加载评价照片
    /// </summary>
    /// <param name="recordID">维修记录ID</param>
    private void LoadReviewPhotos(int recordID)
    {
        try
        {
            string query = @"SELECT rp.PhotoID, rp.FilePath 
                            FROM ReviewPhotos rp
                            INNER JOIN Reviews r ON rp.ReviewID = r.ReviewID
                            WHERE r.ServiceRecordID = @RecordID";
            SqlParameter parameter = new SqlParameter("@RecordID", recordID);
            DataTable photosTable = DatabaseHelper.ExecuteQuery(query, parameter);
            
            if (photosTable != null && photosTable.Rows.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"找到 {photosTable.Rows.Count} 张评价照片");
                
                // 创建照片预览区域的HTML
                System.Text.StringBuilder sb = new System.Text.StringBuilder();
                sb.Append("<div class=\"review-photos mt-3\">");
                sb.Append("<h6>评价照片</h6>");
                sb.Append("<div class=\"row\">");
                
                foreach (DataRow row in photosTable.Rows)
                {
                    string photoPath = row["FilePath"].ToString();
                    sb.AppendFormat("<div class=\"col-md-4 mb-2\">");
                    sb.AppendFormat("<a href=\"{0}\" target=\"_blank\">", ResolveUrl(photoPath));
                    sb.AppendFormat("<img src=\"{0}\" class=\"img-thumbnail\" style=\"max-height:200px;\" />", ResolveUrl(photoPath));
                    sb.Append("</a>");
                    sb.Append("</div>");
                }
                
                sb.Append("</div></div>");
                
                // 找到panel中插入照片预览区域的合适位置
                Panel reviewPhotosPanel = new Panel();
                reviewPhotosPanel.CssClass = "review-photos-container";
                reviewPhotosPanel.Controls.Add(new LiteralControl(sb.ToString()));
                
                // 将照片预览区域添加到页面
                pnlRecordDetails.Controls.AddAt(pnlRecordDetails.Controls.IndexOf(btnBackToList), reviewPhotosPanel);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("没有找到评价照片");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载评价照片时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除评价按钮点击事件
    /// </summary>
    protected void btnDeleteReview_Click(object sender, EventArgs e)
    {
        try
        {
            // 获取评价ID
            int reviewID;
            if (!int.TryParse(hfReviewID.Value, out reviewID) || reviewID <= 0)
            {
                lblMessage.Text = "评价ID无效，无法删除评价。";
                lblMessage.CssClass = "text-danger";
                return;
            }
            
            // 删除评价
            bool success = ShopManager.DeleteReview(reviewID, ownerID, false);
            
            if (success)
            {
                // 删除成功，显示成功消息
                lblMessage.Text = "评价已成功删除。";
                lblMessage.CssClass = "text-success";
                
                // 隐藏评价面板，显示添加评价面板
                pnlMyReview.Visible = false;
                pnlAddReview.Visible = true;
                
                // 清空评价ID
                hfReviewID.Value = "";
            }
            else
            {
                // 删除失败，显示错误消息
                lblMessage.Text = "删除评价失败，可能是该评价不存在或您没有权限删除。";
                lblMessage.CssClass = "text-danger";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine("删除评价时出错: " + ex.Message);
            lblMessage.Text = "删除评价时发生错误，请稍后再试。";
            lblMessage.CssClass = "text-danger";
        }
    }
} 