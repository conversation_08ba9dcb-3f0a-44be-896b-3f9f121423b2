using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class CarOwner_CheckPhoneNumberColumn : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            CheckPhoneNumberColumn();
        }
    }

    private void CheckPhoneNumberColumn()
    {
        try
        {
            string connectionString = ConfigurationManager.ConnectionStrings["CarRepairServiceDB"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                
                // 检查PhoneNumber列是否存在
                string query = @"SELECT COUNT(1) 
                               FROM INFORMATION_SCHEMA.COLUMNS 
                               WHERE TABLE_NAME = 'RepairShops' 
                               AND COLUMN_NAME = 'PhoneNumber'";
                
                SqlCommand command = new SqlCommand(query, connection);
                int result = Convert.ToInt32(command.ExecuteScalar());
                
                if (result > 0)
                {
                    lblResult.Text = "PhoneNumber列存在于RepairShops表中。";
                    
                    // 尝试添加一些电话号码
                    string updateQuery = @"UPDATE RepairShops 
                                        SET PhoneNumber = '010-' + CAST(ABS(CHECKSUM(NEWID())) % 10000000 AS VARCHAR(8)) 
                                        WHERE PhoneNumber IS NULL";
                    
                    SqlCommand updateCommand = new SqlCommand(updateQuery, connection);
                    int updateResult = updateCommand.ExecuteNonQuery();
                    
                    lblUpdateResult.Text = $"已更新 {updateResult} 条记录的电话号码。";
                    divUpdateResult.Visible = true;
                    
                    // 查询一些示例数据
                    string selectQuery = "SELECT TOP 5 ShopID, ShopName, Address, PhoneNumber FROM RepairShops";
                    SqlCommand selectCommand = new SqlCommand(selectQuery, connection);
                    
                    SqlDataAdapter adapter = new SqlDataAdapter(selectCommand);
                    DataTable dataTable = new DataTable();
                    adapter.Fill(dataTable);
                    
                    gvShops.DataSource = dataTable;
                    gvShops.DataBind();
                }
                else
                {
                    lblResult.Text = "PhoneNumber列不存在于RepairShops表中。";
                    
                    // 尝试添加PhoneNumber列
                    string alterQuery = "ALTER TABLE RepairShops ADD PhoneNumber NVARCHAR(50) NULL";
                    
                    try
                    {
                        SqlCommand alterCommand = new SqlCommand(alterQuery, connection);
                        alterCommand.ExecuteNonQuery();
                        lblAddColumnResult.Text = "成功添加PhoneNumber列到RepairShops表。";
                        divAddColumnResult.Visible = true;
                    }
                    catch (Exception ex)
                    {
                        lblAddColumnResult.Text = $"添加PhoneNumber列时出错: {ex.Message}";
                        divAddColumnResult.Visible = true;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            lblError.Text = $"发生错误: {ex.Message}";
            divError.Visible = true;
        }
    }
} 