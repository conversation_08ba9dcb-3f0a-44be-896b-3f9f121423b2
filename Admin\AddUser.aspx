<%@ Page Title="添加用户" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="Admin_AddUser" Codebehind="AddUser.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
        .password-strength-meter {
            height: 5px;
            margin-top: 5px;
            margin-bottom: 15px;
            background-color: #eee;
        }
        .password-strength-meter div {
            height: 100%;
            width: 0%;
            transition: width 0.3s;
        }
        .strength-weak { background-color: #f44336; width: 25% !important; }
        .strength-medium { background-color: #ff9800; width: 50% !important; }
        .strength-good { background-color: #4caf50; width: 75% !important; }
        .strength-strong { background-color: #4CAF50; width: 100% !important; }
        .validation-summary { margin-bottom: 15px; }
    </style>
    <script type="text/javascript">
        // 密码强度检查
        function checkPasswordStrength(password) {
            var strengthMeter = document.getElementById('passwordStrengthMeter');
            var strengthText = document.getElementById('passwordStrengthText');
            
            if (!password) {
                strengthMeter.className = '';
                strengthText.innerHTML = '';
                return;
            }

            // 计算密码强度
            var strength = 0;
            
            // 长度检查
            if (password.length >= 8) strength += 1;
            if (password.length >= 12) strength += 1;
            
            // 复杂度检查
            if (password.match(/[a-z]+/)) strength += 1;
            if (password.match(/[A-Z]+/)) strength += 1;
            if (password.match(/[0-9]+/)) strength += 1;
            if (password.match(/[^a-zA-Z0-9]+/)) strength += 1;
            
            // 显示强度
            var strengthClass = '';
            var strengthLabel = '';
            
            switch (true) {
                case (strength < 3):
                    strengthClass = 'strength-weak';
                    strengthLabel = '弱';
                    break;
                case (strength < 5):
                    strengthClass = 'strength-medium';
                    strengthLabel = '中';
                    break;
                case (strength < 7):
                    strengthClass = 'strength-good';
                    strengthLabel = '良好';
                    break;
                default:
                    strengthClass = 'strength-strong';
                    strengthLabel = '强';
                    break;
            }
            
            strengthMeter.className = strengthClass;
            strengthText.innerHTML = '密码强度: ' + strengthLabel;
        }
        
        // 验证手机号格式
        function validatePhone(source, args) {
            var phoneNumber = args.Value;
            var phoneRegex = /^1[3-9]\d{9}$/;
            args.IsValid = phoneNumber === '' || phoneRegex.test(phoneNumber);
        }
        
        // 验证用户名格式
        function validateUsername(source, args) {
            var username = args.Value;
            var usernameRegex = /^[a-zA-Z0-9_]{4,20}$/;
            args.IsValid = usernameRegex.test(username);
        }
        
        // 页面加载初始化
        function pageLoad() {
            var passwordInput = document.getElementById('<%= txtPassword.ClientID %>');
            if (passwordInput) {
                passwordInput.addEventListener('keyup', function() {
                    checkPasswordStrength(this.value);
                });
            }
        }
    </script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="container">
        <div class="row mb-3">
            <div class="col">
                <h2><i class="fas fa-user-plus"></i> 添加用户</h2>
                <hr />
                <asp:ValidationSummary ID="ValidationSummary1" runat="server" CssClass="alert alert-danger validation-summary" />
                <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">用户基本信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="ddlUserType">用户类型：</label>
                            <asp:DropDownList ID="ddlUserType" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlUserType_SelectedIndexChanged">
                                <asp:ListItem Value="CarOwner">车主</asp:ListItem>
                                <asp:ListItem Value="RepairShop">维修店</asp:ListItem>
                                <asp:ListItem Value="Admin">管理员</asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <div class="form-group">
                            <label for="txtUsername">用户名：</label>
                            <asp:TextBox ID="txtUsername" runat="server" CssClass="form-control" placeholder="请输入用户名 (4-20个字符，字母、数字和下划线)"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvUsername" runat="server" ControlToValidate="txtUsername" Display="Dynamic" CssClass="text-danger" ErrorMessage="请输入用户名"></asp:RequiredFieldValidator>
                            <asp:CustomValidator ID="cvUsername" runat="server" ControlToValidate="txtUsername" Display="Dynamic" CssClass="text-danger" ErrorMessage="用户名必须为4-20个字符，仅支持字母、数字和下划线" ClientValidationFunction="validateUsername"></asp:CustomValidator>
                        </div>
                        <div class="form-group">
                            <label for="txtPassword">密码：</label>
                            <asp:TextBox ID="txtPassword" runat="server" TextMode="Password" CssClass="form-control" placeholder="请输入密码 (至少8个字符)"></asp:TextBox>
                            <div class="password-strength-meter">
                                <div id="passwordStrengthMeter"></div>
                            </div>
                            <small id="passwordStrengthText" class="form-text"></small>
                            <asp:RequiredFieldValidator ID="rfvPassword" runat="server" ControlToValidate="txtPassword" Display="Dynamic" CssClass="text-danger" ErrorMessage="请输入密码"></asp:RequiredFieldValidator>
                            <asp:RegularExpressionValidator ID="revPassword" runat="server" ControlToValidate="txtPassword" Display="Dynamic" CssClass="text-danger" ErrorMessage="密码至少需要8个字符" ValidationExpression=".{8,}"></asp:RegularExpressionValidator>
                        </div>
                        <div class="form-group">
                            <label for="txtEmail">邮箱：</label>
                            <asp:TextBox ID="txtEmail" runat="server" TextMode="Email" CssClass="form-control" placeholder="请输入有效的邮箱地址"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvEmail" runat="server" ControlToValidate="txtEmail" Display="Dynamic" CssClass="text-danger" ErrorMessage="请输入邮箱"></asp:RequiredFieldValidator>
                            <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txtEmail" Display="Dynamic" CssClass="text-danger" ErrorMessage="邮箱格式不正确" ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"></asp:RegularExpressionValidator>
                        </div>
                        <div class="form-group">
                            <label for="txtPhoneNumber">联系电话：</label>
                            <asp:TextBox ID="txtPhoneNumber" runat="server" CssClass="form-control" placeholder="请输入11位手机号码"></asp:TextBox>
                            <asp:CustomValidator ID="cvPhoneNumber" runat="server" ControlToValidate="txtPhoneNumber" Display="Dynamic" CssClass="text-danger" ErrorMessage="请输入正确的手机号码（11位）" ClientValidationFunction="validatePhone"></asp:CustomValidator>
                        </div>
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <asp:CheckBox ID="chkIsActive" runat="server" Checked="true" Text="激活账户" CssClass="custom-control-input" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <asp:Panel ID="pnlCarOwner" runat="server">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">车主信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="txtFullName">姓名：</label>
                                <asp:TextBox ID="txtFullName" runat="server" CssClass="form-control" placeholder="请输入姓名"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvFullName" runat="server" ControlToValidate="txtFullName" Display="Dynamic" CssClass="text-danger" ErrorMessage="请输入姓名"></asp:RequiredFieldValidator>
                            </div>
                            <div class="form-group">
                                <label for="txtAddress">地址：</label>
                                <asp:TextBox ID="txtAddress" runat="server" CssClass="form-control" placeholder="请输入地址"></asp:TextBox>
                            </div>
                        </div>
                    </div>
                </asp:Panel>

                <asp:Panel ID="pnlRepairShop" runat="server" Visible="false">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">维修店信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="txtShopName">店铺名称：</label>
                                <asp:TextBox ID="txtShopName" runat="server" CssClass="form-control" placeholder="请输入店铺名称"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvShopName" runat="server" ControlToValidate="txtShopName" Display="Dynamic" CssClass="text-danger" ErrorMessage="请输入店铺名称"></asp:RequiredFieldValidator>
                            </div>
                            <div class="form-group">
                                <label for="txtShopAddress">店铺地址：</label>
                                <asp:TextBox ID="txtShopAddress" runat="server" CssClass="form-control" placeholder="请输入店铺地址"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvShopAddress" runat="server" ControlToValidate="txtShopAddress" Display="Dynamic" CssClass="text-danger" ErrorMessage="请输入店铺地址"></asp:RequiredFieldValidator>
                            </div>
                            <div class="form-group">
                                <label for="txtContactPerson">联系人：</label>
                                <asp:TextBox ID="txtContactPerson" runat="server" CssClass="form-control" placeholder="请输入联系人姓名"></asp:TextBox>
                            </div>
                            <div class="form-group">
                                <label for="txtDescription">店铺描述：</label>
                                <asp:TextBox ID="txtDescription" runat="server" TextMode="MultiLine" Rows="3" CssClass="form-control" placeholder="请输入店铺描述"></asp:TextBox>
                            </div>
                        </div>
                    </div>
                </asp:Panel>
                
                <asp:Panel ID="pnlAdmin" runat="server" Visible="false">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">管理员信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> 请注意，添加新管理员将授予该用户对系统的完全访问权限。
                            </div>
                        </div>
                    </div>
                </asp:Panel>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col">
                <asp:Button ID="btnSave" runat="server" Text="保存" CssClass="btn btn-primary" OnClick="btnSave_Click" />
                <a href="UserManagement.aspx" class="btn btn-secondary">返回</a>
            </div>
        </div>
    </div>
</asp:Content> 