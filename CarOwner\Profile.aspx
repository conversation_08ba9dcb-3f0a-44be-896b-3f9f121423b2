<%@ Page Title="个人信息" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="CarOwner_Profile" Codebehind="Profile.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
    <style type="text/css">
        .avatar-container {
            position: relative;
            width: 150px;
            height: 150px;
            margin: 0 auto;
            cursor: pointer;
        }
        
        .avatar-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            opacity: 0;
            transition: opacity 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        
        .avatar-container:hover .avatar-overlay {
            opacity: 1;
        }
        
        #avatarPreview {
            max-width: 100%;
            max-height: 200px;
            margin-top: 10px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col-md-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="Dashboard.aspx">控制台</a></li>
                        <li class="breadcrumb-item active">个人信息</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <div class="avatar-container" onclick="$('#fileAvatar').click();">
                            <asp:Image ID="imgAvatar" runat="server" CssClass="img-fluid rounded-circle mb-3" Width="150" Height="150" />
                            <div class="avatar-overlay">
                                <i class="fas fa-camera fa-2x"></i>
                            </div>
                        </div>
                        <h5 class="card-title mt-3">
                            <asp:Label ID="lblUsername" runat="server"></asp:Label>
                        </h5>
                        <p class="text-muted">车主</p>
                    </div>
                </div>
                
                <div class="list-group mb-4">
                    <a href="Dashboard.aspx" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt mr-2"></i>控制台
                    </a>
                    <a href="MyCars.aspx" class="list-group-item list-group-item-action">
                        <i class="fas fa-car mr-2"></i>我的车辆
                    </a>
                    <a href="Appointments.aspx" class="list-group-item list-group-item-action">
                        <i class="fas fa-calendar-check mr-2"></i>我的预约
                    </a>
                    <a href="ServiceHistory.aspx" class="list-group-item list-group-item-action">
                        <i class="fas fa-history mr-2"></i>维修记录
                    </a>
                    <a href="Profile.aspx" class="list-group-item list-group-item-action active">
                        <i class="fas fa-user-edit mr-2"></i>个人信息
                    </a>
                </div>
            </div>
            
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user-edit mr-2"></i>编辑个人信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="alert alert-success mb-4">
                            <asp:Literal ID="litMessage" runat="server"></asp:Literal>
                        </asp:Panel>
                        
                        <h5 class="border-bottom pb-2 mb-4">个人头像</h5>
                        
                        <div class="form-group">
                            <label>更改头像</label>
                            <div class="custom-file">
                                <asp:FileUpload ID="fileAvatar" runat="server" CssClass="custom-file-input" onchange="previewAvatar(this);" />
                                <label class="custom-file-label" for="fileAvatar">选择图片...</label>
                            </div>
                            <small class="form-text text-muted">支持JPG、JPEG、PNG和GIF格式，最大5MB</small>
                            <div id="avatarPreviewContainer" class="mt-3" style="display: none;">
                                <img id="avatarPreview" src="#" alt="头像预览" class="img-thumbnail" />
                                <div class="mt-2">
                                    <asp:Button ID="btnUploadAvatar" runat="server" Text="上传头像" CssClass="btn btn-primary btn-sm" OnClick="btnUploadAvatar_Click" CausesValidation="false" />
                                    <button type="button" class="btn btn-outline-secondary btn-sm ml-2" onclick="cancelAvatarUpload();">取消</button>
                                </div>
                            </div>
                        </div>
                        
                        <h5 class="border-bottom pb-2 mb-4 mt-5">个人资料</h5>
                        
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">用户名</label>
                            <div class="col-sm-9">
                                <asp:Label ID="lblDisplayUsername" runat="server" CssClass="form-control-plaintext"></asp:Label>
                            </div>
                        </div>
                        
                        <div class="form-group row">
                            <label for="txtFullName" class="col-sm-3 col-form-label">姓名</label>
                            <div class="col-sm-9">
                                <asp:TextBox ID="txtFullName" runat="server" CssClass="form-control" placeholder="输入您的真实姓名"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvFullName" runat="server" ControlToValidate="txtFullName" 
                                    ErrorMessage="请输入姓名" CssClass="text-danger" Display="Dynamic"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        
                        <div class="form-group row">
                            <label for="txtEmail" class="col-sm-3 col-form-label">电子邮箱</label>
                            <div class="col-sm-9">
                                <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" TextMode="Email" placeholder="<EMAIL>"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvEmail" runat="server" ControlToValidate="txtEmail" 
                                    ErrorMessage="请输入电子邮箱" CssClass="text-danger" Display="Dynamic"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txtEmail" 
                                    ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" 
                                    ErrorMessage="请输入有效的电子邮箱地址" CssClass="text-danger" Display="Dynamic"></asp:RegularExpressionValidator>
                            </div>
                        </div>
                        
                        <div class="form-group row">
                            <label for="txtPhoneNumber" class="col-sm-3 col-form-label">手机号码</label>
                            <div class="col-sm-9">
                                <asp:TextBox ID="txtPhoneNumber" runat="server" CssClass="form-control" placeholder="输入您的手机号码"></asp:TextBox>
                                <asp:RegularExpressionValidator ID="revPhoneNumber" runat="server" ControlToValidate="txtPhoneNumber" 
                                    ValidationExpression="^1[3-9]\d{9}$" 
                                    ErrorMessage="请输入有效的手机号码" CssClass="text-danger" Display="Dynamic"></asp:RegularExpressionValidator>
                            </div>
                        </div>

                        <h5 class="border-bottom pb-2 mb-4 mt-5">联系地址</h5>
                        
                        <div class="form-group row">
                            <label for="txtAddress" class="col-sm-3 col-form-label">详细地址</label>
                            <div class="col-sm-9">
                                <asp:TextBox ID="txtAddress" runat="server" CssClass="form-control" placeholder="街道、门牌号等"></asp:TextBox>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-4">
                            <i class="fas fa-info-circle mr-2"></i>
                            <strong>提示：</strong> 如需更新更多个人信息，请联系系统管理员。
                        </div>
                        
                        <div class="text-right mt-4">
                            <asp:Button ID="btnSave" runat="server" Text="保存修改" CssClass="btn btn-primary" OnClick="btnSave_Click" />
                            <a href="Dashboard.aspx" class="btn btn-outline-secondary ml-2">返回控制台</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script type="text/javascript">
        function previewAvatar(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                
                reader.onload = function (e) {
                    $('#avatarPreview').attr('src', e.target.result);
                    $('#avatarPreviewContainer').show();
                    $('.custom-file-label').text(input.files[0].name);
                }
                
                reader.readAsDataURL(input.files[0]);
            }
        }
        
        function cancelAvatarUpload() {
            $('#fileAvatar').val('');
            $('#avatarPreviewContainer').hide();
            $('.custom-file-label').text('选择图片...');
        }
        
        // 初始化Bootstrap自定义文件输入
        $(document).ready(function () {
            // 添加以下代码以确保Bootstrap的自定义文件输入功能正常工作
            $('.custom-file-input').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName);
            });
        });
    </script>
</asp:Content> 