<%@ Page Language="C#" AutoEventWireup="true" CodeFile="CheckPhoneNumberColumn.aspx.cs" Inherits="CarOwner_CheckPhoneNumberColumn" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>检查PhoneNumber列</title>
    <link href="https://cdn.staticfile.org/twitter-bootstrap/4.6.0/css/bootstrap.min.css" rel="stylesheet" />
</head>
<body>
    <form id="form1" runat="server">
        <div class="container mt-5">
            <h2>检查RepairShops表中的PhoneNumber列</h2>
            <hr />
            
            <div class="alert alert-info">
                <asp:Label ID="lblResult" runat="server" />
            </div>
            
            <div class="alert alert-success" runat="server" id="divUpdateResult" Visible="false">
                <asp:Label ID="lblUpdateResult" runat="server" />
            </div>
            
            <div class="alert alert-warning" runat="server" id="divAddColumnResult" Visible="false">
                <asp:Label ID="lblAddColumnResult" runat="server" />
            </div>
            
            <div class="alert alert-danger" runat="server" id="divError" Visible="false">
                <asp:Label ID="lblError" runat="server" />
            </div>
            
            <h4>示例数据:</h4>
            <asp:GridView ID="gvShops" runat="server" CssClass="table table-striped" AutoGenerateColumns="true" />
            
            <div class="mt-4">
                <a href="PaymentSimulation.aspx" class="btn btn-primary">返回验收支付页面</a>
            </div>
        </div>
    </form>
</body>
</html> 