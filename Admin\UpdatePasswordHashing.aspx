<%@ Page Title="更新密码加密" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeBehind="UpdatePasswordHashing.aspx.cs" Inherits="Admin_UpdatePasswordHashing" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-key mr-2"></i>更新密码加密</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            此工具将重新设置所有用户的密码为指定的默认值，并使用新的加密格式进行加密。
                            <br />
                            <strong>注意：</strong> 此操作不可逆，请确保您已经备份了数据库。
                        </div>

                        <div class="form-group">
                            <label for="txtDefaultPassword">默认密码</label>
                            <asp:TextBox ID="txtDefaultPassword" runat="server" CssClass="form-control" TextMode="Password" placeholder="为所有用户设置的默认密码"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDefaultPassword" runat="server" ControlToValidate="txtDefaultPassword"
                                ErrorMessage="请输入默认密码" CssClass="text-danger" Display="Dynamic"></asp:RequiredFieldValidator>
                        </div>

                        <div class="form-group">
                            <label for="txtConfirmDefaultPassword">确认默认密码</label>
                            <asp:TextBox ID="txtConfirmDefaultPassword" runat="server" CssClass="form-control" TextMode="Password" placeholder="确认默认密码"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvConfirmDefaultPassword" runat="server" ControlToValidate="txtConfirmDefaultPassword"
                                ErrorMessage="请确认默认密码" CssClass="text-danger" Display="Dynamic"></asp:RequiredFieldValidator>
                            <asp:CompareValidator ID="cvPassword" runat="server" ControlToValidate="txtConfirmDefaultPassword"
                                ControlToCompare="txtDefaultPassword" ErrorMessage="两次输入的密码不一致" Display="Dynamic"
                                CssClass="text-danger"></asp:CompareValidator>
                        </div>

                        <div class="form-group">
                            <label for="txtAdminPassword">管理员密码</label>
                            <asp:TextBox ID="txtAdminPassword" runat="server" CssClass="form-control" TextMode="Password" placeholder="输入您的管理员密码以确认操作"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvAdminPassword" runat="server" ControlToValidate="txtAdminPassword"
                                ErrorMessage="请输入管理员密码" CssClass="text-danger" Display="Dynamic"></asp:RequiredFieldValidator>
                        </div>

                        <asp:Label ID="lblMessage" runat="server" CssClass="mb-3 d-block"></asp:Label>

                        <div class="form-group">
                            <asp:Button ID="btnUpdatePasswords" runat="server" CssClass="btn btn-primary" Text="更新所有用户密码" OnClick="btnUpdatePasswords_Click" />
                            <a href="Dashboard.aspx" class="btn btn-secondary ml-2">返回仪表板</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content> 