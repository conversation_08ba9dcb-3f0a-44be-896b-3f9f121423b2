using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class CarOwner_MyCars : System.Web.UI.Page
{
    private int userID;
    private int ownerID;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取当前用户信息
        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "CarOwner")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        
        // 获取车主ID
        ownerID = CarManager.GetOwnerIDByUserID(userID);
        if (ownerID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            // 加载车辆数据
            LoadCarData();
        }
    }

    /// <summary>
    /// 加载车辆数据
    /// </summary>
    private void LoadCarData()
    {
        DataTable carsTable = CarManager.GetCarsByOwnerID(ownerID);
        gvCars.DataSource = carsTable;
        gvCars.DataBind();
    }

    /// <summary>
    /// 添加新车辆按钮点击事件
    /// </summary>
    protected void btnAddCar_Click(object sender, EventArgs e)
    {
        // 显示添加车辆面板
        pnlAddEditCar.Visible = true;
        litPanelTitle.Text = "添加新车辆";
        hfCarID.Value = string.Empty;
        
        // 清空表单
        txtMake.Text = string.Empty;
        txtModel.Text = string.Empty;
        txtYear.Text = DateTime.Now.Year.ToString();
        txtLicensePlate.Text = string.Empty;
        txtVIN.Text = string.Empty;
        txtColor.Text = string.Empty;

        lblMessage.Text = string.Empty;
    }

    /// <summary>
    /// 保存车辆信息按钮点击事件
    /// </summary>
    protected void btnSaveCar_Click(object sender, EventArgs e)
    {
        string make = txtMake.Text.Trim();
        string model = txtModel.Text.Trim();
        int year;
        if (!int.TryParse(txtYear.Text.Trim(), out year))
        {
            lblMessage.Text = "请输入有效的年份";
            return;
        }
        string licensePlate = txtLicensePlate.Text.Trim();
        string vin = txtVIN.Text.Trim();
        string color = txtColor.Text.Trim();

        string photoUrl = null;
        if (fuCarPhoto.HasFile)
        {
            string ext = System.IO.Path.GetExtension(fuCarPhoto.FileName);
            string fileName = Guid.NewGuid().ToString() + ext;
            string dir = Server.MapPath("~/Images/Cars/");
            if (!System.IO.Directory.Exists(dir)) System.IO.Directory.CreateDirectory(dir);
            string savePath = dir + fileName;
            fuCarPhoto.SaveAs(savePath);
            photoUrl = "/Images/Cars/" + fileName;
        }

        bool success = false;
        if (string.IsNullOrEmpty(hfCarID.Value)) // 新增
        {
            int carID = CarManager.AddCar(ownerID, make, model, year, licensePlate, vin, color, photoUrl);
            success = (carID > 0);
            if (success)
            {
                lblMessage.Text = "车辆添加成功！";
            }
            else
            {
                lblMessage.Text = "添加失败，请稍后再试。";
            }
        }
        else // 更新
        {
            int carID = Convert.ToInt32(hfCarID.Value);
            // 若未上传新图片，保持原有图片
            if (photoUrl == null)
            {
                var carTable = CarManager.GetCarByID(carID);
                if (carTable != null && carTable.Rows.Count > 0)
                    photoUrl = carTable.Rows[0]["PhotoUrl"] as string;
            }
            success = CarManager.UpdateCar(carID, make, model, year, licensePlate, vin, color, photoUrl);
            if (success)
            {
                lblMessage.Text = "车辆信息更新成功！";
            }
            else
            {
                lblMessage.Text = "更新失败，请稍后再试。";
            }
        }

        if (success)
        {
            LoadCarData();
            pnlAddEditCar.Visible = false;
        }
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    protected void btnCancel_Click(object sender, EventArgs e)
    {
        pnlAddEditCar.Visible = false;
        lblMessage.Text = string.Empty;
    }

    /// <summary>
    /// GridView的行命令事件
    /// </summary>
    protected void gvCars_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        int carID = Convert.ToInt32(e.CommandArgument);

        if (e.CommandName == "EditCar")
        {
            DataTable carTable = CarManager.GetCarByID(carID);
            if (carTable != null && carTable.Rows.Count > 0)
            {
                DataRow row = carTable.Rows[0];
                pnlAddEditCar.Visible = true;
                litPanelTitle.Text = "编辑车辆信息";
                hfCarID.Value = carID.ToString();
                txtMake.Text = row["Make"].ToString();
                txtModel.Text = row["Model"].ToString();
                txtYear.Text = row["Year"].ToString();
                txtLicensePlate.Text = row["LicensePlate"].ToString();
                txtVIN.Text = row["VIN"].ToString();
                txtColor.Text = row["Color"].ToString();
                // 显示图片预览
                imgCarPhotoPreview.ImageUrl = string.IsNullOrEmpty(row["PhotoUrl"] as string) ? "/Images/default-car.png" : row["PhotoUrl"].ToString();
                lblMessage.Text = string.Empty;
            }
        }
        else if (e.CommandName == "DeleteCar")
        {
            bool success = CarManager.DeleteCar(carID);
            if (success)
            {
                lblMessage.Text = "车辆删除成功！";
                LoadCarData();
            }
            else
            {
                lblMessage.Text = "无法删除此车辆，因为它有相关的维修预约记录。";
            }
        }
    }

    /// <summary>
    /// 返回控制台按钮点击事件
    /// </summary>
    protected void btnBackToDashboard_Click(object sender, EventArgs e)
    {
        Response.Redirect("~/CarOwner/Dashboard.aspx");
    }
} 