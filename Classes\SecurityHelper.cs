using System;
using System.Data;
using System.Data.SqlClient;
using System.Text.RegularExpressions;

/// <summary>
/// 安全辅助类，提供密码验证、登录尝试次数检查等安全相关功能
/// </summary>
public static class SecurityHelper
{
    /// <summary>
    /// 获取系统设置值
    /// </summary>
    public static string GetSystemSetting(string key, string defaultValue = "")
    {
        try
        {
            string query = "SELECT SettingValue FROM SystemSettings WHERE SettingKey = @SettingKey";
            SqlParameter parameter = new SqlParameter("@SettingKey", key);
            object result = DatabaseHelper.ExecuteScalar(query, parameter);
            string value = result != null && result != DBNull.Value ? result.ToString() : defaultValue;
            
            // 记录重要安全设置的读取
            if (key == "MaxLoginAttempts" || key == "LockoutDuration")
            {
                System.Diagnostics.Debug.WriteLine($"读取系统设置 {key}: {value}");
            }
            
            return value;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"读取系统设置 {key} 时出错: {ex.Message}");
            return defaultValue;
        }
    }

    /// <summary>
    /// 验证密码是否符合安全要求
    /// </summary>
    /// <param name="password">要验证的密码</param>
    /// <param name="errorMessage">错误信息</param>
    /// <returns>验证通过返回true，失败返回false</returns>
    public static bool ValidatePasswordStrength(string password, out string errorMessage)
    {
        errorMessage = string.Empty;

        // 获取密码安全设置
        int minLength = Convert.ToInt32(GetSystemSetting("MinPasswordLength", "8"));
        bool requireUppercase = GetSystemSetting("RequireUppercase", "true") == "true";
        bool requireDigit = GetSystemSetting("RequireDigit", "true") == "true";
        bool requireSpecialChar = GetSystemSetting("RequireSpecialChar", "false") == "true";

        // 检查密码长度
        if (string.IsNullOrEmpty(password) || password.Length < minLength)
        {
            errorMessage = $"密码长度不能少于{minLength}个字符";
            return false;
        }

        // 检查是否需要大写字母
        if (requireUppercase && !Regex.IsMatch(password, "[A-Z]"))
        {
            errorMessage = "密码必须包含至少一个大写字母";
            return false;
        }

        // 检查是否需要数字
        if (requireDigit && !Regex.IsMatch(password, "[0-9]"))
        {
            errorMessage = "密码必须包含至少一个数字";
            return false;
        }

        // 检查是否需要特殊字符
        if (requireSpecialChar && !Regex.IsMatch(password, "[!@#$%^&*(),.?\":{}|<>]"))
        {
            errorMessage = "密码必须包含至少一个特殊字符";
            return false;
        }

        return true;
    }

    /// <summary>
    /// 检查登录尝试次数并更新
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="isLoginSuccess">登录是否成功</param>
    /// <param name="remainingAttempts">剩余尝试次数</param>
    /// <returns>如果账户被锁定返回true，否则返回false</returns>
    public static bool CheckAndUpdateLoginAttempts(string username, bool isLoginSuccess, out int remainingAttempts)
    {
        remainingAttempts = 0;

        // 获取登录安全设置
        int maxAttempts = Convert.ToInt32(GetSystemSetting("MaxLoginAttempts", "5"));
        int lockDuration = Convert.ToInt32(GetSystemSetting("LockoutDuration", "30"));
        
        // 记录当前使用的设置
        System.Diagnostics.Debug.WriteLine($"当前安全设置: 最大尝试次数={maxAttempts}, 锁定时间(分钟)={lockDuration}");

        try
        {
            // 检查用户是否存在
            string checkUserQuery = "SELECT UserID FROM Users WHERE Username = @Username";
            SqlParameter checkParam = new SqlParameter("@Username", username);
            object userIDObj = DatabaseHelper.ExecuteScalar(checkUserQuery, checkParam);

            if (userIDObj == null || userIDObj == DBNull.Value)
            {
                // 用户不存在，不进行锁定检查
                remainingAttempts = maxAttempts;
                return false;
            }

            int userID = Convert.ToInt32(userIDObj);

            // 检查用户是否已被锁定
            string lockCheckQuery = @"SELECT LockoutEndTime FROM UserLogins 
                                    WHERE UserID = @UserID AND LockoutEndTime > GETDATE()";
            SqlParameter lockParam = new SqlParameter("@UserID", userID);
            object lockEndTimeObj = DatabaseHelper.ExecuteScalar(lockCheckQuery, lockParam);

            if (lockEndTimeObj != null && lockEndTimeObj != DBNull.Value)
            {
                // 用户已被锁定
                DateTime lockEndTime = Convert.ToDateTime(lockEndTimeObj);
                TimeSpan remainingTime = lockEndTime - DateTime.Now;
                remainingAttempts = 0;
                return true;
            }

            // 清除已过期的锁定状态
            string clearExpiredLockQuery = @"UPDATE UserLogins SET 
                                         LockoutEndTime = NULL
                                         WHERE UserID = @UserID 
                                         AND LockoutEndTime IS NOT NULL 
                                         AND LockoutEndTime <= GETDATE()";
            SqlParameter clearParam = new SqlParameter("@UserID", userID);
            DatabaseHelper.ExecuteNonQuery(clearExpiredLockQuery, clearParam);

            if (isLoginSuccess)
            {
                // 登录成功，重置登录尝试次数
                string resetQuery = @"UPDATE UserLogins SET 
                                    FailedAttempts = 0, 
                                    LastLoginAttempt = GETDATE(),
                                    LockoutEndTime = NULL
                                    WHERE UserID = @UserID;

                                    IF @@ROWCOUNT = 0
                                    INSERT INTO UserLogins (UserID, FailedAttempts, LastLoginAttempt)
                                    VALUES (@UserID, 0, GETDATE())";
                SqlParameter resetParam = new SqlParameter("@UserID", userID);
                DatabaseHelper.ExecuteNonQuery(resetQuery, resetParam);

                remainingAttempts = maxAttempts;
                return false;
            }
            else
            {
                // 登录失败，增加失败次数
                string failedQuery = @"UPDATE UserLogins SET 
                                    FailedAttempts = FailedAttempts + 1,
                                    LastLoginAttempt = GETDATE()
                                    WHERE UserID = @UserID;
                                    
                                    IF @@ROWCOUNT = 0
                                    INSERT INTO UserLogins (UserID, FailedAttempts, LastLoginAttempt)
                                    VALUES (@UserID, 1, GETDATE());
                                    
                                    SELECT FailedAttempts FROM UserLogins WHERE UserID = @UserID";

                SqlParameter failedParam = new SqlParameter("@UserID", userID);
                int failedAttempts = Convert.ToInt32(DatabaseHelper.ExecuteScalar(failedQuery, failedParam));

                remainingAttempts = maxAttempts - failedAttempts;

                // 如果达到最大尝试次数，锁定账户
                if (failedAttempts >= maxAttempts)
                {
                    string lockQuery = @"UPDATE UserLogins SET 
                                        LockoutEndTime = DATEADD(MINUTE, @LockDuration, GETDATE())
                                        WHERE UserID = @UserID";
                    SqlParameter[] lockParams =
                    {
                        new SqlParameter("@UserID", userID),
                        new SqlParameter("@LockDuration", lockDuration)
                    };
                    DatabaseHelper.ExecuteNonQuery(lockQuery, lockParams);
                    
                    // 记录账号锁定信息
                    DateTime lockoutEndTime = DateTime.Now.AddMinutes(lockDuration);
                    System.Diagnostics.Debug.WriteLine($"账户 {username} 已被锁定，解锁时间: {lockoutEndTime:yyyy-MM-dd HH:mm:ss}");
                    
                    return true;
                }

                return false;
            }
        }
        catch
        {
            // 发生错误时，为安全起见，返回账户不锁定，但剩余尝试次数为1
            remainingAttempts = 1;
            return false;
        }
    }

    /// <summary>
    /// 确保UserLogins表存在
    /// </summary>
    public static void EnsureUserLoginsTableExists()
    {
        string query = @"
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'UserLogins')
            BEGIN
                CREATE TABLE UserLogins (
                    UserID INT PRIMARY KEY,
                    FailedAttempts INT DEFAULT 0,
                    LastLoginAttempt DATETIME,
                    LockoutEndTime DATETIME,
                    FOREIGN KEY (UserID) REFERENCES Users(UserID)
                )
            END";

        DatabaseHelper.ExecuteNonQuery(query);
    }
    
    /// <summary>
    /// 清理所有过期的账号锁定
    /// </summary>
    /// <returns>清理的账号数量</returns>
    public static int ClearExpiredLockouts()
    {
        try
        {
            string clearQuery = @"UPDATE UserLogins SET 
                               LockoutEndTime = NULL
                               WHERE LockoutEndTime IS NOT NULL 
                               AND LockoutEndTime <= GETDATE();
                               
                               SELECT @@ROWCOUNT";
                               
            object result = DatabaseHelper.ExecuteScalar(clearQuery);
            int count = (result != null && result != DBNull.Value) ? Convert.ToInt32(result) : 0;
            
            if (count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"已清理 {count} 个过期的账号锁定");
            }
            
            return count;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"清理过期锁定时出错: {ex.Message}");
            return 0;
        }
    }
} 