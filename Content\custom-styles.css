/* 自定义样式表 - 汽车维修服务平台 */

/* 全局样式 */
:root {
  --primary-color: #3498db;
  --primary-dark: #2980b9;
  --secondary-color: #2ecc71;
  --accent-color: #f39c12;
  --danger-color: #e74c3c;
  --dark-color: #34495e;
  --light-color: #ecf0f1;
  --gray-color: #95a5a6;
  --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body {
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  color: #333;
  padding-bottom: 80px; /* 为固定页脚留出空间 */
}

/* 导航栏美化 */
.navbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-weight: bold;
  font-size: 1.4rem;
}

.nav-link {
  font-size: 1rem;
  margin: 0 3px;
  border-radius: 4px;
  transition: var(--transition);
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

/* 卡片美化 */
.card {
  border: none;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  margin-bottom: 25px;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
  font-weight: bold;
}

.card-body {
  padding: 2rem;
}

/* 按钮美化 */
.btn {
  border-radius: 30px;
  padding: 10px 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  transform: scaleY(0);
  transform-origin: bottom;
  transition: transform 0.4s;
  z-index: -1;
}

.btn:hover::after {
  transform: scaleY(1);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.btn-success {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-success:hover {
  background-color: #27ae60;
  border-color: #27ae60;
  box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);
}

.btn-info {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-info:hover {
  background-color: #e67e22;
  border-color: #e67e22;
  box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
}

.btn-block {
  padding: 12px;
}

/* 表单美化 */
.form-control {
  border-radius: 5px;
  padding: 12px;
  border: 1px solid #ddd;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* 修复下拉框中文字底部被截断的问题 */
select.form-control, 
.form-select,
.dropdown-menu,
.dropdown-item {
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 1.8;
  height: auto !important;
}

option {
  padding: 10px;
  line-height: 1.8;
}

/* 修复中文字体在选择框中的显示 */
select, option {
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 修复搜索按钮与输入框高度不一致的问题 */
.input-group {
  align-items: stretch;
}

.input-group .form-control {
  height: 46px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
}

.input-group .btn {
  height: 46px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  padding: 0.375rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-group-append {
  display: flex;
}

/* 搜索框特别调整 */
.input-group > .form-control, .input-group > .btn {
  border-radius: 0;
}

.input-group > .form-control:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

.input-group > .input-group-append > .btn {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  text-transform: none;
  padding-left: 20px;
  padding-right: 20px;
}

label {
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--dark-color);
}

/* 首页大横幅美化 */
.jumbotron {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  border-radius: 10px;
  padding: 3rem;
  box-shadow: var(--box-shadow);
  position: relative;
  overflow: hidden;
}

.jumbotron::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(255,255,255,0.05)" fill-opacity="1" d="M0,160L48,154.7C96,149,192,139,288,154.7C384,171,480,213,576,208C672,203,768,149,864,138.7C960,128,1056,160,1152,181.3C1248,203,1344,213,1392,218.7L1440,224L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  background-position: center bottom;
  background-repeat: no-repeat;
  opacity: 0.8;
}

.jumbotron .container {
  position: relative;
  z-index: 2;
}

.display-4 {
  font-weight: 700;
  margin-bottom: 20px;
}

.lead {
  font-size: 1.4rem;
}

/* 图标样式 */
.fas {
  transition: var(--transition);
}

.card:hover .fas {
  transform: scale(1.2);
}

/* 平台优势卡片 */
.media .fas {
  color: var(--primary-color);
}

/* 页脚美化 */
.footer {
  background-color: #2c3e50 !important;
  color: white !important;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.footer .text-muted {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .jumbotron {
    padding: 2rem;
  }
  
  .card-body {
    padding: 1.5rem;
  }
  
  .btn {
    padding: 8px 16px;
  }
  
  .display-4 {
    font-size: 2.5rem;
  }
  
  .lead {
    font-size: 1.2rem;
  }
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

/* 页面切换动画 */
.container.mt-4 {
  animation: fadeIn 0.4s;
}

/* 提示消息样式 */
.text-danger {
  font-weight: 500;
  padding: 5px 0;
} 