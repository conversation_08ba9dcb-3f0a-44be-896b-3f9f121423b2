﻿<%@ Page Title="搜索" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="Search" Codebehind="Search.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>搜索维修店</h2>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-8">
                <div class="input-group">
                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="输入维修店名称或地址关键词进行搜索"></asp:TextBox>
                    <div class="input-group-append">
                        <asp:Button ID="btnSearch" runat="server" Text="搜索" CssClass="btn btn-primary" OnClick="btnSearch_Click" />
                    </div>
                </div>
                <small class="form-text text-muted mt-1">您可以输入维修店名称或地址关键词（如：城市名、区域名、街道名）进行搜索</small>
            </div>
            <div class="col-md-4">
                <asp:DropDownList ID="ddlFilter" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlFilter_SelectedIndexChanged">
                    <asp:ListItem Value="0" Text="全部" Selected="True"></asp:ListItem>
                    <asp:ListItem Value="1" Text="评分最高"></asp:ListItem>
                    <asp:ListItem Value="2" Text="距离最近"></asp:ListItem>
                </asp:DropDownList>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <asp:Repeater ID="rptShops" runat="server" OnItemCommand="rptShops_ItemCommand">
                    <HeaderTemplate>
                        <div class="list-group">
                    </HeaderTemplate>
                    <ItemTemplate>
                        <div class="list-group-item">
                            <div class="row">
                                <div class="col-md-2">
                                    <img src='<%# Eval("LogoUrl") %>' alt="Shop Logo" class="img-fluid" />
                                </div>
                                <div class="col-md-8">
                                    <h5 class="mb-1"><%# Eval("ShopName") %></h5>
                                    <p class="mb-1">地址: <%# Eval("Address") %></p>
                                    <p class="mb-1">电话: <%# Eval("Phone") %></p>
                                    <p class="mb-1">
                                        <small class="text-muted">评分: 
                                            <span class="text-warning">
                                                <%# GetRatingStars(Convert.ToDouble(Eval("Rating"))) %>
                                            </span>
                                            <%# Eval("Rating", "{0:F1}") %>/5.0 (<%# Eval("ReviewCount") %>条评价)
                                        </small>
                                    </p>
                                </div>
                                <div class="col-md-2 text-right">
                                    <asp:LinkButton ID="lnkViewDetails" runat="server" CssClass="btn btn-primary btn-sm" 
                                        CommandName="ViewShop" CommandArgument='<%# Eval("ShopID") %>'>
                                        查看详情
                                    </asp:LinkButton>
                                </div>
                            </div>
                        </div>
                    </ItemTemplate>
                    <FooterTemplate>
                        </div>
                    </FooterTemplate>
                </asp:Repeater>

                <asp:Panel ID="pnlNoResults" runat="server" Visible="false">
                    <div class="alert alert-info">
                        没有找到符合条件的维修店，请尝试其他搜索词。
                    </div>
                </asp:Panel>
            </div>
        </div>
    </div>
</asp:Content> 
