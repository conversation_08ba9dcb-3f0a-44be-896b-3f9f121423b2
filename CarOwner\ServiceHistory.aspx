<%@ Page Title="维修记录" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="CarOwner_ServiceHistory" Codebehind="ServiceHistory.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2><i class="fas fa-history"></i> 维修记录</h2>
                <hr />
            </div>
        </div>
        
        <asp:HiddenField ID="hfSelectedRecordID" runat="server" Value="0" />
        <asp:HiddenField ID="hfSelectedShopID" runat="server" Value="0" />
        <asp:HiddenField ID="hfReviewID" runat="server" Value="0" />
        
        <asp:Panel ID="pnlServiceRecords" runat="server">
            <div class="row mb-3">
                <div class="col">
                    <div class="form-row align-items-center">
                        <div class="col-auto">
                            <label for="ddlCarFilter">按车辆筛选：</label>
                        </div>
                        <div class="col-auto">
                            <asp:DropDownList ID="ddlCarFilter" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlCarFilter_SelectedIndexChanged"></asp:DropDownList>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <asp:GridView ID="gvServiceRecords" runat="server" AutoGenerateColumns="False"
                        CssClass="table table-striped table-hover" DataKeyNames="RecordID"
                        OnRowCommand="gvServiceRecords_RowCommand" EmptyDataText="暂无维修记录">
                        <Columns>
                            <asp:BoundField DataField="CompletedDate" HeaderText="完成时间" DataFormatString="{0:yyyy-MM-dd}" />
                            <asp:BoundField DataField="CarInfo" HeaderText="车辆" />
                            <asp:BoundField DataField="ShopName" HeaderText="维修店" />
                            <asp:BoundField DataField="ServiceName" HeaderText="服务项目" />
                            <asp:BoundField DataField="TotalCost" HeaderText="总费用" DataFormatString="¥{0:N2}" />
                            <asp:TemplateField HeaderText="操作">
                                <ItemTemplate>
                                    <asp:LinkButton ID="lbtnView" runat="server" CssClass="btn btn-sm btn-info mr-1"
                                        CommandName="ViewRecord" CommandArgument='<%# Eval("RecordID") %>' ToolTip="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </asp:LinkButton>
                                </ItemTemplate>
                            </asp:TemplateField>
                        </Columns>
                    </asp:GridView>
                </div>
            </div>
        </asp:Panel>

        <asp:Panel ID="pnlRecordDetails" runat="server" Visible="false">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">维修记录详情</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <dl class="row">
                                <dt class="col-sm-4">服务项目：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblServiceName" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">预约时间：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblAppointmentDate" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">完成时间：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblCompletedDate" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6>车辆信息</h6>
                            <dl class="row">
                                <dt class="col-sm-4">车辆：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblCarInfo" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">维修店：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblShopName" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">技师：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblTechnicianName" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <hr />

                    <div class="row">
                        <div class="col-md-6">
                            <h6>维修内容</h6>
                            <dl class="row">
                                <dt class="col-sm-4">诊断结果：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblDiagnosisDetails" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">更换零件：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblPartsReplaced" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6>费用明细</h6>
                            <dl class="row">
                                <dt class="col-sm-4">工时费：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblLaborCost" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">零件费：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblPartsCost" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">总费用：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblTotalCost" runat="server" CssClass="font-weight-bold"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <!-- 添加已提交评价显示区域 -->
                    <asp:Panel ID="pnlMyReview" runat="server" Visible="false" CssClass="mt-4">
                        <div class="card">
                            <div class="card-header bg-warning">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><i class="fas fa-star"></i> 我的评价</h6>
                                    <asp:LinkButton ID="btnDeleteReview" runat="server" CssClass="btn btn-sm btn-danger" 
                                        OnClick="btnDeleteReview_Click" OnClientClick="return confirm('确定要删除此评价吗？此操作不可恢复。');">
                                        <i class="fas fa-trash-alt"></i> 删除评价
                                    </asp:LinkButton>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-warning mb-2">
                                            <asp:Literal ID="litMyRatingStars" runat="server"></asp:Literal>
                                        </div>
                                        <div class="h4">
                                            <asp:Label ID="lblMyRating" runat="server"></asp:Label> <small>/ 5</small>
                                        </div>
                                        <small class="text-muted">
                                            评价日期：<asp:Label ID="lblMyReviewDate" runat="server"></asp:Label>
                                        </small>
                                    </div>
                                    <div class="col-md-9">
                                        <blockquote class="blockquote">
                                            <p class="mb-0">
                                                <asp:Label ID="lblMyReviewComments" runat="server"></asp:Label>
                                            </p>
                                        </blockquote>
                                    </div>
                                </div>
                                <div class="review-photos-container mt-3">
                                    <!-- 评价照片显示区域由代码生成 -->
                                </div>
                            </div>
                        </div>
                    </asp:Panel>

                    <div class="mt-4">
                        <asp:Panel ID="pnlAddReview" runat="server" Visible="false" CssClass="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">为这次维修评价</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="rblRating">评分：</label>
                                    <asp:RadioButtonList ID="rblRating" runat="server" RepeatDirection="Horizontal" CssClass="rating-list">
                                        <asp:ListItem Value="1">1分</asp:ListItem>
                                        <asp:ListItem Value="2">2分</asp:ListItem>
                                        <asp:ListItem Value="3">3分</asp:ListItem>
                                        <asp:ListItem Value="4">4分</asp:ListItem>
                                        <asp:ListItem Value="5" Selected="True">5分</asp:ListItem>
                                    </asp:RadioButtonList>
                                    <asp:RequiredFieldValidator ID="rfvRating" runat="server" ControlToValidate="rblRating"
                                        ErrorMessage="请选择评分" Display="Dynamic" CssClass="text-danger" ValidationGroup="Review"></asp:RequiredFieldValidator>
                                </div>
                                <div class="form-group">
                                    <label for="txtComments">评价内容：</label>
                                    <asp:TextBox ID="txtComments" runat="server" TextMode="MultiLine" Rows="3"
                                        CssClass="form-control" placeholder="请输入您对此次维修的评价"></asp:TextBox>
                                </div>
                                <div class="form-group">
                                    <label for="fuPhotos">上传照片：</label>
                                    <div class="custom-file mb-2">
                                        <asp:FileUpload ID="fuPhotos" runat="server" CssClass="custom-file-input" AllowMultiple="true" onchange="previewImages(this)" />
                                        <label class="custom-file-label" for="fuPhotos">选择照片...</label>
                                    </div>
                                    <small class="form-text text-muted">
                                        您可以上传维修前后的照片。允许的格式：JPG、PNG、GIF；最大文件大小：5MB；最多3张照片。
                                    </small>
                                    <div id="photoPreview" class="mt-2 row" runat="server">
                                        <!-- 预览区域 -->
                                    </div>
                                </div>
                                <div class="form-group">
                                    <asp:Button ID="btnSubmitReview" runat="server" Text="提交评价" CssClass="btn btn-primary"
                                        OnClick="btnSubmitReview_Click" ValidationGroup="Review" />
                                </div>
                            </div>
                        </asp:Panel>

                        <asp:Button ID="btnBackToList" runat="server" Text="返回列表" CssClass="btn btn-secondary" OnClick="btnBackToList_Click" />
                    </div>
                </div>
            </div>
        </asp:Panel>

        <div class="row mt-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col">
                <asp:Button ID="btnBackToDashboard" runat="server" Text="返回控制台" CssClass="btn btn-secondary" OnClick="btnBackToDashboard_Click" />
            </div>
        </div>
    </div>

    <style>
        .rating-list label {
            margin-right: 10px;
            padding: 5px 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }

        .rating-list input[type=radio] {
            margin-right: 5px;
        }
    </style>
    
    <script type="text/javascript">
        $(document).ready(function() {
            // 自定义文件输入框的展示
            $(".custom-file-input").on("change", function() {
                var fileCount = $(this)[0].files.length;
                if (fileCount > 0) {
                    if (fileCount > 1) {
                        $(this).next(".custom-file-label").html("已选择 " + fileCount + " 个文件");
                    } else {
                        $(this).next(".custom-file-label").html($(this)[0].files[0].name);
                    }
                } else {
                    $(this).next(".custom-file-label").html("选择照片...");
                }
            });
        });
        
        // 图片预览功能
        function previewImages(input) {
            var previewDiv = document.getElementById('<%= photoPreview.ClientID %>');
            previewDiv.innerHTML = '';
            
            if (input.files && input.files.length > 0) {
                for (var i = 0; i < Math.min(input.files.length, 3); i++) {
                    var reader = new FileReader();
                    var file = input.files[i];
                    
                    reader.onload = (function(file, index) {
                        return function(e) {
                            var col = document.createElement('div');
                            col.className = 'col-md-4 mb-2';
                            
                            var img = document.createElement('img');
                            img.src = e.target.result;
                            img.className = 'img-thumbnail';
                            img.style = 'max-height: 200px; width: auto;';
                            img.title = file.name;
                            
                            col.appendChild(img);
                            previewDiv.appendChild(col);
                        };
                    })(file, i);
                    
                    reader.readAsDataURL(file);
                }
            }
        }
    </script>
</asp:Content> 