using System;
using System.Data.SqlClient;

/// <summary>
/// 验证码辅助类
/// </summary>
public static class VerificationHelper
{
    /// <summary>
    /// 生成随机验证码
    /// </summary>
    /// <param name="length">验证码长度</param>
    /// <returns>随机验证码</returns>
    public static string GenerateVerificationCode(int length = 6)
    {
        Random random = new Random();
        return random.Next((int)Math.Pow(10, length - 1), (int)Math.Pow(10, length) - 1).ToString();
    }

    /// <summary>
    /// 验证手机验证码
    /// </summary>
    /// <param name="phoneNumber">手机号</param>
    /// <param name="code">验证码</param>
    /// <returns>验证成功返回true，失败返回false</returns>
    public static bool VerifyCode(string phoneNumber, string code)
    {
        try
        {
            // 查询最近发送的未使用且未过期的验证码
            string query = @"SELECT TOP 1 ID FROM VerificationCodes 
                          WHERE PhoneNumber = @PhoneNumber 
                          AND Code = @Code 
                          AND IsUsed = 0 
                          AND ExpireTime > GETDATE()
                          ORDER BY CreateTime DESC";

            SqlParameter[] parameters =
            {
                new SqlParameter("@PhoneNumber", phoneNumber),
                new SqlParameter("@Code", code)
            };

            object result = DatabaseHelper.ExecuteScalar(query, parameters);
            if (result == null || result == DBNull.Value)
            {
                return false;
            }

            // 标记验证码为已使用
            int codeID = Convert.ToInt32(result);
            string updateQuery = "UPDATE VerificationCodes SET IsUsed = 1 WHERE ID = @ID";
            SqlParameter updateParam = new SqlParameter("@ID", codeID);
            DatabaseHelper.ExecuteNonQuery(updateQuery, updateParam);

            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"验证码验证错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 记录验证码发送
    /// </summary>
    /// <param name="phoneNumber">手机号</param>
    /// <param name="code">验证码</param>
    /// <param name="expireMinutes">过期时间（分钟）</param>
    /// <returns>记录成功返回true，失败返回false</returns>
    public static bool LogVerificationCode(string phoneNumber, string code, int expireMinutes = 10)
    {
        try
        {
            // 计算过期时间
            DateTime expireTime = DateTime.Now.AddMinutes(expireMinutes);

            // 记录验证码
            string logQuery = @"INSERT INTO VerificationCodes (PhoneNumber, Code, ExpireTime, IsUsed) 
                             VALUES (@PhoneNumber, @Code, @ExpireTime, 0)";

            SqlParameter[] logParams =
            {
                new SqlParameter("@PhoneNumber", phoneNumber),
                new SqlParameter("@Code", code),
                new SqlParameter("@ExpireTime", expireTime)
            };

            DatabaseHelper.ExecuteNonQuery(logQuery, logParams);
            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"记录验证码错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 清理过期验证码
    /// </summary>
    /// <returns>清理的记录数</returns>
    public static int ClearExpiredCodes()
    {
        try
        {
            // 删除过期的验证码记录
            string query = @"DELETE FROM VerificationCodes WHERE ExpireTime < GETDATE();
                          SELECT @@ROWCOUNT";

            object result = DatabaseHelper.ExecuteScalar(query);
            return result != null && result != DBNull.Value ? Convert.ToInt32(result) : 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"清理过期验证码错误: {ex.Message}");
            return 0;
        }
    }

    /// <summary>
    /// 检查是否可以发送新验证码
    /// </summary>
    /// <param name="phoneNumber">手机号</param>
    /// <param name="intervalSeconds">两次发送间隔（秒）</param>
    /// <returns>可以发送返回true，否则返回false</returns>
    public static bool CanSendNewCode(string phoneNumber, int intervalSeconds = 60)
    {
        try
        {
            // 检查最近发送的验证码时间
            string query = @"SELECT TOP 1 CreateTime FROM VerificationCodes 
                          WHERE PhoneNumber = @PhoneNumber 
                          ORDER BY CreateTime DESC";

            SqlParameter parameter = new SqlParameter("@PhoneNumber", phoneNumber);

            object result = DatabaseHelper.ExecuteScalar(query, parameter);
            if (result == null || result == DBNull.Value)
            {
                return true;
            }

            DateTime lastSentTime = Convert.ToDateTime(result);
            TimeSpan interval = DateTime.Now - lastSentTime;

            return interval.TotalSeconds >= intervalSeconds;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"检查验证码发送间隔错误: {ex.Message}");
            return true; // 出错时允许发送
        }
    }
} 