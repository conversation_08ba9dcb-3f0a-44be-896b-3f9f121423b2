-- 创建验证码表
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'VerificationCodes')
BEGIN
    CREATE TABLE VerificationCodes (
        ID INT IDENTITY(1, 1) PRIMARY KEY,
        PhoneNumber NVARCHAR(20) NOT NULL,
        Code NVARCHAR(10) NOT NULL,
        CreateTime DATETIME DEFAULT GETDATE(),
        ExpireTime DATETIME NOT NULL,
        IsUsed BIT DEFAULT 0
    );
    
    CREATE INDEX IX_VerificationCodes_PhoneNumber ON VerificationCodes(PhoneNumber);
    CREATE INDEX IX_VerificationCodes_ExpireTime ON VerificationCodes(ExpireTime);
    
    PRINT '验证码表创建成功';
END
ELSE
BEGIN
    PRINT '验证码表已存在';
END 