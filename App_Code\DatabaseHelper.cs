using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// 数据库访问帮助类
/// </summary>
public class DatabaseHelper
{
    // 修复：使用正确的连接字符串配置名称 "CarRepairServiceDB"
    private static readonly string connectionString =
        ConfigurationManager.ConnectionStrings["CarRepairServiceDB"].ConnectionString;

    /// <summary>
    /// 执行SQL查询，返回DataTable
    /// </summary>
    public static DataTable ExecuteQuery(string query, params SqlParameter[] parameters)
    {
        using (SqlConnection connection = new SqlConnection(connectionString))
        {
            using (SqlCommand command = new SqlCommand(query, connection))
            {
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                DataTable dataTable = new DataTable();
                try
                {
                    connection.Open();
                    SqlDataAdapter adapter = new SqlDataAdapter(command);
                    adapter.Fill(dataTable);
                }
                catch (Exception ex)
                {
                    throw new Exception("执行查询时出错: " + ex.Message);
                }
                return dataTable;
            }
        }
    }

    /// <summary>
    /// 执行增删改操作，返回影响的行数
    /// </summary>
    public static int ExecuteNonQuery(string query, params SqlParameter[] parameters)
    {
        using (SqlConnection connection = new SqlConnection(connectionString))
        {
            using (SqlCommand command = new SqlCommand(query, connection))
            {
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                try
                {
                    connection.Open();
                    return command.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    throw new Exception("执行操作时出错: " + ex.Message);
                }
            }
        }
    }

    /// <summary>
    /// 执行查询，返回第一行第一列的值
    /// </summary>
    public static object ExecuteScalar(string query, params SqlParameter[] parameters)
    {
        using (SqlConnection connection = new SqlConnection(connectionString))
        {
            using (SqlCommand command = new SqlCommand(query, connection))
            {
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                try
                {
                    // 记录执行前的SQL信息
                    System.Diagnostics.Debug.WriteLine("===== 执行SQL查询(ExecuteScalar) =====");
                    System.Diagnostics.Debug.WriteLine($"SQL: {query}");
                    if (parameters != null && parameters.Length > 0)
                    {
                        System.Diagnostics.Debug.WriteLine("参数:");
                        foreach (SqlParameter param in parameters)
                        {
                            System.Diagnostics.Debug.WriteLine($"  {param.ParameterName} = {(param.Value == DBNull.Value ? "NULL" : param.Value)}");
                        }
                    }
                    
                    connection.Open();
                    object result = command.ExecuteScalar();
                    
                    // 记录执行结果
                    System.Diagnostics.Debug.WriteLine($"执行结果: {(result == null ? "NULL" : (result == DBNull.Value ? "DBNull" : result.ToString()))}");
                    System.Diagnostics.Debug.WriteLine("===== SQL执行完成 =====");
                    
                    return result;
                }
                catch (Exception ex)
                {
                    // 记录异常信息
                    System.Diagnostics.Debug.WriteLine("===== SQL执行异常 =====");
                    System.Diagnostics.Debug.WriteLine($"异常: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"SQL: {query}");
                    if (parameters != null && parameters.Length > 0)
                    {
                        System.Diagnostics.Debug.WriteLine("参数:");
                        foreach (SqlParameter param in parameters)
                        {
                            System.Diagnostics.Debug.WriteLine($"  {param.ParameterName} = {(param.Value == DBNull.Value ? "NULL" : param.Value)}");
                        }
                    }
                    System.Diagnostics.Debug.WriteLine($"堆栈: {ex.StackTrace}");
                    
                    throw new Exception("执行查询时出错: " + ex.Message, ex);
                }
            }
        }
    }
}