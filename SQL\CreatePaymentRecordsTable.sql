-- 创建支付记录表
USE CarRepairServiceDB;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PaymentRecords')
BEGIN
    CREATE TABLE PaymentRecords (
        PaymentID INT PRIMARY KEY IDENTITY(1,1),
        AppointmentID INT NOT NULL,
        ServiceRecordID INT NOT NULL,
        Amount DECIMAL(10, 2) NOT NULL,
        PaymentMethod NVARCHAR(50) NOT NULL, -- 'CreditCard', 'DebitCard', 'Cash', 'WeChat', 'Alipay'
        PaymentDate DATETIME DEFAULT GETDATE(),
        TransactionNumber NVARCHAR(100),
        Status NVARCHAR(20) NOT NULL DEFAULT 'Completed', -- 'Pending', 'Completed', 'Failed', 'Refunded'
        Notes NVARCHAR(500),
        FOREIGN KEY (AppointmentID) REFERENCES Appointments(AppointmentID),
        FOREIGN KEY (ServiceRecordID) REFERENCES ServiceRecords(RecordID)
    );
    PRINT '成功创建支付记录表(PaymentRecords)';
END
ELSE
BEGIN
    PRINT '支付记录表(PaymentRecords)已经存在';
END

-- 添加已付款标记到ServiceRecords表
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ServiceRecords]') AND name = 'IsPaid')
BEGIN
    ALTER TABLE [dbo].[ServiceRecords]
    ADD IsPaid BIT NOT NULL DEFAULT 0;
    PRINT '成功添加IsPaid列到ServiceRecords表';
END
ELSE
BEGIN
    PRINT 'IsPaid列已存在于ServiceRecords表';
END

-- 添加已验收标记到ServiceRecords表
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[ServiceRecords]') AND name = 'IsVerified')
BEGIN
    ALTER TABLE [dbo].[ServiceRecords]
    ADD IsVerified BIT NOT NULL DEFAULT 0;
    PRINT '成功添加IsVerified列到ServiceRecords表';
END
ELSE
BEGIN
    PRINT 'IsVerified列已存在于ServiceRecords表';
END 