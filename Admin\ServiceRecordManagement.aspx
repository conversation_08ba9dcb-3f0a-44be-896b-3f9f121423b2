<%@ Page Title="维修记录管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="True" CodeBehind="ServiceRecordManagement.aspx.cs" Inherits="WebApplication1.Admin.ServiceRecordManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-md-6">
                <h2><i class="fas fa-tools"></i> 维修记录管理</h2>
            </div>
            <div class="col-md-6 text-right">
                <asp:LinkButton ID="lbtnBackToDashboard" runat="server" CssClass="btn btn-secondary" OnClick="lbtnBackToDashboard_Click">
                    <i class="fas fa-arrow-left"></i> 返回控制台
                </asp:LinkButton>
            </div>
        </div>

        <!-- 消息提示区域 -->
        <div class="row mb-3">
            <div class="col-12">
                <asp:Panel ID="pnlMessage" runat="server" Visible="false">
                    <asp:Label ID="lblMessage" runat="server" CssClass="alert"></asp:Label>
                </asp:Panel>
            </div>
        </div>

        <!-- 搜索和过滤区域 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-search"></i> 搜索和过滤</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="txtStartDate">开始日期</label>
                            <asp:TextBox ID="txtStartDate" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="txtEndDate">结束日期</label>
                            <asp:TextBox ID="txtEndDate" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="txtMinCost">最小费用</label>
                            <asp:TextBox ID="txtMinCost" runat="server" CssClass="form-control" TextMode="Number" Step="0.01"></asp:TextBox>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="txtMaxCost">最大费用</label>
                            <asp:TextBox ID="txtMaxCost" runat="server" CssClass="form-control" TextMode="Number" Step="0.01"></asp:TextBox>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="txtSearch">搜索</label>
                            <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="车主/维修店名称/技师"></asp:TextBox>
                        </div>
                    </div>
                    <div class="col-md-6 align-self-end">
                        <asp:Button ID="btnSearch" runat="server" Text="搜索" CssClass="btn btn-primary" OnClick="btnSearch_Click" />
                        <asp:Button ID="btnReset" runat="server" Text="重置" CssClass="btn btn-outline-secondary ml-2" OnClick="btnReset_Click" />
                        <asp:LinkButton ID="lbtnCreateRecord" runat="server" CssClass="btn btn-success float-right" OnClick="lbtnCreateRecord_Click">
                            <i class="fas fa-plus"></i> 创建维修记录
                        </asp:LinkButton>
                    </div>
                </div>
            </div>
        </div>

        <!-- 维修记录列表 -->
        <asp:Panel ID="pnlRecordList" runat="server">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> 维修记录列表</h5>
                    <span class="badge badge-light">共 <asp:Label ID="lblTotalCount" runat="server" Text="0"></asp:Label> 条记录</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <asp:GridView ID="gvRecords" runat="server" AutoGenerateColumns="False" CssClass="table table-striped table-hover" 
                            DataKeyNames="RecordID" OnRowCommand="gvRecords_RowCommand" 
                            OnRowDataBound="gvRecords_RowDataBound" AllowPaging="True" PageSize="10" 
                            OnPageIndexChanging="gvRecords_PageIndexChanging" EmptyDataText="没有找到维修记录" 
                            PagerStyle-CssClass="pagination-container" PagerSettings-Mode="NumericFirstLast">
                            <Columns>
                                <asp:BoundField DataField="RecordID" HeaderText="ID" ItemStyle-Width="50px" />
                                <asp:BoundField DataField="CompletedDate" HeaderText="完成日期" DataFormatString="{0:yyyy-MM-dd}" />
                                <asp:BoundField DataField="OwnerName" HeaderText="车主" />
                                <asp:BoundField DataField="CarInfo" HeaderText="车辆" />
                                <asp:BoundField DataField="ShopName" HeaderText="维修店" />
                                <asp:BoundField DataField="ServiceName" HeaderText="服务项目" />
                                <asp:BoundField DataField="TechnicianName" HeaderText="技师" />
                                <asp:TemplateField HeaderText="总费用">
                                    <ItemTemplate>
                                        ¥<%# Eval("TotalCost", "{0:N2}") %>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="操作" ItemStyle-Width="180px">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lbtnView" runat="server" CssClass="btn btn-sm btn-info" 
                                            CommandName="ViewRecord" CommandArgument='<%# Eval("RecordID") %>' ToolTip="查看">
                                            <i class="fas fa-eye"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="lbtnEdit" runat="server" CssClass="btn btn-sm btn-primary" 
                                            CommandName="EditRecord" CommandArgument='<%# Eval("RecordID") %>' ToolTip="编辑">
                                            <i class="fas fa-edit"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="lbtnDelete" runat="server" CssClass="btn btn-sm btn-danger" 
                                            CommandName="DeleteRecord" CommandArgument='<%# Eval("RecordID") %>' 
                                            OnClientClick="return confirm('确定要删除此维修记录吗？此操作不可恢复，也会删除相关的评价记录。');" ToolTip="删除">
                                            <i class="fas fa-trash"></i>
                                        </asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle CssClass="pagination-container" HorizontalAlign="Center" />
                            <PagerSettings Mode="NumericFirstLast" FirstPageText="首页" LastPageText="末页" />
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <!-- 查看/编辑维修记录表单 -->
        <asp:Panel ID="pnlRecordDetails" runat="server" Visible="false">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <asp:Literal ID="litFormTitle" runat="server" Text="维修记录详情"></asp:Literal>
                    </h5>
                </div>
                <div class="card-body">
                    <asp:HiddenField ID="hfRecordID" runat="server" />
                    <asp:HiddenField ID="hfAppointmentID" runat="server" />
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>车辆信息</label>
                                <asp:Label ID="lblCarInfo" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>车主</label>
                                <asp:Label ID="lblOwnerName" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>维修店</label>
                                <asp:Label ID="lblShopName" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>服务项目</label>
                                <asp:Label ID="lblServiceName" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>预约日期</label>
                                <asp:Label ID="lblAppointmentDate" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>完成日期</label>
                                <asp:Label ID="lblCompletedDate" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                                <asp:TextBox ID="txtCompletedDate" runat="server" CssClass="form-control" TextMode="DateTimeLocal" Visible="false"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvCompletedDate" runat="server" ControlToValidate="txtCompletedDate"
                                    ErrorMessage="请选择完成日期" Display="Dynamic" CssClass="text-danger" ValidationGroup="RecordEdit"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>诊断详情</label>
                        <asp:Label ID="lblDiagnosisDetails" runat="server" CssClass="form-control" ReadOnly="true" TextMode="MultiLine" Rows="3"></asp:Label>
                        <asp:TextBox ID="txtDiagnosisDetails" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" Visible="false"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvDiagnosisDetails" runat="server" ControlToValidate="txtDiagnosisDetails"
                            ErrorMessage="请输入诊断详情" Display="Dynamic" CssClass="text-danger" ValidationGroup="RecordEdit"></asp:RequiredFieldValidator>
                    </div>
                    
                    <div class="form-group">
                        <label>更换配件</label>
                        <asp:Label ID="lblPartsReplaced" runat="server" CssClass="form-control" ReadOnly="true" TextMode="MultiLine" Rows="2"></asp:Label>
                        <asp:TextBox ID="txtPartsReplaced" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2" Visible="false"></asp:TextBox>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>人工费用</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">¥</span>
                                    </div>
                                    <asp:Label ID="lblLaborCost" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                                    <asp:TextBox ID="txtLaborCost" runat="server" CssClass="form-control" TextMode="Number" Step="0.01" Visible="false"></asp:TextBox>
                                </div>
                                <asp:RequiredFieldValidator ID="rfvLaborCost" runat="server" ControlToValidate="txtLaborCost"
                                    ErrorMessage="请输入人工费用" Display="Dynamic" CssClass="text-danger" ValidationGroup="RecordEdit"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>零件费用</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">¥</span>
                                    </div>
                                    <asp:Label ID="lblPartsCost" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                                    <asp:TextBox ID="txtPartsCost" runat="server" CssClass="form-control" TextMode="Number" Step="0.01" Visible="false"></asp:TextBox>
                                </div>
                                <asp:RequiredFieldValidator ID="rfvPartsCost" runat="server" ControlToValidate="txtPartsCost"
                                    ErrorMessage="请输入零件费用" Display="Dynamic" CssClass="text-danger" ValidationGroup="RecordEdit"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>总费用</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">¥</span>
                                    </div>
                                    <asp:Label ID="lblTotalCost" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>技师姓名</label>
                        <asp:Label ID="lblTechnicianName" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                        <asp:TextBox ID="txtTechnicianName" runat="server" CssClass="form-control" Visible="false"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvTechnicianName" runat="server" ControlToValidate="txtTechnicianName"
                            ErrorMessage="请输入技师姓名" Display="Dynamic" CssClass="text-danger" ValidationGroup="RecordEdit"></asp:RequiredFieldValidator>
                    </div>
                    
                    <div class="mt-3">
                        <asp:Button ID="btnEdit" runat="server" Text="编辑" CssClass="btn btn-primary" OnClick="btnEdit_Click" />
                        <asp:Button ID="btnSave" runat="server" Text="保存" CssClass="btn btn-success" ValidationGroup="RecordEdit" OnClick="btnSave_Click" Visible="false" />
                        <asp:Button ID="btnCancel" runat="server" Text="取消" CssClass="btn btn-outline-secondary ml-2" OnClick="btnCancel_Click" />
                        <asp:Button ID="btnDelete" runat="server" Text="删除记录" CssClass="btn btn-danger float-right" OnClick="btnDelete_Click" OnClientClick="return confirm('确定要删除此维修记录吗？此操作不可恢复，也会删除相关的评价记录。');" />
                    </div>
                </div>
            </div>
        </asp:Panel>
        
        <!-- 创建维修记录表单 -->
        <asp:Panel ID="pnlCreateRecord" runat="server" Visible="false">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-plus"></i> 创建新维修记录</h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="ddlAppointments">关联预约</label>
                        <asp:DropDownList ID="ddlAppointments" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlAppointments_SelectedIndexChanged">
                            <asp:ListItem Value="0" Text="-- 请选择预约 --" Selected="True" />
                        </asp:DropDownList>
                        <asp:RequiredFieldValidator ID="rfvAppointments" runat="server" ControlToValidate="ddlAppointments" InitialValue="0"
                            ErrorMessage="请选择关联预约" Display="Dynamic" CssClass="text-danger" ValidationGroup="CreateRecord"></asp:RequiredFieldValidator>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>车辆信息</label>
                                <asp:Label ID="lblNewCarInfo" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>车主</label>
                                <asp:Label ID="lblNewOwnerName" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>维修店</label>
                                <asp:Label ID="lblNewShopName" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>服务项目</label>
                                <asp:Label ID="lblNewServiceName" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>预约日期</label>
                                <asp:Label ID="lblNewAppointmentDate" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="txtNewCompletedDate">完成日期</label>
                                <asp:TextBox ID="txtNewCompletedDate" runat="server" CssClass="form-control" TextMode="DateTimeLocal"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvNewCompletedDate" runat="server" ControlToValidate="txtNewCompletedDate"
                                    ErrorMessage="请选择完成日期" Display="Dynamic" CssClass="text-danger" ValidationGroup="CreateRecord"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="txtNewDiagnosisDetails">诊断详情</label>
                        <asp:TextBox ID="txtNewDiagnosisDetails" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvNewDiagnosisDetails" runat="server" ControlToValidate="txtNewDiagnosisDetails"
                            ErrorMessage="请输入诊断详情" Display="Dynamic" CssClass="text-danger" ValidationGroup="CreateRecord"></asp:RequiredFieldValidator>
                    </div>
                    
                    <div class="form-group">
                        <label for="txtNewPartsReplaced">更换配件</label>
                        <asp:TextBox ID="txtNewPartsReplaced" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2"></asp:TextBox>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="txtNewLaborCost">人工费用</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">¥</span>
                                    </div>
                                    <asp:TextBox ID="txtNewLaborCost" runat="server" CssClass="form-control" TextMode="Number" Step="0.01"></asp:TextBox>
                                </div>
                                <asp:RequiredFieldValidator ID="rfvNewLaborCost" runat="server" ControlToValidate="txtNewLaborCost"
                                    ErrorMessage="请输入人工费用" Display="Dynamic" CssClass="text-danger" ValidationGroup="CreateRecord"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="txtNewPartsCost">零件费用</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">¥</span>
                                    </div>
                                    <asp:TextBox ID="txtNewPartsCost" runat="server" CssClass="form-control" TextMode="Number" Step="0.01"></asp:TextBox>
                                </div>
                                <asp:RequiredFieldValidator ID="rfvNewPartsCost" runat="server" ControlToValidate="txtNewPartsCost"
                                    ErrorMessage="请输入零件费用" Display="Dynamic" CssClass="text-danger" ValidationGroup="CreateRecord"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>总费用</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">¥</span>
                                    </div>
                                    <asp:Label ID="lblNewTotalCost" runat="server" CssClass="form-control" Text="0.00"></asp:Label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="txtNewTechnicianName">技师姓名</label>
                        <asp:TextBox ID="txtNewTechnicianName" runat="server" CssClass="form-control"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvNewTechnicianName" runat="server" ControlToValidate="txtNewTechnicianName"
                            ErrorMessage="请输入技师姓名" Display="Dynamic" CssClass="text-danger" ValidationGroup="CreateRecord"></asp:RequiredFieldValidator>
                    </div>
                    
                    <div class="mt-3">
                        <asp:Button ID="btnCreate" runat="server" Text="创建维修记录" CssClass="btn btn-success" ValidationGroup="CreateRecord" OnClick="btnCreate_Click" />
                        <asp:Button ID="btnCancelCreate" runat="server" Text="取消" CssClass="btn btn-outline-secondary ml-2" OnClick="btnCancelCreate_Click" />
                    </div>
                </div>
            </div>
        </asp:Panel>
    </div>

    <!-- JavaScript -->
    <script type="text/javascript">
        // 计算总费用
        function calculateTotalCost() {
            try {
                var laborCost = parseFloat(document.getElementById('<%= txtNewLaborCost.ClientID %>').value) || 0;
                var partsCost = parseFloat(document.getElementById('<%= txtNewPartsCost.ClientID %>').value) || 0;
                var total = laborCost + partsCost;
                document.getElementById('<%= lblNewTotalCost.ClientID %>').innerText = total.toFixed(2);
            } catch (e) {
                console.error("Error calculating total cost:", e);
            }
        }

        // 添加事件监听器
        document.addEventListener('DOMContentLoaded', function () {
            var laborCostInput = document.getElementById('<%= txtNewLaborCost.ClientID %>');
            var partsCostInput = document.getElementById('<%= txtNewPartsCost.ClientID %>');
            
            if (laborCostInput) {
                laborCostInput.addEventListener('input', calculateTotalCost);
            }
            
            if (partsCostInput) {
                partsCostInput.addEventListener('input', calculateTotalCost);
            }
            
            // 同样为编辑表单添加事件监听器
            var editLaborCostInput = document.getElementById('<%= txtLaborCost.ClientID %>');
            var editPartsCostInput = document.getElementById('<%= txtPartsCost.ClientID %>');
            var editTotalCostLabel = document.getElementById('<%= lblTotalCost.ClientID %>');
            
            if (editLaborCostInput && editPartsCostInput && editTotalCostLabel) {
                function updateEditTotalCost() {
                    try {
                        var laborCost = parseFloat(editLaborCostInput.value) || 0;
                        var partsCost = parseFloat(editPartsCostInput.value) || 0;
                        var total = laborCost + partsCost;
                        editTotalCostLabel.innerText = total.toFixed(2);
                    } catch (e) {
                        console.error("Error calculating edit total cost:", e);
                    }
                }
                
                editLaborCostInput.addEventListener('input', updateEditTotalCost);
                editPartsCostInput.addEventListener('input', updateEditTotalCost);
            }
        });
    </script>
</asp:Content> 