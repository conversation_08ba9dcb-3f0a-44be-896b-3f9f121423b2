<%@ Master Language="C#" AutoEventWireup="true" Inherits="MasterPage" Codebehind="MasterPage.master.cs" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>汽车维修服务平台</title>
    <link href="https://cdn.staticfile.org/twitter-bootstrap/4.6.0/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdn.staticfile.org/font-awesome/5.15.4/css/all.min.css" rel="stylesheet" />
    <script src="https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.staticfile.org/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
    <style>
        .footer {
            position: fixed;
            bottom: 0;
            width: 100%;
            height: 60px;
            line-height: 60px;
            background-color: #f5f5f5;
            text-align: center;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="Default.aspx">汽车维修服务平台</a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav mr-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="Default.aspx">首页</a>
                        </li>
                        <asp:PlaceHolder ID="phCarOwnerNav" runat="server" Visible="false">
                            <li class="nav-item">
                                <a class="nav-link" href="CarOwner/Dashboard.aspx">我的主页</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="CarOwner/MyCars.aspx">我的车辆</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="CarOwner/Appointments.aspx">维修预约</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="CarOwner/ServiceHistory.aspx">维修记录</a>
                            </li>
                        </asp:PlaceHolder>
                        <asp:PlaceHolder ID="phRepairShopNav" runat="server" Visible="false">
                            <li class="nav-item">
                                <a class="nav-link" href="RepairShop/Dashboard.aspx">店铺主页</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="RepairShop/Services.aspx">服务管理</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="RepairShop/Appointments.aspx">预约管理</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="RepairShop/ServiceRecords.aspx">维修记录</a>
                            </li>
                        </asp:PlaceHolder>
                        <asp:PlaceHolder ID="phAdminNav" runat="server" Visible="false">
                            <li class="nav-item">
                                <a class="nav-link" href="Admin/Dashboard.aspx">管理控制台</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="Admin/UserManagement.aspx">用户管理</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="Admin/ShopManagement.aspx">店铺管理</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="Admin/SystemSettings.aspx">系统设置</a>
                            </li>
                        </asp:PlaceHolder>
                    </ul>
                    <div class="navbar-nav">
                        <asp:LoginView ID="LoginView1" runat="server">
                            <LoggedInTemplate>
                                <span class="nav-item nav-link text-light">欢迎,<asp:LoginName ID="LoginName1" runat="server" /></span>
                                <asp:LoginStatus ID="LoginStatus1" runat="server" CssClass="nav-item nav-link" LogoutAction="Redirect" LogoutPageUrl="~/Login.aspx" LogoutText="退出" />
                            </LoggedInTemplate>
                            <AnonymousTemplate>
                                <a class="nav-item nav-link" href="Login.aspx">登录</a>
                                <a class="nav-item nav-link" href="Register.aspx">注册</a>
                            </AnonymousTemplate>
                        </asp:LoginView>
                    </div>
                </div>
            </div>
        </nav>
        <div class="container mt-4 mb-5 pb-5">
            <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
            </asp:ContentPlaceHolder>
        </div>
        <footer class="footer">
            <div class="container">
                <span class="text-muted">© <%= DateTime.Now.Year %> 汽车维修服务平台 版权所有</span>
            </div>
        </footer>
    </form>
</body>
</html> 