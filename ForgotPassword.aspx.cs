using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class ForgotPassword : System.Web.UI.Page
{
    // 用户ID和手机号码，验证成功后保存
    private int UserID
    {
        get { return ViewState["UserID"] != null ? Convert.ToInt32(ViewState["UserID"]) : 0; }
        set { ViewState["UserID"] = value; }
    }

    private string VerificationCode
    {
        get { return ViewState["VerificationCode"] != null ? ViewState["VerificationCode"].ToString() : string.Empty; }
        set { ViewState["VerificationCode"] = value; }
    }

    private DateTime CodeExpireTime
    {
        get { return ViewState["CodeExpireTime"] != null ? Convert.ToDateTime(ViewState["CodeExpireTime"]) : DateTime.MinValue; }
        set { ViewState["CodeExpireTime"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        // 如果用户已经登录，则重定向到首页
        if (User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Default.aspx");
        }

        if (!IsPostBack)
        {
            UpdatePasswordRequirements();
        }
    }

    /// <summary>
    /// 更新密码要求提示
    /// </summary>
    private void UpdatePasswordRequirements()
    {
        try
        {
            // 获取密码安全设置
            string minLengthStr = SecurityHelper.GetSystemSetting("MinPasswordLength", "8");
            bool requireUppercaseValue = SecurityHelper.GetSystemSetting("RequireUppercase", "true") == "true";
            bool requireDigitValue = SecurityHelper.GetSystemSetting("RequireDigit", "true") == "true";
            bool requireSpecialCharValue = SecurityHelper.GetSystemSetting("RequireSpecialChar", "false") == "true";
            
            // 更新密码要求提示
            minLength.Text = minLengthStr;
            requireUppercase.Visible = requireUppercaseValue;
            requireDigit.Visible = requireDigitValue;
            requireSpecialChar.Visible = requireSpecialCharValue;
        }
        catch (Exception ex)
        {
            // 如果更新密码要求提示失败，忽略错误，不影响重置密码功能
            System.Diagnostics.Debug.WriteLine("更新密码要求提示失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 第一步：验证用户名和手机号
    /// </summary>
    protected void btnNext_Click(object sender, EventArgs e)
    {
        string username = txtUsername.Text.Trim();
        string phoneNumber = txtPhoneNumber.Text.Trim();

        // 验证用户名和手机号码是否匹配
        int userID = VerifyUserPhoneNumber(username, phoneNumber);

        if (userID > 0)
        {
            // 验证通过，保存用户ID并进入下一步
            UserID = userID;
            
            // 显示第二步面板
            pnlStep1.Visible = false;
            pnlStep2.Visible = true;
            lblVerifyMessage.Text = string.Empty;
        }
        else
        {
            // 验证失败
            lblMessage.Text = "用户名或手机号码不正确，请重试。";
        }
    }

    /// <summary>
    /// 返回第一步
    /// </summary>
    protected void btnBack_Click(object sender, EventArgs e)
    {
        pnlStep1.Visible = true;
        pnlStep2.Visible = false;
        pnlStep3.Visible = false;
        pnlSuccess.Visible = false;
    }

    /// <summary>
    /// 发送验证码
    /// </summary>
    protected void btnSendCode_Click(object sender, EventArgs e)
    {
        if (UserID <= 0)
        {
            lblVerifyMessage.Text = "验证信息已过期，请重新开始。";
            return;
        }

        // 生成随机6位数验证码
        Random random = new Random();
        string code = random.Next(100000, 999999).ToString();

        // 将验证码保存到ViewState并设置过期时间（10分钟）
        VerificationCode = code;
        CodeExpireTime = DateTime.Now.AddMinutes(10);

        // 获取用户手机号
        string phoneNumber = GetUserPhoneNumber(UserID);
        if (string.IsNullOrEmpty(phoneNumber))
        {
            lblVerifyMessage.Text = "无法获取用户手机号，请重试。";
            return;
        }

        // 发送短信验证码
        bool sendResult = SendVerificationCode(phoneNumber, code);
        
        if (sendResult)
        {
            // 发送成功
            btnSendCode.Text = "重新发送";
            btnSendCode.Enabled = false;
            
            // 启用JavaScript定时器，60秒后重新启用按钮
            string script = @"
                var seconds = 60;
                var btnSendCode = document.getElementById('" + btnSendCode.ClientID + @"');
                btnSendCode.value = '重新发送(' + seconds + ')';
                var interval = setInterval(function() {
                    seconds--;
                    btnSendCode.value = '重新发送(' + seconds + ')';
                    if (seconds <= 0) {
                        clearInterval(interval);
                        btnSendCode.value = '重新发送';
                        btnSendCode.disabled = false;
                    }
                }, 1000);
            ";
            
            ScriptManager.RegisterStartupScript(this, GetType(), "EnableButtonTimer", script, true);
            
            // 注意：验证码文本消息已在SendVerificationCode方法中设置，此处不再覆盖
        }
        else
        {
            // 发送失败
            lblVerifyMessage.Text = "验证码发送失败，请稍后重试。";
            lblVerifyMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 验证验证码
    /// </summary>
    protected void btnVerifyCode_Click(object sender, EventArgs e)
    {
        string inputCode = txtVerificationCode.Text.Trim();
        string phoneNumber = GetUserPhoneNumber(UserID);
        
        if (string.IsNullOrEmpty(phoneNumber))
        {
            lblVerifyMessage.Text = "无法获取用户手机号，请重新开始。";
            return;
        }
        
        // 先检查ViewState中的验证码(内存中的验证码)
        if (DateTime.Now <= CodeExpireTime && inputCode == VerificationCode)
        {
            // 验证通过，进入第三步
            pnlStep2.Visible = false;
            pnlStep3.Visible = true;
            lblResetMessage.Text = string.Empty;
            return;
        }
        
        // 如果内存中的验证码不匹配或过期，则检查数据库中的验证码
        if (VerificationHelper.VerifyCode(phoneNumber, inputCode))
        {
            // 验证通过，进入第三步
            pnlStep2.Visible = false;
            pnlStep3.Visible = true;
            lblResetMessage.Text = string.Empty;
        }
        else
        {
            // 验证失败
            lblVerifyMessage.Text = "验证码不正确或已过期，请重新输入或获取新的验证码。";
        }
    }

    /// <summary>
    /// 重置密码
    /// </summary>
    protected void btnResetPassword_Click(object sender, EventArgs e)
    {
        string newPassword = txtNewPassword.Text;
        string confirmPassword = txtConfirmPassword.Text;
        
        // 验证两次密码是否一致
        if (newPassword != confirmPassword)
        {
            lblResetMessage.Text = "两次输入的密码不一致，请重新输入。";
            return;
        }
        
        // 验证密码强度
        string errorMessage;
        if (!SecurityHelper.ValidatePasswordStrength(newPassword, out errorMessage))
        {
            lblResetMessage.Text = errorMessage;
            return;
        }
        
        // 更新密码
        if (UpdateUserPassword(UserID, newPassword))
        {
            // 更新成功，显示成功页面
            pnlStep1.Visible = false;
            pnlStep2.Visible = false;
            pnlStep3.Visible = false;
            pnlSuccess.Visible = true;
            
            // 确保页面完全刷新
            ScriptManager.RegisterStartupScript(this, GetType(), "ResetComplete", "console.log('密码重置成功');", true);
        }
        else
        {
            // 更新失败
            lblResetMessage.Text = "密码重置失败，请稍后再试。";
        }
    }

    /// <summary>
    /// 验证用户名和手机号码是否匹配
    /// </summary>
    private int VerifyUserPhoneNumber(string username, string phoneNumber)
    {
        try
        {
            string query = "SELECT UserID FROM Users WHERE Username = @Username AND PhoneNumber = @PhoneNumber AND IsActive = 1";
            SqlParameter[] parameters =
            {
                new SqlParameter("@Username", username),
                new SqlParameter("@PhoneNumber", phoneNumber)
            };

            object result = DatabaseHelper.ExecuteScalar(query, parameters);
            if (result != null && result != DBNull.Value)
            {
                return Convert.ToInt32(result);
            }
            return -1;
        }
        catch
        {
            return -1;
        }
    }

    /// <summary>
    /// 获取用户手机号
    /// </summary>
    private string GetUserPhoneNumber(int userID)
    {
        try
        {
            string query = "SELECT PhoneNumber FROM Users WHERE UserID = @UserID AND IsActive = 1";
            SqlParameter parameter = new SqlParameter("@UserID", userID);

            object result = DatabaseHelper.ExecuteScalar(query, parameter);
            if (result != null && result != DBNull.Value)
            {
                return result.ToString();
            }
            return string.Empty;
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    /// 发送短信验证码
    /// </summary>
    private bool SendVerificationCode(string phoneNumber, string code)
    {
        try
        {
            // 检查是否可以发送新验证码（60秒内不允许重复发送）
            if (!VerificationHelper.CanSendNewCode(phoneNumber, 60))
            {
                System.Diagnostics.Debug.WriteLine($"手机号 {phoneNumber} 请求发送验证码过于频繁");
                return false;
            }

            // 记录验证码
            VerificationHelper.LogVerificationCode(phoneNumber, code, 10); // 10分钟有效期

            // 模拟发送短信验证码，实际应用中应调用短信API
            System.Diagnostics.Debug.WriteLine($"向手机号 {phoneNumber} 发送验证码: {code}");

            // 输出消息到调试日志
            System.Diagnostics.Debug.WriteLine($"发送验证码到 {phoneNumber}: {code}，过期时间: {CodeExpireTime:yyyy-MM-dd HH:mm:ss}");
            
            // 在开发模式下，直接显示验证码在页面上（注意：生产环境应移除此代码）
            lblVerifyMessage.Text = $"【测试模式】验证码已发送到您的手机，10分钟内有效。<br/><strong>验证码：{code}</strong>";
            lblVerifyMessage.CssClass = "text-success";
            
            // 真实环境中应接入短信服务API
            // 这里简单返回成功
            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"发送验证码错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 更新用户密码
    /// </summary>
    private bool UpdateUserPassword(int userID, string newPassword)
    {
        try
        {
            string query = "UPDATE Users SET Password = @Password WHERE UserID = @UserID";
            SqlParameter[] parameters =
            {
                new SqlParameter("@UserID", userID),
                new SqlParameter("@Password", newPassword)
            };

            int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
            return result > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新密码错误: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// 返回登录页面
    /// </summary>
    protected void btnBackToLogin_Click(object sender, EventArgs e)
    {
        Response.Redirect("~/Login.aspx");
    }
} 