using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebApplication1.Admin
{
    public partial class AppointmentManagement : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // 检查用户是否为管理员
            if (!User.Identity.IsAuthenticated || Session["UserType"] == null || Session["UserType"].ToString() != "Admin")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                // 加载预约列表
                LoadAppointments();
            }
        }

        #region 预约列表和搜索

        /// <summary>
        /// 加载预约列表
        /// </summary>
        private void LoadAppointments()
        {
            try
            {
                // 获取搜索参数
                string status = ddlStatus.SelectedValue;
                DateTime? startDate = null;
                DateTime? endDate = null;
                string searchText = txtSearch.Text.Trim();

                if (!string.IsNullOrEmpty(txtStartDate.Text))
                {
                    startDate = DateTime.Parse(txtStartDate.Text);
                }

                if (!string.IsNullOrEmpty(txtEndDate.Text))
                {
                    endDate = DateTime.Parse(txtEndDate.Text + " 23:59:59");
                }

                // 搜索预约
                DataTable appointments;
                if (!string.IsNullOrEmpty(searchText) || !string.IsNullOrEmpty(status) || startDate != null || endDate != null)
                {
                    appointments = AppointmentManager.SearchAppointments(status, startDate, endDate, 
                        searchText, searchText); // 同时搜索车主姓名和维修店名称
                }
                else
                {
                    // 获取全部预约
                    int pageIndex = gvAppointments.PageIndex;
                    int pageSize = gvAppointments.PageSize;
                    appointments = AppointmentManager.GetAllAppointmentsPaged(pageIndex, pageSize);
                }

                // 绑定到GridView
                gvAppointments.DataSource = appointments;
                gvAppointments.DataBind();

                // 更新总数
                lblTotalCount.Text = AppointmentManager.GetAppointmentsCount().ToString();

                // 如果没有找到记录，显示提示
                if (appointments == null || appointments.Rows.Count == 0)
                {
                    ShowMessage("没有找到符合条件的预约记录。", "info");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("加载预约记录时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        protected void btnSearch_Click(object sender, EventArgs e)
        {
            gvAppointments.PageIndex = 0; // 重置到第一页
            LoadAppointments();
        }

        /// <summary>
        /// 重置搜索按钮点击事件
        /// </summary>
        protected void btnReset_Click(object sender, EventArgs e)
        {
            ddlStatus.SelectedIndex = 0;
            txtStartDate.Text = string.Empty;
            txtEndDate.Text = string.Empty;
            txtSearch.Text = string.Empty;
            gvAppointments.PageIndex = 0;
            LoadAppointments();
        }

        /// <summary>
        /// GridView分页事件
        /// </summary>
        protected void gvAppointments_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            gvAppointments.PageIndex = e.NewPageIndex;
            LoadAppointments();
        }

        #endregion

        #region GridView事件处理

        /// <summary>
        /// GridView行命令事件处理
        /// </summary>
        protected void gvAppointments_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "ViewAppointment" || e.CommandName == "EditAppointment")
                {
                    int appointmentID = Convert.ToInt32(e.CommandArgument);
                    ShowAppointmentDetails(appointmentID, e.CommandName == "EditAppointment");
                }
                else if (e.CommandName == "DeleteAppointment")
                {
                    int appointmentID = Convert.ToInt32(e.CommandArgument);
                    DeleteAppointment(appointmentID);
                }
            }
            catch (Exception ex)
            {
                ShowMessage("处理命令时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// GridView行数据绑定事件
        /// </summary>
        protected void gvAppointments_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                // 可以在这里添加行数据绑定逻辑
            }
        }

        #endregion

        #region 查看和编辑预约详情

        /// <summary>
        /// 显示预约详情
        /// </summary>
        private void ShowAppointmentDetails(int appointmentID, bool isEditMode = false)
        {
            try
            {
                DataTable appointmentData = AppointmentManager.GetAppointmentByID(appointmentID);
                if (appointmentData != null && appointmentData.Rows.Count > 0)
                {
                    DataRow row = appointmentData.Rows[0];

                    // 保存预约ID
                    hfAppointmentID.Value = appointmentID.ToString();

                    // 设置表单标题
                    litFormTitle.Text = isEditMode ? "编辑预约" : "预约详情";

                    // 显示基本信息（查看模式）
                    lblCarInfo.Text = row["Make"].ToString() + " " + row["Model"].ToString() + " (" + row["LicensePlate"].ToString() + ")";
                    lblOwnerName.Text = GetOwnerName(Convert.ToInt32(row["OwnerID"]));
                    lblShopName.Text = row["ShopName"].ToString();
                    lblServiceName.Text = row["ServiceName"].ToString();
                    lblAppointmentDate.Text = Convert.ToDateTime(row["AppointmentDate"]).ToString("yyyy-MM-dd HH:mm");
                    lblStatus.Text = GetStatusText(row["Status"].ToString());
                    lblDescription.Text = row["Description"] != DBNull.Value ? row["Description"].ToString() : "";
                    lblCreatedDate.Text = row["CreatedDate"] != DBNull.Value ? Convert.ToDateTime(row["CreatedDate"]).ToString("yyyy-MM-dd HH:mm:ss") : "";

                    // 编辑模式设置
                    if (isEditMode)
                    {
                        // 设置编辑控件的值
                        LoadCarsDropdown(Convert.ToInt32(row["CarID"]));
                        LoadShopsDropdown(Convert.ToInt32(row["ShopID"]));
                        LoadServicesDropdown(Convert.ToInt32(row["ShopID"]), Convert.ToInt32(row["ServiceID"]));
                        
                        DateTime appointmentDate = Convert.ToDateTime(row["AppointmentDate"]);
                        txtAppointmentDate.Text = appointmentDate.ToString("yyyy-MM-ddTHH:mm");
                        
                        ddlAppointmentStatus.SelectedValue = row["Status"].ToString();
                        txtDescription.Text = row["Description"] != DBNull.Value ? row["Description"].ToString() : "";

                        // 显示编辑控件，隐藏显示控件
                        ShowEditControls(true);
                    }
                    else
                    {
                        // 显示查看控件，隐藏编辑控件
                        ShowEditControls(false);
                    }

                    // 显示详情面板，隐藏列表面板
                    pnlAppointmentDetails.Visible = true;
                    pnlAppointmentList.Visible = false;
                }
                else
                {
                    ShowMessage("找不到指定的预约记录。", "warning");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("加载预约详情时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 获取车主姓名
        /// </summary>
        private string GetOwnerName(int ownerID)
        {
            try
            {
                string query = "SELECT FullName FROM CarOwners WHERE OwnerID = @OwnerID";
                System.Data.SqlClient.SqlParameter parameter = new System.Data.SqlClient.SqlParameter("@OwnerID", ownerID);
                object result = DatabaseHelper.ExecuteScalar(query, parameter);
                return result != null ? result.ToString() : "未知";
            }
            catch
            {
                return "未知";
            }
        }

        /// <summary>
        /// 加载车辆下拉列表
        /// </summary>
        private void LoadCarsDropdown(int selectedCarID = 0)
        {
            try
            {
                string query = "SELECT CarID, Make + ' ' + Model + ' (' + LicensePlate + ')' AS CarInfo FROM Cars ORDER BY Make, Model";
                DataTable cars = DatabaseHelper.ExecuteQuery(query);

                ddlCars.Items.Clear();
                ddlCars.Items.Add(new ListItem("-- 请选择车辆 --", "0"));

                if (cars != null && cars.Rows.Count > 0)
                {
                    foreach (DataRow row in cars.Rows)
                    {
                        ListItem item = new ListItem(row["CarInfo"].ToString(), row["CarID"].ToString());
                        if (Convert.ToInt32(row["CarID"]) == selectedCarID)
                        {
                            item.Selected = true;
                        }
                        ddlCars.Items.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage("加载车辆列表时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 加载维修店下拉列表
        /// </summary>
        private void LoadShopsDropdown(int selectedShopID = 0)
        {
            try
            {
                DataTable shops = AppointmentManager.GetAllRepairShops();

                ddlShops.Items.Clear();
                ddlShops.Items.Add(new ListItem("-- 请选择维修店 --", "0"));

                if (shops != null && shops.Rows.Count > 0)
                {
                    foreach (DataRow row in shops.Rows)
                    {
                        ListItem item = new ListItem(row["ShopName"].ToString(), row["ShopID"].ToString());
                        if (Convert.ToInt32(row["ShopID"]) == selectedShopID)
                        {
                            item.Selected = true;
                        }
                        ddlShops.Items.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage("加载维修店列表时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 加载服务项目下拉列表
        /// </summary>
        private void LoadServicesDropdown(int shopID, int selectedServiceID = 0)
        {
            try
            {
                DataTable services = AppointmentManager.GetServicesByShopID(shopID);

                ddlServices.Items.Clear();
                ddlServices.Items.Add(new ListItem("-- 请选择服务项目 --", "0"));

                if (services != null && services.Rows.Count > 0)
                {
                    foreach (DataRow row in services.Rows)
                    {
                        string serviceInfo = row["ServiceName"].ToString();
                        if (row["BasePrice"] != DBNull.Value)
                        {
                            serviceInfo += " (" + string.Format("¥{0:N2}", row["BasePrice"]) + ")";
                        }
                        
                        ListItem item = new ListItem(serviceInfo, row["ServiceID"].ToString());
                        if (Convert.ToInt32(row["ServiceID"]) == selectedServiceID)
                        {
                            item.Selected = true;
                        }
                        ddlServices.Items.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage("加载服务项目列表时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 切换显示/编辑控件
        /// </summary>
        private void ShowEditControls(bool isEditMode)
        {
            // 显示模式控件
            lblCarInfo.Visible = !isEditMode;
            lblShopName.Visible = !isEditMode;
            lblServiceName.Visible = !isEditMode;
            lblAppointmentDate.Visible = !isEditMode;
            lblStatus.Visible = !isEditMode;
            lblDescription.Visible = !isEditMode;
            btnEdit.Visible = !isEditMode;

            // 编辑模式控件
            ddlCars.Visible = isEditMode;
            ddlShops.Visible = isEditMode;
            ddlServices.Visible = isEditMode;
            txtAppointmentDate.Visible = isEditMode;
            ddlAppointmentStatus.Visible = isEditMode;
            txtDescription.Visible = isEditMode;
            btnSave.Visible = isEditMode;

            // 删除按钮始终可见
            btnDelete.Visible = true;
        }

        /// <summary>
        /// 编辑按钮点击事件
        /// </summary>
        protected void btnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                int appointmentID = Convert.ToInt32(hfAppointmentID.Value);
                ShowAppointmentDetails(appointmentID, true);
            }
            catch (Exception ex)
            {
                ShowMessage("进入编辑模式时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        protected void btnSave_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid)
                return;

            try
            {
                // 获取表单数据
                int appointmentID = Convert.ToInt32(hfAppointmentID.Value);
                int carID = Convert.ToInt32(ddlCars.SelectedValue);
                int shopID = Convert.ToInt32(ddlShops.SelectedValue);
                int serviceID = Convert.ToInt32(ddlServices.SelectedValue);
                DateTime appointmentDate = DateTime.Parse(txtAppointmentDate.Text);
                string status = ddlAppointmentStatus.SelectedValue;
                string description = txtDescription.Text.Trim();

                // 验证必填字段
                if (carID <= 0 || shopID <= 0 || serviceID <= 0)
                {
                    ShowMessage("请选择车辆、维修店和服务项目。", "danger");
                    return;
                }

                // 更新预约信息
                bool success = AppointmentManager.UpdateAppointment(appointmentID, appointmentDate, status, description, carID, shopID, serviceID);

                if (success)
                {
                    ShowMessage("预约信息已成功更新。", "success");
                    ShowAppointmentDetails(appointmentID); // 返回查看模式
                }
                else
                {
                    ShowMessage("更新预约信息失败。请稍后再试。", "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("保存预约信息时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        protected void btnCancel_Click(object sender, EventArgs e)
        {
            // 返回列表视图
            pnlAppointmentDetails.Visible = false;
            pnlAppointmentList.Visible = true;
            LoadAppointments();
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        protected void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                int appointmentID = Convert.ToInt32(hfAppointmentID.Value);
                DeleteAppointment(appointmentID);
            }
            catch (Exception ex)
            {
                ShowMessage("删除预约时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 删除预约
        /// </summary>
        private void DeleteAppointment(int appointmentID)
        {
            try
            {
                bool success = AppointmentManager.DeleteAppointment(appointmentID);

                if (success)
                {
                    ShowMessage("预约已成功删除。", "success");
                    
                    // 返回列表视图
                    pnlAppointmentDetails.Visible = false;
                    pnlAppointmentList.Visible = true;
                    LoadAppointments();
                }
                else
                {
                    ShowMessage("无法删除预约。可能该预约已有关联的维修记录。", "warning");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("删除预约时出错: " + ex.Message, "danger");
            }
        }

        /// <summary>
        /// 维修店下拉列表选择改变事件
        /// </summary>
        protected void ddlShops_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                int shopID = Convert.ToInt32(ddlShops.SelectedValue);
                if (shopID > 0)
                {
                    LoadServicesDropdown(shopID);
                }
                else
                {
                    ddlServices.Items.Clear();
                    ddlServices.Items.Add(new ListItem("-- 请先选择维修店 --", "0"));
                }
            }
            catch (Exception ex)
            {
                ShowMessage("加载服务项目时出错: " + ex.Message, "danger");
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取预约状态文本
        /// </summary>
        protected string GetStatusText(string status)
        {
            switch (status)
            {
                case "Pending": return "待确认";
                case "Confirmed": return "已确认";
                case "Completed": return "已完成";
                case "Cancelled": return "已取消";
                default: return status;
            }
        }

        /// <summary>
        /// 获取预约状态CSS类
        /// </summary>
        protected string GetStatusCssClass(string status)
        {
            switch (status)
            {
                case "Pending": return "badge badge-warning";
                case "Confirmed": return "badge badge-success";
                case "Completed": return "badge badge-info";
                case "Cancelled": return "badge badge-danger";
                default: return "badge badge-secondary";
            }
        }

        /// <summary>
        /// 显示消息
        /// </summary>
        private void ShowMessage(string message, string type)
        {
            lblMessage.Text = message;
            lblMessage.CssClass = "alert alert-" + type;
            pnlMessage.Visible = true;
        }

        /// <summary>
        /// 返回控制台按钮点击事件
        /// </summary>
        protected void lbtnBackToDashboard_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Admin/Dashboard.aspx");
        }

        #endregion
    }
} 