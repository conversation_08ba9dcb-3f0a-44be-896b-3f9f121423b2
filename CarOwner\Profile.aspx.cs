using System;
using System.Data;
using System.Web.UI;
using System.IO;

public partial class CarOwner_Profile : System.Web.UI.Page
{
    private int userID;
    private int ownerID;
    private const string DEFAULT_AVATAR_URL = "~/Images/default_avatar.jpg";
    private const string AVATAR_UPLOAD_PATH = "~/Uploads/Avatars";

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取当前用户信息
        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "CarOwner")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        
        // 获取车主ID
        ownerID = CarManager.GetOwnerIDByUserID(userID);
        if (ownerID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            // 加载用户信息
            LoadUserInfo();
        }
    }

    /// <summary>
    /// 加载用户信息
    /// </summary>
    private void LoadUserInfo()
    {
        DataTable ownerInfo = CarManager.GetOwnerInfo(ownerID);
        if (ownerInfo != null && ownerInfo.Rows.Count > 0)
        {
            DataRow row = ownerInfo.Rows[0];

            // 显示基本信息
            string username = row["Username"].ToString();
            lblUsername.Text = username;
            lblDisplayUsername.Text = username;
            txtFullName.Text = row["FullName"].ToString();
            txtEmail.Text = row["Email"].ToString();
            txtPhoneNumber.Text = row["UserPhoneNumber"].ToString();
            
            // 显示地址信息
            txtAddress.Text = row["Address"].ToString();
            
            // 显示头像 - 安全地检查列是否存在
            if (ownerInfo.Columns.Contains("AvatarUrl"))
            {
                string avatarUrl = row["AvatarUrl"].ToString();
                if (!string.IsNullOrEmpty(avatarUrl))
                {
                    imgAvatar.ImageUrl = avatarUrl;
                }
                else
                {
                    imgAvatar.ImageUrl = DEFAULT_AVATAR_URL;
                }
            }
            else
            {
                imgAvatar.ImageUrl = DEFAULT_AVATAR_URL;
            }
        }
    }

    /// <summary>
    /// 保存用户信息
    /// </summary>
    protected void btnSave_Click(object sender, EventArgs e)
    {
        // 获取表单数据
        string fullName = txtFullName.Text.Trim();
        string email = txtEmail.Text.Trim();
        string phoneNumber = txtPhoneNumber.Text.Trim();
        string address = txtAddress.Text.Trim();
        
        try
        {
            // 更新用户基本信息（Users表）
            bool userUpdateSuccess = CarManager.UpdateUserInfo(userID, email, phoneNumber);
            
            // 更新车主信息（CarOwners表）
            // 注意：我们只更新数据库中实际存在的字段
            bool ownerUpdateSuccess = CarManager.UpdateOwnerInfo(ownerID, fullName, address, null, null, null, null, null);
            
            if (userUpdateSuccess || ownerUpdateSuccess)
            {
                // 更新成功，显示成功消息
                pnlMessage.Visible = true;
                litMessage.Text = "<i class='fas fa-check-circle mr-2'></i>个人信息更新成功！";
                pnlMessage.CssClass = "alert alert-success mb-4";
                
                // 重新加载用户信息
                LoadUserInfo();
            }
            else
            {
                // 更新失败，显示错误消息
                pnlMessage.Visible = true;
                litMessage.Text = "<i class='fas fa-exclamation-circle mr-2'></i>更新个人信息时出现错误，请稍后再试。";
                pnlMessage.CssClass = "alert alert-danger mb-4";
            }
        }
        catch (Exception ex)
        {
            // 处理异常
            pnlMessage.Visible = true;
            litMessage.Text = $"<i class='fas fa-exclamation-triangle mr-2'></i>发生错误: {ex.Message}";
            pnlMessage.CssClass = "alert alert-danger mb-4";
        }
    }
    
    /// <summary>
    /// 上传头像
    /// </summary>
    protected void btnUploadAvatar_Click(object sender, EventArgs e)
    {
        try
        {
            if (fileAvatar.HasFile)
            {
                // 上传头像
                string avatarUrl = FileUploadHelper.UploadImage(fileAvatar.PostedFile, AVATAR_UPLOAD_PATH);
                
                // 更新数据库中的头像URL
                bool success = CarManager.UpdateOwnerAvatar(ownerID, avatarUrl);
                
                if (success)
                {
                    // 更新成功，显示成功消息
                    pnlMessage.Visible = true;
                    litMessage.Text = "<i class='fas fa-check-circle mr-2'></i>头像上传成功！";
                    pnlMessage.CssClass = "alert alert-success mb-4";
                    
                    // 更新头像显示
                    imgAvatar.ImageUrl = avatarUrl;
                }
                else
                {
                    // 更新失败，显示错误消息
                    pnlMessage.Visible = true;
                    litMessage.Text = "<i class='fas fa-exclamation-circle mr-2'></i>更新头像时出现错误，请稍后再试。";
                    pnlMessage.CssClass = "alert alert-danger mb-4";
                }
            }
            else
            {
                // 没有选择文件
                pnlMessage.Visible = true;
                litMessage.Text = "<i class='fas fa-exclamation-circle mr-2'></i>请选择要上传的图片。";
                pnlMessage.CssClass = "alert alert-warning mb-4";
            }
        }
        catch (Exception ex)
        {
            // 处理异常
            pnlMessage.Visible = true;
            litMessage.Text = $"<i class='fas fa-exclamation-triangle mr-2'></i>上传头像时出错: {ex.Message}";
            pnlMessage.CssClass = "alert alert-danger mb-4";
        }
    }
} 