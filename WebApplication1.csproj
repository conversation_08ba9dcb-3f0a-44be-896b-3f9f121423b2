﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{DB9C54EA-E63E-4B12-B11C-7BA6A6883436}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>WebApplication1</RootNamespace>
    <AssemblyName>WebApplication1</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44321</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>packages\Microsoft.Web.Infrastructure.2.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.MSAjax">
      <HintPath>packages\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\lib\net45\Microsoft.ScriptManager.MSAjax.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.WebForms">
      <HintPath>packages\Microsoft.AspNet.ScriptManager.WebForms.5.0.0\lib\net45\Microsoft.ScriptManager.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <HintPath>packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>packages\Antlr.*******\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Web.Optimization.WebForms">
      <Private>True</Private>
      <HintPath>packages\Microsoft.AspNet.Web.Optimization.WebForms.1.1.3\lib\net45\Microsoft.AspNet.Web.Optimization.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.FriendlyUrls">
      <HintPath>packages\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\lib\net45\Microsoft.AspNet.FriendlyUrls.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="About.aspx" />
    <Content Include="Admin\AddUser.aspx" />
    <Content Include="Admin\AppointmentManagement.aspx" />
    <Content Include="Admin\CategoryManagement.aspx" />
    <Content Include="Admin\Dashboard.aspx" />
    <Content Include="Admin\ReviewManagement.aspx" />
    <Content Include="Admin\ServiceRecordManagement.aspx" />
    <Content Include="Admin\ShopManagement.aspx" />
    <Content Include="Admin\SystemSettings.aspx" />
    <Content Include="Admin\UserManagement.aspx" />
    <Content Include="Appointments.aspx" />
    <Content Include="CarOwner\Appointments.aspx" />
    <Content Include="CarOwner\Dashboard.aspx" />
    <Content Include="CarOwner\MyCars.aspx" />
    <Content Include="CarOwner\PaymentSimulation.aspx" />
    <Content Include="CarOwner\Profile.aspx" />
    <Content Include="CarOwner\ServiceHistory.aspx" />
    <Content Include="CarOwner\ServiceSearch.aspx" />
    <Content Include="Contact.aspx" />
    <Content Include="Content\bootstrap-grid.css" />
    <Content Include="Content\bootstrap-grid.min.css" />
    <Content Include="Content\bootstrap-grid.rtl.css" />
    <Content Include="Content\bootstrap-grid.rtl.min.css" />
    <Content Include="Content\bootstrap-reboot.css" />
    <Content Include="Content\bootstrap-reboot.min.css" />
    <Content Include="Content\bootstrap-reboot.rtl.css" />
    <Content Include="Content\bootstrap-reboot.rtl.min.css" />
    <Content Include="Content\bootstrap-utilities.css" />
    <Content Include="Content\bootstrap-utilities.min.css" />
    <Content Include="Content\bootstrap-utilities.rtl.css" />
    <Content Include="Content\bootstrap-utilities.rtl.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\bootstrap.rtl.css" />
    <Content Include="Content\bootstrap.rtl.min.css" />
    <Content Include="Content\Site.css" />
    <Content Include="Default.aspx" />
    <Content Include="favicon.ico" />
    <Content Include="ForgotPassword.aspx" />
    <Content Include="Global.asax" />
    <Content Include="Images\Cars\2f9545bb-7503-4008-b6f2-0f02f75e69eb.jpg" />
    <Content Include="Images\Cars\6ab2a7e8-36a6-43ab-94eb-31c867962ac0.jpg" />
    <Content Include="Images\Shops\4fbe72ff-a5bc-4484-8b20-afe17c571bae.jpg" />
    <Content Include="Images\Shops\69fd0a53-c8a4-4e5e-8edb-fd9cca63e790.gif" />
    <Content Include="Images\Shops\c6140801-2823-4e83-9075-6d12493c4fbb.gif" />
    <Content Include="Images\Shops\da68802f-e6be-4f36-a4da-907f14b597ef.gif" />
    <Content Include="Login.aspx" />
    <Content Include="output.txt" />
    <Content Include="Register.aspx" />
    <Content Include="RepairShop\Appointments.aspx" />
    <Content Include="RepairShop\Dashboard.aspx" />
    <Content Include="RepairShop\ServiceRecords.aspx" />
    <Content Include="RepairShop\Services.aspx" />
    <Content Include="Scripts\bootstrap.bundle.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap.esm.js" />
    <Content Include="Scripts\bootstrap.esm.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\bootstrap.min.js.map" />
    <Content Include="Scripts\bootstrap.js.map" />
    <Content Include="Scripts\bootstrap.esm.min.js.map" />
    <Content Include="Scripts\bootstrap.esm.js.map" />
    <Content Include="Scripts\bootstrap.bundle.min.js.map" />
    <Content Include="Scripts\bootstrap.bundle.js.map" />
    <Content Include="Content\bootstrap.rtl.min.css.map" />
    <Content Include="Content\bootstrap.rtl.css.map" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap-utilities.rtl.min.css.map" />
    <Content Include="Content\bootstrap-utilities.rtl.css.map" />
    <Content Include="Content\bootstrap-utilities.min.css.map" />
    <Content Include="Content\bootstrap-utilities.css.map" />
    <Content Include="Content\bootstrap-reboot.rtl.min.css.map" />
    <Content Include="Content\bootstrap-reboot.rtl.css.map" />
    <Content Include="Content\bootstrap-reboot.min.css.map" />
    <Content Include="Content\bootstrap-reboot.css.map" />
    <Content Include="Content\bootstrap-grid.rtl.min.css.map" />
    <Content Include="Content\bootstrap-grid.rtl.css.map" />
    <Content Include="Content\bootstrap-grid.min.css.map" />
    <Content Include="Content\bootstrap-grid.css.map" />
    <Content Include="MasterPage.master" />
    <Content Include="Register.aspx.bak" />
    <Content Include="MasterPage.master.bak" />
    <Content Include="Login.aspx.bak" />
    <Content Include="Register.aspx.old" />
    <Content Include="Login.aspx.old" />
    <Content Include="Admin\Dashboard.aspx.old" />
    <Content Include="Admin\Dashboard.aspx.bak" />
    <None Include="Scripts\jquery-3.7.0.intellisense.js" />
    <Content Include="Scripts\jquery-3.7.0.js" />
    <Content Include="Scripts\jquery-3.7.0.min.js" />
    <Content Include="Scripts\jquery-3.7.0.slim.js" />
    <Content Include="Scripts\jquery-3.7.0.slim.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\WebForms\DetailsView.js" />
    <Content Include="Scripts\WebForms\Focus.js" />
    <Content Include="Scripts\WebForms\GridView.js" />
    <Content Include="Scripts\WebForms\Menu.js" />
    <Content Include="Scripts\WebForms\MenuStandards.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjax.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxApplicationServices.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxComponentModel.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxCore.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxGlobalization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxHistory.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxNetwork.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxSerialization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxTimer.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebForms.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebServices.js" />
    <Content Include="Scripts\WebForms\SmartNav.js" />
    <Content Include="Scripts\WebForms\TreeView.js" />
    <Content Include="Scripts\WebForms\WebForms.js" />
    <Content Include="Scripts\WebForms\WebParts.js" />
    <Content Include="Scripts\WebForms\WebUIValidation.js" />
    <Content Include="Search.aspx" />
    <Content Include="ShopDetails.aspx" />
    <Content Include="Site.Master" />
    <Content Include="ViewSwitcher.ascx" />
    <Content Include="Web.config" />
    <Content Include="Bundle.config" />
    <Content Include="Site.Mobile.Master" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Admin\AddUser.aspx.cs">
      <DependentUpon>AddUser.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\AddUser.aspx.designer.cs">
      <DependentUpon>AddUser.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\AppointmentManagement.aspx.cs">
      <DependentUpon>AppointmentManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\AppointmentManagement.aspx.designer.cs">
      <DependentUpon>AppointmentManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\CategoryManagement.aspx.cs">
      <DependentUpon>CategoryManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\CategoryManagement.aspx.designer.cs">
      <DependentUpon>CategoryManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ReviewManagement.aspx.cs">
      <DependentUpon>ReviewManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ReviewManagement.aspx.designer.cs">
      <DependentUpon>ReviewManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ServiceRecordManagement.aspx.cs">
      <DependentUpon>ServiceRecordManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ServiceRecordManagement.aspx.designer.cs">
      <DependentUpon>ServiceRecordManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\ShopManagement.aspx.cs">
      <DependentUpon>ShopManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\ShopManagement.aspx.designer.cs">
      <DependentUpon>ShopManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\SystemSettings.aspx.cs">
      <DependentUpon>SystemSettings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\SystemSettings.aspx.designer.cs">
      <DependentUpon>SystemSettings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\UserManagement.aspx.cs">
      <DependentUpon>UserManagement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CarOwner\PaymentSimulation.aspx.cs">
      <DependentUpon>PaymentSimulation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CarOwner\PaymentSimulation.aspx.designer.cs">
      <DependentUpon>PaymentSimulation.aspx</DependentUpon>
    </Compile>
    <Compile Include="CarOwner\Profile.aspx.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CarOwner\Profile.aspx.designer.cs">
      <DependentUpon>Profile.aspx</DependentUpon>
    </Compile>
    <Compile Include="CarOwner\ServiceSearch.aspx.cs">
      <DependentUpon>ServiceSearch.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CarOwner\ServiceSearch.aspx.designer.cs">
      <DependentUpon>ServiceSearch.aspx</DependentUpon>
    </Compile>
    <Compile Include="ForgotPassword.aspx.cs">
      <DependentUpon>ForgotPassword.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ForgotPassword.aspx.designer.cs">
      <DependentUpon>ForgotPassword.aspx</DependentUpon>
    </Compile>

    <Compile Include="Admin\UserManagement.aspx.designer.cs">
      <DependentUpon>UserManagement.aspx</DependentUpon>
    </Compile>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="About.aspx.cs">
      <DependentUpon>About.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="About.aspx.designer.cs">
      <DependentUpon>About.aspx</DependentUpon>
    </Compile>
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="CarOwner\Appointments.aspx.cs">
      <DependentUpon>Appointments.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CarOwner\Appointments.aspx.designer.cs">
      <DependentUpon>Appointments.aspx</DependentUpon>
    </Compile>
    <Compile Include="CarOwner\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CarOwner\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="CarOwner\MyCars.aspx.cs">
      <DependentUpon>MyCars.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CarOwner\MyCars.aspx.designer.cs">
      <DependentUpon>MyCars.aspx</DependentUpon>
    </Compile>
    <Compile Include="CarOwner\ServiceHistory.aspx.cs">
      <DependentUpon>ServiceHistory.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CarOwner\ServiceHistory.aspx.designer.cs">
      <DependentUpon>ServiceHistory.aspx</DependentUpon>
    </Compile>
    <Compile Include="Contact.aspx.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Contact.aspx.designer.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Login.aspx.designer.cs">
      <DependentUpon>Login.aspx</DependentUpon>
    </Compile>
    <Compile Include="MasterPage.master.cs">
      <DependentUpon>MasterPage.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPage.master.designer.cs">
      <DependentUpon>MasterPage.master</DependentUpon>
    </Compile>

    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Register.aspx.cs">
      <DependentUpon>Register.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Register.aspx.designer.cs">
      <DependentUpon>Register.aspx</DependentUpon>
    </Compile>
    <Compile Include="RepairShop\Appointments.aspx.cs">
      <DependentUpon>Appointments.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="RepairShop\Appointments.aspx.designer.cs">
      <DependentUpon>Appointments.aspx</DependentUpon>
    </Compile>
    <Compile Include="RepairShop\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="RepairShop\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="RepairShop\ServiceRecords.aspx.cs">
      <DependentUpon>ServiceRecords.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="RepairShop\ServiceRecords.aspx.designer.cs">
      <DependentUpon>ServiceRecords.aspx</DependentUpon>
    </Compile>
    <Compile Include="RepairShop\Services.aspx.cs">
      <DependentUpon>Services.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="RepairShop\Services.aspx.designer.cs">
      <DependentUpon>Services.aspx</DependentUpon>
    </Compile>
    <Compile Include="Search.aspx.cs">
      <DependentUpon>Search.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Search.aspx.designer.cs">
      <DependentUpon>Search.aspx</DependentUpon>
    </Compile>
    <Compile Include="ShopDetails.aspx.cs">
      <DependentUpon>ShopDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ShopDetails.aspx.designer.cs">
      <DependentUpon>ShopDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="Site.Master.cs">
      <DependentUpon>Site.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Master.designer.cs">
      <DependentUpon>Site.Master</DependentUpon>
    </Compile>
    <Compile Include="Site.Mobile.Master.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Mobile.Master.designer.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.designer.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Images\ReviewPhotos\" />
    <Folder Include="Uploads\Avatars\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <Content Include="Scripts\jquery-3.7.0.slim.min.map" />
    <Content Include="Scripts\jquery-3.7.0.min.map" />
    <Content Include="Search.aspx.old" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != '' AND Exists('$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets')" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>51167</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44321/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用"NuGet 程序包还原"可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>