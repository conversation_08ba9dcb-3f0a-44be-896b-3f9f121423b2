using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Text;

public partial class Admin_ShopManagement : Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否已登录且为管理员
        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.RawUrl));
            return;
        }

        if (!IsPostBack)
        {
            // 加载维修店列表
            LoadShops();
        }
    }

    /// <summary>
    /// 加载维修店列表
    /// </summary>
    private void LoadShops()
    {
        try
        {
            string searchText = txtSearch.Text.Trim();

            // 获取维修店列表
            DataTable dt = AdminManager.GetRepairShops(searchText);
            gvShops.DataSource = dt;
            gvShops.DataBind();
        }
        catch (Exception ex)
        {
            lblMessage.Text = "加载维修店列表时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 搜索按钮点击事件
    /// </summary>
    protected void btnSearch_Click(object sender, EventArgs e)
    {
        // 重置分页索引
        gvShops.PageIndex = 0;
        
        // 加载维修店列表
        LoadShops();
        
        // 隐藏维修店详情面板
        pnlShopDetails.Visible = false;
    }

    /// <summary>
    /// 维修店列表行命令事件
    /// </summary>
    protected void gvShops_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        try
        {
            switch (e.CommandName)
            {
                case "ViewShop":
                    // 查看维修店详情
                    int shopID = Convert.ToInt32(e.CommandArgument);
                    ViewShopDetails(shopID);
                    break;
                    
                case "ToggleStatus":
                    // 切换维修店状态
                    int toggleShopID = Convert.ToInt32(e.CommandArgument);
                    ToggleShopStatus(toggleShopID);
                    break;
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "处理维修店操作时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 维修店列表分页事件
    /// </summary>
    protected void gvShops_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvShops.PageIndex = e.NewPageIndex;
        LoadShops();
    }

    /// <summary>
    /// 查看维修店详情
    /// </summary>
    private void ViewShopDetails(int shopID)
    {
        try
        {
            // 获取维修店详情
            DataTable dt = AdminManager.GetShopDetails(shopID);
            if (dt.Rows.Count > 0)
            {
                DataRow row = dt.Rows[0];
                
                // 设置维修店基本信息
                lblShopID.Text = row["ShopID"].ToString();
                lblShopName.Text = row["ShopName"].ToString();
                lblContactPerson.Text = row["ContactPerson"] != DBNull.Value ? row["ContactPerson"].ToString() : "未设置";
                lblPhone.Text = row["Phone"] != DBNull.Value ? row["Phone"].ToString() : "未设置";
                lblAddress.Text = row["Address"].ToString();
                lblUsername.Text = row["Username"].ToString();
                lblEmail.Text = row["Email"].ToString();
                lblStatus.Text = (bool)row["IsActive"] ? "已激活" : "已禁用";
                lblDescription.Text = row["Description"] != DBNull.Value ? row["Description"].ToString() : "未设置";
                
                // 获取维修店服务列表
                string servicesQuery = @"SELECT rs.ServiceID, rs.ServiceName, rs.BasePrice as Price, rs.IsActive, sc.CategoryName
                                        FROM RepairServices rs
                                        LEFT JOIN ServiceCategories sc ON rs.CategoryID = sc.CategoryID
                                        WHERE rs.ShopID = @ShopID";
                SqlParameter servicesParameter = new SqlParameter("@ShopID", shopID);
                DataTable servicesDt = DatabaseHelper.ExecuteQuery(servicesQuery, servicesParameter);
                
                gvServices.DataSource = servicesDt;
                gvServices.DataBind();
                
                // 获取维修店评价列表
                string reviewsQuery = @"SELECT r.ReviewID, r.Rating, r.Comments as Comment, r.ReviewDate, co.FullName as Username
                                        FROM Reviews r
                                        INNER JOIN CarOwners co ON r.OwnerID = co.OwnerID
                                        WHERE r.ShopID = @ShopID
                                        ORDER BY r.ReviewDate DESC";
                SqlParameter reviewsParameter = new SqlParameter("@ShopID", shopID);
                DataTable reviewsDt = DatabaseHelper.ExecuteQuery(reviewsQuery, reviewsParameter);
                
                gvReviews.DataSource = reviewsDt;
                gvReviews.DataBind();
                
                // 显示维修店详情面板
                pnlShopDetails.Visible = true;
            }
            else
            {
                lblMessage.Text = "找不到指定的维修店";
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "查看维修店详情时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 切换维修店状态
    /// </summary>
    private void ToggleShopStatus(int shopID)
    {
        try
        {
            // 获取维修店关联的UserID
            string userIDQuery = "SELECT UserID FROM RepairShops WHERE ShopID = @ShopID";
            SqlParameter userIDParam = new SqlParameter("@ShopID", shopID);
            object userIDResult = DatabaseHelper.ExecuteScalar(userIDQuery, userIDParam);
            
            if (userIDResult != null && userIDResult != DBNull.Value)
            {
                int userID = Convert.ToInt32(userIDResult);
                
                // 获取当前用户状态
                string query = "SELECT IsActive FROM Users WHERE UserID = @UserID";
                SqlParameter parameter = new SqlParameter("@UserID", userID);
                object result = DatabaseHelper.ExecuteScalar(query, parameter);
                
                if (result != null && result != DBNull.Value)
                {
                    bool currentStatus = Convert.ToBoolean(result);
                    
                    // 更新用户状态
                    bool updateResult = AdminManager.UpdateUserStatus(userID, !currentStatus);
                    if (updateResult)
                    {
                        lblMessage.Text = "维修店状态已更新";
                        lblMessage.CssClass = "text-success";
                    }
                    else
                    {
                        lblMessage.Text = "更新维修店状态失败";
                        lblMessage.CssClass = "text-danger";
                    }
                    
                    // 重新加载维修店列表
                    LoadShops();
                    
                    // 如果正在查看该维修店的详情，则关闭详情面板
                    pnlShopDetails.Visible = false;
                }
                else
                {
                    lblMessage.Text = "找不到指定的用户";
                    lblMessage.CssClass = "text-danger";
                }
            }
            else
            {
                lblMessage.Text = "找不到维修店关联的用户";
                lblMessage.CssClass = "text-danger";
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "切换维修店状态时出错：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 关闭详情按钮点击事件
    /// </summary>
    protected void lbtnCloseDetails_Click(object sender, EventArgs e)
    {
        pnlShopDetails.Visible = false;
    }

    /// <summary>
    /// 获取星级评分HTML
    /// </summary>
    protected string GetStarRating(int rating)
    {
        StringBuilder sb = new StringBuilder();
        
        // 添加实心星星
        for (int i = 0; i < rating; i++)
        {
            sb.Append("<i class=\"fas fa-star\"></i> ");
        }
        
        // 添加空心星星
        for (int i = rating; i < 5; i++)
        {
            sb.Append("<i class=\"far fa-star\"></i> ");
        }
        
        return sb.ToString();
    }
} 