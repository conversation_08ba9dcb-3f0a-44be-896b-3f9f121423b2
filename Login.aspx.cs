using System;
using System.Web.Security;
using System.Data.SqlClient;

public partial class Login : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // 确保UserLogins表存在
        if (!IsPostBack)
        {
            SecurityHelper.EnsureUserLoginsTableExists();
        }

        // 如果用户已经登录，则重定向到首页
        if (User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Default.aspx");
        }
    }

    protected void btnLogin_Click(object sender, EventArgs e)
    {
        string username = txtUsername.Text.Trim();
        string password = txtPassword.Text.Trim();

        try
        {
            // 1. 检查用户是否存在
            string checkUserQuery = "SELECT UserID FROM Users WHERE Username = @Username";
            SqlParameter checkParam = new SqlParameter("@Username", username);
            object userIDObj = DatabaseHelper.ExecuteScalar(checkUserQuery, checkParam);

            if (userIDObj == null || userIDObj == DBNull.Value)
            {
                // 用户不存在，显示错误但不透露用户是否存在
                lblMessage.Text = "用户名或密码错误。";
                lblMessage.CssClass = "text-danger";
                return;
            }

            int userID = Convert.ToInt32(userIDObj);

            // 2. 强制清除过期的锁定状态
            string clearExpiredLockQuery = @"
                UPDATE UserLogins 
                SET LockoutEndTime = NULL 
                WHERE UserID = @UserID 
                AND LockoutEndTime IS NOT NULL 
                AND LockoutEndTime <= GETDATE()";
            
            SqlParameter clearParam = new SqlParameter("@UserID", userID);
            int rowsAffected = DatabaseHelper.ExecuteNonQuery(clearExpiredLockQuery, clearParam);
            
            if (rowsAffected > 0)
            {
                // 记录解锁操作
                System.Diagnostics.Debug.WriteLine($"账户 {username}(ID:{userID}) 的锁定已过期，已自动解锁");
            }

            // 3. 检查账户是否仍然被锁定
            string checkLockQuery = @"
                SELECT LockoutEndTime 
                FROM UserLogins 
                WHERE UserID = @UserID 
                AND LockoutEndTime IS NOT NULL 
                AND LockoutEndTime > GETDATE()";
            
            SqlParameter lockParam = new SqlParameter("@UserID", userID);
            object lockEndTimeObj = DatabaseHelper.ExecuteScalar(checkLockQuery, lockParam);
            
            if (lockEndTimeObj != null && lockEndTimeObj != DBNull.Value)
            {
                // 账户仍处于锁定状态
                DateTime lockEndTime = Convert.ToDateTime(lockEndTimeObj);
                TimeSpan remainingTime = lockEndTime - DateTime.Now;
                
                lblMessage.Text = $"账户因多次登录失败已被临时锁定，请在 {Math.Ceiling(remainingTime.TotalMinutes)} 分钟后再试。";
                lblMessage.CssClass = "text-danger";
                
                System.Diagnostics.Debug.WriteLine($"账户 {username}(ID:{userID}) 仍被锁定，解锁时间: {lockEndTime:yyyy-MM-dd HH:mm:ss}");
                return;
            }

            // 4. 验证用户凭据
            UserInfo userInfo = UserManager.ValidateUser(username, password);
            
            if (userInfo != null)
            {
                // 登录成功，重置登录尝试次数
                string resetAttemptsQuery = @"
                    UPDATE UserLogins SET 
                    FailedAttempts = 0, 
                    LastLoginAttempt = GETDATE(),
                    LockoutEndTime = NULL
                    WHERE UserID = @UserID;

                    IF @@ROWCOUNT = 0
                    INSERT INTO UserLogins (UserID, FailedAttempts, LastLoginAttempt)
                    VALUES (@UserID, 0, GETDATE())";
                
                SqlParameter resetParam = new SqlParameter("@UserID", userID);
                DatabaseHelper.ExecuteNonQuery(resetAttemptsQuery, resetParam);
                
                System.Diagnostics.Debug.WriteLine($"用户 {username}(ID:{userID}) 登录成功，重置尝试次数");

                // 记录用户登录状态
                FormsAuthentication.SetAuthCookie(username, false);
                // 在Session中保存用户信息
                Session["UserID"] = userInfo.UserID;
                Session["UserType"] = userInfo.UserType;
                Session["Username"] = userInfo.Username;

                // 根据用户类型重定向到不同的页面
                switch (userInfo.UserType)
                {
                    case "CarOwner":
                        Response.Redirect("~/CarOwner/Dashboard.aspx");
                        break;
                    case "RepairShop":
                        Response.Redirect("~/RepairShop/Dashboard.aspx");
                        break;
                    case "Admin":
                        Response.Redirect("~/Admin/Dashboard.aspx");
                        break;
                    default:
                        Response.Redirect("~/Default.aspx");
                        break;
                }
            }
            else
            {
                // 登录失败，更新失败次数
                // 获取设置
                int maxAttempts = Convert.ToInt32(SecurityHelper.GetSystemSetting("MaxLoginAttempts", "5"));
                int lockDuration = Convert.ToInt32(SecurityHelper.GetSystemSetting("LockoutDuration", "30"));
                
                // 增加失败次数
                string failedQuery = @"
                    UPDATE UserLogins SET 
                    FailedAttempts = ISNULL(FailedAttempts, 0) + 1,
                    LastLoginAttempt = GETDATE()
                    WHERE UserID = @UserID;
                    
                    IF @@ROWCOUNT = 0
                    INSERT INTO UserLogins (UserID, FailedAttempts, LastLoginAttempt)
                    VALUES (@UserID, 1, GETDATE());
                    
                    SELECT FailedAttempts FROM UserLogins WHERE UserID = @UserID";

                SqlParameter failedParam = new SqlParameter("@UserID", userID);
                int failedAttempts = Convert.ToInt32(DatabaseHelper.ExecuteScalar(failedQuery, failedParam));
                
                int remainingAttempts = maxAttempts - failedAttempts;
                
                System.Diagnostics.Debug.WriteLine($"用户 {username}(ID:{userID}) 登录失败，已失败 {failedAttempts} 次，还剩 {remainingAttempts} 次尝试机会");
                
                // 如果达到最大尝试次数，锁定账户
                if (failedAttempts >= maxAttempts)
                {
                    string lockQuery = @"
                        UPDATE UserLogins SET 
                        LockoutEndTime = DATEADD(MINUTE, @LockDuration, GETDATE())
                        WHERE UserID = @UserID";
                    
                    SqlParameter[] lockParams =
                    {
                        new SqlParameter("@UserID", userID),
                        new SqlParameter("@LockDuration", lockDuration)
                    };
                    
                    DatabaseHelper.ExecuteNonQuery(lockQuery, lockParams);
                    
                    DateTime lockEndTime = DateTime.Now.AddMinutes(lockDuration);
                    System.Diagnostics.Debug.WriteLine($"账户 {username}(ID:{userID}) 已被锁定，解锁时间: {lockEndTime:yyyy-MM-dd HH:mm:ss}");
                    
                    lblMessage.Text = $"账户因多次登录失败已被临时锁定，请在 {lockDuration} 分钟后再试。";
                }
                else
                {
                    lblMessage.Text = $"用户名或密码错误，您还有 {remainingAttempts} 次尝试机会。";
                }
                
                lblMessage.CssClass = "text-danger";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"登录过程中出错: {ex.Message}");
            lblMessage.Text = "登录过程中发生错误，请稍后再试。";
            lblMessage.CssClass = "text-danger";
        }
    }
} 