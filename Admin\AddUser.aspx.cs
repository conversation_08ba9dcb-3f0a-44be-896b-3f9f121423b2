using System;
using System.Data;
using System.Web.UI;
using System.Text.RegularExpressions;

public partial class Admin_AddUser : Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否已登录且为管理员
        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.RawUrl));
            return;
        }

        if (!IsPostBack)
        {
            // 如果URL中包含用户类型参数，则预选中
            if (!string.IsNullOrEmpty(Request.QueryString["type"]))
            {
                string userType = Request.QueryString["type"];
                ddlUserType.SelectedValue = userType;
                UpdatePanelsVisibility();
            }
            
            // 设置验证器的服务器端验证
            cvUsername.ServerValidate += CvUsername_ServerValidate;
            cvPhoneNumber.ServerValidate += CvPhoneNumber_ServerValidate;
        }
    }
    
    /// <summary>
    /// 用户名服务器端验证
    /// </summary>
    private void CvUsername_ServerValidate(object source, System.Web.UI.WebControls.ServerValidateEventArgs args)
    {
        // 用户名必须为4-20个字符，仅支持字母、数字和下划线
        string username = args.Value;
        Regex usernameRegex = new Regex(@"^[a-zA-Z0-9_]{4,20}$");
        args.IsValid = usernameRegex.IsMatch(username);
    }

    /// <summary>
    /// 手机号服务器端验证
    /// </summary>
    private void CvPhoneNumber_ServerValidate(object source, System.Web.UI.WebControls.ServerValidateEventArgs args)
    {
        // 如果手机号为空则不验证（可选字段）
        if (string.IsNullOrEmpty(args.Value))
        {
            args.IsValid = true;
            return;
        }

        // 验证是否为有效的中国手机号码（11位数字，以1开头）
        string phoneNumber = args.Value;
        Regex phoneRegex = new Regex(@"^1[3-9]\d{9}$");
        args.IsValid = phoneRegex.IsMatch(phoneNumber);
    }

    /// <summary>
    /// 用户类型下拉框改变事件
    /// </summary>
    protected void ddlUserType_SelectedIndexChanged(object sender, EventArgs e)
    {
        UpdatePanelsVisibility();
    }

    /// <summary>
    /// 根据用户类型更新面板可见性
    /// </summary>
    private void UpdatePanelsVisibility()
    {
        string userType = ddlUserType.SelectedValue;

        pnlCarOwner.Visible = (userType == "CarOwner");
        pnlRepairShop.Visible = (userType == "RepairShop");
        pnlAdmin.Visible = (userType == "Admin");

        // 根据用户类型设置验证控件启用/禁用
        rfvFullName.Enabled = (userType == "CarOwner");
        rfvShopName.Enabled = (userType == "RepairShop");
        rfvShopAddress.Enabled = (userType == "RepairShop");
    }

    /// <summary>
    /// 保存按钮点击事件
    /// </summary>
    protected void btnSave_Click(object sender, EventArgs e)
    {
        // 首先验证表单
        if (!Page.IsValid)
        {
            return;
        }

        try
        {
            // 获取用户基本信息
            string username = txtUsername.Text.Trim();
            string password = txtPassword.Text.Trim();
            string email = txtEmail.Text.Trim();
            string phoneNumber = txtPhoneNumber.Text.Trim();
            string userType = ddlUserType.SelectedValue;
            bool isActive = chkIsActive.Checked;

            // 检查用户名是否已存在
            if (UserManager.IsUsernameExists(username))
            {
                lblMessage.Text = "用户名已被使用，请选择其他用户名。";
                return;
            }

            // 检查邮箱是否已存在
            if (UserManager.IsEmailExists(email))
            {
                lblMessage.Text = "此邮箱已被注册，请使用其他邮箱。";
                return;
            }

            // 注册用户
            int userId = UserManager.RegisterUser(username, password, email, phoneNumber, userType);
            
            if (userId > 0)
            {
                // 根据用户类型处理额外信息
                switch (userType)
                {
                    case "CarOwner":
                        // 更新车主信息
                        UpdateCarOwnerInfo(userId);
                        break;
                    case "RepairShop":
                        // 更新维修店信息
                        UpdateRepairShopInfo(userId);
                        break;
                }

                // 注册成功，重定向到用户管理页面
                Response.Redirect("UserManagement.aspx?msg=added");
            }
            else
            {
                lblMessage.Text = "添加用户失败，请稍后再试。";
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "添加用户时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 更新车主信息
    /// </summary>
    private void UpdateCarOwnerInfo(int userId)
    {
        string fullName = txtFullName.Text.Trim();
        string address = txtAddress.Text.Trim();

        // 获取车主ID
        string query = "SELECT OwnerID FROM CarOwners WHERE UserID = @UserID";
        System.Data.SqlClient.SqlParameter parameter = new System.Data.SqlClient.SqlParameter("@UserID", userId);
        object result = DatabaseHelper.ExecuteScalar(query, parameter);

        if (result != null && result != DBNull.Value)
        {
            int ownerId = Convert.ToInt32(result);

            // 更新车主信息
            string updateQuery = "UPDATE CarOwners SET FullName = @FullName, Address = @Address WHERE OwnerID = @OwnerID";
            System.Data.SqlClient.SqlParameter[] parameters =
            {
                new System.Data.SqlClient.SqlParameter("@FullName", fullName),
                new System.Data.SqlClient.SqlParameter("@Address", address ?? (object)DBNull.Value),
                new System.Data.SqlClient.SqlParameter("@OwnerID", ownerId)
            };

            DatabaseHelper.ExecuteNonQuery(updateQuery, parameters);
        }
    }

    /// <summary>
    /// 更新维修店信息
    /// </summary>
    private void UpdateRepairShopInfo(int userId)
    {
        string shopName = txtShopName.Text.Trim();
        string shopAddress = txtShopAddress.Text.Trim();
        string contactPerson = txtContactPerson.Text.Trim();
        string description = txtDescription.Text.Trim();

        // 获取维修店ID
        string query = "SELECT ShopID FROM RepairShops WHERE UserID = @UserID";
        System.Data.SqlClient.SqlParameter parameter = new System.Data.SqlClient.SqlParameter("@UserID", userId);
        object result = DatabaseHelper.ExecuteScalar(query, parameter);

        if (result != null && result != DBNull.Value)
        {
            int shopId = Convert.ToInt32(result);

            // 更新维修店信息
            string updateQuery = @"UPDATE RepairShops 
                                  SET ShopName = @ShopName, 
                                      Address = @Address,
                                      ContactPerson = @ContactPerson,
                                      Description = @Description
                                  WHERE ShopID = @ShopID";

            System.Data.SqlClient.SqlParameter[] parameters =
            {
                new System.Data.SqlClient.SqlParameter("@ShopName", shopName),
                new System.Data.SqlClient.SqlParameter("@Address", shopAddress),
                new System.Data.SqlClient.SqlParameter("@ContactPerson", contactPerson ?? (object)DBNull.Value),
                new System.Data.SqlClient.SqlParameter("@Description", description ?? (object)DBNull.Value),
                new System.Data.SqlClient.SqlParameter("@ShopID", shopId)
            };

            DatabaseHelper.ExecuteNonQuery(updateQuery, parameters);
        }
    }
} 