<%@ Page Title="预约管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="True" CodeBehind="AppointmentManagement.aspx.cs" Inherits="WebApplication1.Admin.AppointmentManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-md-6">
                <h2><i class="fas fa-calendar-alt"></i> 预约管理</h2>
            </div>
            <div class="col-md-6 text-right">
                <asp:LinkButton ID="lbtnBackToDashboard" runat="server" CssClass="btn btn-secondary" OnClick="lbtnBackToDashboard_Click">
                    <i class="fas fa-arrow-left"></i> 返回控制台
                </asp:LinkButton>
            </div>
        </div>

        <!-- 消息提示区域 -->
        <div class="row mb-3">
            <div class="col-12">
                <asp:Panel ID="pnlMessage" runat="server" Visible="false">
                    <asp:Label ID="lblMessage" runat="server" CssClass="alert"></asp:Label>
                </asp:Panel>
            </div>
        </div>

        <!-- 搜索和过滤区域 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-search"></i> 搜索和过滤</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="ddlStatus">预约状态</label>
                            <asp:DropDownList ID="ddlStatus" runat="server" CssClass="form-control">
                                <asp:ListItem Value="" Text="-- 所有状态 --" Selected="True" />
                                <asp:ListItem Value="Pending" Text="待确认" />
                                <asp:ListItem Value="Confirmed" Text="已确认" />
                                <asp:ListItem Value="Completed" Text="已完成" />
                                <asp:ListItem Value="Cancelled" Text="已取消" />
                            </asp:DropDownList>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="txtStartDate">开始日期</label>
                            <asp:TextBox ID="txtStartDate" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="txtEndDate">结束日期</label>
                            <asp:TextBox ID="txtEndDate" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="txtSearch">搜索</label>
                            <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="车主/维修店名称"></asp:TextBox>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <asp:Button ID="btnSearch" runat="server" Text="搜索" CssClass="btn btn-primary" OnClick="btnSearch_Click" />
                        <asp:Button ID="btnReset" runat="server" Text="重置" CssClass="btn btn-outline-secondary ml-2" OnClick="btnReset_Click" />
                    </div>
                </div>
            </div>
        </div>

        <!-- 预约列表 -->
        <asp:Panel ID="pnlAppointmentList" runat="server">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> 预约列表</h5>
                    <span class="badge badge-light">共 <asp:Label ID="lblTotalCount" runat="server" Text="0"></asp:Label> 条记录</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <asp:GridView ID="gvAppointments" runat="server" AutoGenerateColumns="False" CssClass="table table-striped table-hover" 
                            DataKeyNames="AppointmentID" OnRowCommand="gvAppointments_RowCommand" 
                            OnRowDataBound="gvAppointments_RowDataBound" AllowPaging="True" PageSize="10" 
                            OnPageIndexChanging="gvAppointments_PageIndexChanging" EmptyDataText="没有找到预约记录" 
                            PagerStyle-CssClass="pagination-container" PagerSettings-Mode="NumericFirstLast">
                            <Columns>
                                <asp:BoundField DataField="AppointmentID" HeaderText="ID" ItemStyle-Width="50px" />
                                <asp:BoundField DataField="AppointmentDate" HeaderText="预约日期" DataFormatString="{0:yyyy-MM-dd HH:mm}" />
                                <asp:BoundField DataField="OwnerName" HeaderText="车主" />
                                <asp:BoundField DataField="CarInfo" HeaderText="车辆" />
                                <asp:BoundField DataField="ShopName" HeaderText="维修店" />
                                <asp:BoundField DataField="ServiceName" HeaderText="服务项目" />
                                <asp:TemplateField HeaderText="状态">
                                    <ItemTemplate>
                                        <asp:Label ID="lblStatus" runat="server" Text='<%# GetStatusText(Eval("Status").ToString()) %>' CssClass='<%# GetStatusCssClass(Eval("Status").ToString()) %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="操作" ItemStyle-Width="180px">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lbtnView" runat="server" CssClass="btn btn-sm btn-info" 
                                            CommandName="ViewAppointment" CommandArgument='<%# Eval("AppointmentID") %>' ToolTip="查看">
                                            <i class="fas fa-eye"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="lbtnEdit" runat="server" CssClass="btn btn-sm btn-primary" 
                                            CommandName="EditAppointment" CommandArgument='<%# Eval("AppointmentID") %>' ToolTip="编辑">
                                            <i class="fas fa-edit"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="lbtnDelete" runat="server" CssClass="btn btn-sm btn-danger" 
                                            CommandName="DeleteAppointment" CommandArgument='<%# Eval("AppointmentID") %>' 
                                            OnClientClick="return confirm('确定要删除此预约记录吗？此操作不可恢复。');" ToolTip="删除">
                                            <i class="fas fa-trash"></i>
                                        </asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle CssClass="pagination-container" HorizontalAlign="Center" />
                            <PagerSettings Mode="NumericFirstLast" FirstPageText="首页" LastPageText="末页" />
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <!-- 查看/编辑预约表单 -->
        <asp:Panel ID="pnlAppointmentDetails" runat="server" Visible="false">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <asp:Literal ID="litFormTitle" runat="server" Text="预约详情"></asp:Literal>
                    </h5>
                </div>
                <div class="card-body">
                    <asp:HiddenField ID="hfAppointmentID" runat="server" />
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="lblCarInfo">车辆信息</label>
                                <asp:Label ID="lblCarInfo" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                                <asp:DropDownList ID="ddlCars" runat="server" CssClass="form-control" Visible="false"></asp:DropDownList>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="lblOwnerName">车主</label>
                                <asp:Label ID="lblOwnerName" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="lblShopName">维修店</label>
                                <asp:Label ID="lblShopName" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                                <asp:DropDownList ID="ddlShops" runat="server" CssClass="form-control" Visible="false" AutoPostBack="true" OnSelectedIndexChanged="ddlShops_SelectedIndexChanged"></asp:DropDownList>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="lblServiceName">服务项目</label>
                                <asp:Label ID="lblServiceName" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                                <asp:DropDownList ID="ddlServices" runat="server" CssClass="form-control" Visible="false"></asp:DropDownList>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="lblAppointmentDate">预约时间</label>
                                <asp:Label ID="lblAppointmentDate" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                                <asp:TextBox ID="txtAppointmentDate" runat="server" CssClass="form-control" TextMode="DateTimeLocal" Visible="false"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvAppointmentDate" runat="server" ControlToValidate="txtAppointmentDate"
                                    ErrorMessage="请选择预约时间" Display="Dynamic" CssClass="text-danger" ValidationGroup="AppointmentEdit"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="lblStatus">状态</label>
                                <asp:Label ID="lblStatus" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                                <asp:DropDownList ID="ddlAppointmentStatus" runat="server" CssClass="form-control" Visible="false">
                                    <asp:ListItem Value="Pending" Text="待确认" />
                                    <asp:ListItem Value="Confirmed" Text="已确认" />
                                    <asp:ListItem Value="Completed" Text="已完成" />
                                    <asp:ListItem Value="Cancelled" Text="已取消" />
                                </asp:DropDownList>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="lblDescription">问题描述</label>
                        <asp:Label ID="lblDescription" runat="server" CssClass="form-control" ReadOnly="true" TextMode="MultiLine" Rows="3"></asp:Label>
                        <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" Visible="false"></asp:TextBox>
                    </div>
                    
                    <div class="form-group">
                        <label>创建时间</label>
                        <asp:Label ID="lblCreatedDate" runat="server" CssClass="form-control" ReadOnly="true"></asp:Label>
                    </div>
                    
                    <div class="mt-3">
                        <asp:Button ID="btnEdit" runat="server" Text="编辑" CssClass="btn btn-primary" OnClick="btnEdit_Click" />
                        <asp:Button ID="btnSave" runat="server" Text="保存" CssClass="btn btn-success" ValidationGroup="AppointmentEdit" OnClick="btnSave_Click" Visible="false" />
                        <asp:Button ID="btnCancel" runat="server" Text="取消" CssClass="btn btn-outline-secondary ml-2" OnClick="btnCancel_Click" />
                        <asp:Button ID="btnDelete" runat="server" Text="删除预约" CssClass="btn btn-danger float-right" OnClick="btnDelete_Click" OnClientClick="return confirm('确定要删除此预约记录吗？此操作不可恢复。');" />
                    </div>
                </div>
            </div>
        </asp:Panel>
    </div>
</asp:Content> 