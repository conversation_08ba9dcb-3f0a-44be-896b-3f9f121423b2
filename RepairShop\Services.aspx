<%@ Page Title="服务管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="RepairShop_Services" Codebehind="Services.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-tools"></i> 服务管理</h2>
                <hr />
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list"></i> 我的服务列表</h5>
                            <asp:Button ID="btnAddService" runat="server" Text="添加服务" CssClass="btn btn-light" OnClick="btnAddService_Click" />
                        </div>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvServices" runat="server" AutoGenerateColumns="False"
                            CssClass="table table-striped table-hover" DataKeyNames="ServiceID" 
                            OnRowCommand="gvServices_RowCommand" EmptyDataText="尚未添加任何服务">
                            <Columns>
                                <asp:BoundField DataField="ServiceName" HeaderText="服务名称" />
                                <asp:BoundField DataField="CategoryName" HeaderText="类别" />
                                <asp:BoundField DataField="Description" HeaderText="描述" />
                                <asp:BoundField DataField="EstimatedTime" HeaderText="预计时间(分钟)" />
                                <asp:BoundField DataField="BasePrice" HeaderText="基础价格" DataFormatString="{0:C}" />
                                <asp:TemplateField HeaderText="状态">
                                    <ItemTemplate>
                                        <asp:Label ID="lblStatus" runat="server" Text='<%# (bool)Eval("IsActive") ? "激活" : "停用" %>'
                                            CssClass='<%# (bool)Eval("IsActive") ? "badge badge-success" : "badge badge-danger" %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lbtnEdit" runat="server" CssClass="btn btn-sm btn-primary"
                                            CommandName="EditService" CommandArgument='<%# Eval("ServiceID") %>' ToolTip="编辑">
                                            <i class="fas fa-edit"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="lbtnToggle" runat="server" CssClass='<%# (bool)Eval("IsActive") ? "btn btn-sm btn-warning" : "btn btn-sm btn-success" %>'
                                            CommandName="ToggleStatus" CommandArgument='<%# Eval("ServiceID") %>' 
                                            ToolTip='<%# (bool)Eval("IsActive") ? "停用" : "激活" %>'>
                                            <i class='<%# (bool)Eval("IsActive") ? "fas fa-ban" : "fas fa-check" %>'></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="lbtnDelete" runat="server" CssClass="btn btn-sm btn-danger"
                                            CommandName="DeleteService" CommandArgument='<%# Eval("ServiceID") %>' ToolTip="删除"
                                            OnClientClick="return confirm('确定要删除此服务吗？此操作不可恢复。');">
                                            <i class="fas fa-trash"></i>
                                        </asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>

        <asp:Panel ID="pnlAddEdit" runat="server" Visible="false">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <asp:Literal ID="litFormTitle" runat="server"></asp:Literal>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="form-group row">
                                <label for="ddlCategory" class="col-sm-3 col-form-label">服务类别：</label>
                                <div class="col-sm-9">
                                    <asp:DropDownList ID="ddlCategory" runat="server" CssClass="form-control" DataTextField="CategoryName" DataValueField="CategoryID">
                                    </asp:DropDownList>
                                    <asp:RequiredFieldValidator ID="rfvCategory" runat="server" ControlToValidate="ddlCategory"
                                        ErrorMessage="请选择服务类别" Display="Dynamic" CssClass="text-danger" ValidationGroup="ServiceForm"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtServiceName" class="col-sm-3 col-form-label">服务名称：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtServiceName" runat="server" CssClass="form-control"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvServiceName" runat="server" ControlToValidate="txtServiceName"
                                        ErrorMessage="服务名称不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="ServiceForm"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtDescription" class="col-sm-3 col-form-label">服务描述：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3"></asp:TextBox>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtEstimatedTime" class="col-sm-3 col-form-label">预计时间(分钟)：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtEstimatedTime" runat="server" CssClass="form-control" TextMode="Number"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvEstimatedTime" runat="server" ControlToValidate="txtEstimatedTime"
                                        ErrorMessage="预计时间不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="ServiceForm"></asp:RequiredFieldValidator>
                                    <asp:RangeValidator ID="rvEstimatedTime" runat="server" ControlToValidate="txtEstimatedTime"
                                        ErrorMessage="请输入有效的时间（5-480分钟）" Display="Dynamic" CssClass="text-danger" ValidationGroup="ServiceForm"
                                        MinimumValue="5" MaximumValue="480" Type="Integer"></asp:RangeValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="txtBasePrice" class="col-sm-3 col-form-label">基础价格(元)：</label>
                                <div class="col-sm-9">
                                    <asp:TextBox ID="txtBasePrice" runat="server" CssClass="form-control"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvBasePrice" runat="server" ControlToValidate="txtBasePrice"
                                        ErrorMessage="基础价格不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="ServiceForm"></asp:RequiredFieldValidator>
                                    <asp:RegularExpressionValidator ID="revBasePrice" runat="server" ControlToValidate="txtBasePrice"
                                        ErrorMessage="请输入有效的价格" Display="Dynamic" CssClass="text-danger" ValidationGroup="ServiceForm"
                                        ValidationExpression="^\d+(\.\d{1,2})?$"></asp:RegularExpressionValidator>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label for="chkIsActive" class="col-sm-3 col-form-label">立即启用：</label>
                                <div class="col-sm-9">
                                    <div class="form-check mt-2">
                                        <asp:CheckBox ID="chkIsActive" runat="server" Checked="true" />
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-9 offset-sm-3">
                                    <asp:Button ID="btnSave" runat="server" Text="保存" CssClass="btn btn-primary" ValidationGroup="ServiceForm" OnClick="btnSave_Click" />
                                    <asp:Button ID="btnCancel" runat="server" Text="取消" CssClass="btn btn-secondary ml-2" OnClick="btnCancel_Click" CausesValidation="false" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <div class="row mt-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
            </div>
        </div>
    </div>
</asp:Content> 