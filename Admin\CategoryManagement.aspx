<%@ Page Title="服务类别管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="Admin_CategoryManagement" Codebehind="CategoryManagement.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-tags"></i> 服务类别管理</h2>
                <hr />
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-plus-circle"></i> <asp:Literal ID="litCardTitle" runat="server" Text="添加服务类别"></asp:Literal></h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="txtCategoryName">类别名称：</label>
                            <asp:TextBox ID="txtCategoryName" runat="server" CssClass="form-control" MaxLength="50"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvCategoryName" runat="server" ControlToValidate="txtCategoryName"
                                ErrorMessage="类别名称不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="Category"></asp:RequiredFieldValidator>
                        </div>
                        <div class="form-group">
                            <label for="txtDescription">描述：</label>
                            <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3"></asp:TextBox>
                        </div>
                        <div class="form-group">
                            <asp:Button ID="btnSave" runat="server" Text="保存" CssClass="btn btn-primary" OnClick="btnSave_Click" ValidationGroup="Category" />
                            <asp:Button ID="btnCancel" runat="server" Text="取消" CssClass="btn btn-secondary ml-2" OnClick="btnCancel_Click" Visible="false" CausesValidation="false" />
                            <asp:HiddenField ID="hfCategoryID" runat="server" Value="0" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-list"></i> 服务类别列表</h5>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvCategories" runat="server" AutoGenerateColumns="False" CssClass="table table-striped table-bordered"
                            OnRowCommand="gvCategories_RowCommand" DataKeyNames="CategoryID">
                            <Columns>
                                <asp:BoundField DataField="CategoryID" HeaderText="ID" />
                                <asp:BoundField DataField="CategoryName" HeaderText="类别名称" />
                                <asp:BoundField DataField="Description" HeaderText="描述" />
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lbtnEdit" runat="server" CssClass="btn btn-sm btn-info" CommandName="EditCategory" CommandArgument='<%# Eval("CategoryID") %>'>
                                            <i class="fas fa-edit"></i> 编辑
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="lbtnDelete" runat="server" CssClass="btn btn-sm btn-danger" CommandName="DeleteCategory" CommandArgument='<%# Eval("CategoryID") %>'
                                            OnClientClick="return confirm('确定要删除此类别吗？如果该类别已被服务使用，则无法删除。');">
                                            <i class="fas fa-trash"></i> 删除
                                        </asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <EmptyDataTemplate>
                                <div class="alert alert-info">
                                    暂无服务类别
                                </div>
                            </EmptyDataTemplate>
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
            </div>
        </div>
    </div>
</asp:Content> 