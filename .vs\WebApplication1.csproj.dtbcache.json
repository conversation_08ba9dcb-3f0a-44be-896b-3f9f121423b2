{"RootPath": "D:\\try\\WebApplication1", "ProjectFileName": "WebApplication1.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Admin\\AddUser.aspx.cs"}, {"SourceFile": "Admin\\AddUser.aspx.designer.cs"}, {"SourceFile": "Admin\\AppointmentManagement.aspx.cs"}, {"SourceFile": "Admin\\AppointmentManagement.aspx.designer.cs"}, {"SourceFile": "Admin\\CategoryManagement.aspx.cs"}, {"SourceFile": "Admin\\CategoryManagement.aspx.designer.cs"}, {"SourceFile": "Admin\\Dashboard.aspx.cs"}, {"SourceFile": "Admin\\Dashboard.aspx.designer.cs"}, {"SourceFile": "Admin\\ReviewManagement.aspx.cs"}, {"SourceFile": "Admin\\ReviewManagement.aspx.designer.cs"}, {"SourceFile": "Admin\\ServiceRecordManagement.aspx.cs"}, {"SourceFile": "Admin\\ServiceRecordManagement.aspx.designer.cs"}, {"SourceFile": "Admin\\ShopManagement.aspx.cs"}, {"SourceFile": "Admin\\ShopManagement.aspx.designer.cs"}, {"SourceFile": "Admin\\SystemSettings.aspx.cs"}, {"SourceFile": "Admin\\SystemSettings.aspx.designer.cs"}, {"SourceFile": "Admin\\UserManagement.aspx.cs"}, {"SourceFile": "CarOwner\\Profile.aspx.cs"}, {"SourceFile": "CarOwner\\Profile.aspx.designer.cs"}, {"SourceFile": "ForgotPassword.aspx.cs"}, {"SourceFile": "ForgotPassword.aspx.designer.cs"}, {"SourceFile": "Old_App_Code\\AdminManager.cs"}, {"SourceFile": "Old_App_Code\\AppointmentManager.cs"}, {"SourceFile": "Old_App_Code\\CarManager.cs"}, {"SourceFile": "Old_App_Code\\DatabaseHelper.cs"}, {"SourceFile": "Old_App_Code\\FileUploadHelper.cs"}, {"SourceFile": "Old_App_Code\\PhotoManager.cs"}, {"SourceFile": "Old_App_Code\\SecurityHelper.cs"}, {"SourceFile": "Old_App_Code\\ShopManager.cs"}, {"SourceFile": "Old_App_Code\\UserInfo.cs"}, {"SourceFile": "Old_App_Code\\UserManager.cs"}, {"SourceFile": "Admin\\UserManagement.aspx.designer.cs"}, {"SourceFile": "App_Start\\BundleConfig.cs"}, {"SourceFile": "About.aspx.cs"}, {"SourceFile": "About.aspx.designer.cs"}, {"SourceFile": "App_Start\\RouteConfig.cs"}, {"SourceFile": "CarOwner\\Appointments.aspx.cs"}, {"SourceFile": "CarOwner\\Appointments.aspx.designer.cs"}, {"SourceFile": "CarOwner\\Dashboard.aspx.cs"}, {"SourceFile": "CarOwner\\Dashboard.aspx.designer.cs"}, {"SourceFile": "CarOwner\\MyCars.aspx.cs"}, {"SourceFile": "CarOwner\\MyCars.aspx.designer.cs"}, {"SourceFile": "CarOwner\\ServiceHistory.aspx.cs"}, {"SourceFile": "CarOwner\\ServiceHistory.aspx.designer.cs"}, {"SourceFile": "Contact.aspx.cs"}, {"SourceFile": "Contact.aspx.designer.cs"}, {"SourceFile": "Default.aspx.cs"}, {"SourceFile": "Default.aspx.designer.cs"}, {"SourceFile": "Global.asax.cs"}, {"SourceFile": "Login.aspx.cs"}, {"SourceFile": "Login.aspx.designer.cs"}, {"SourceFile": "MasterPage.master.cs"}, {"SourceFile": "MasterPage.master.designer.cs"}, {"SourceFile": "Old_App_Code\\VerificationHelper.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Register.aspx.cs"}, {"SourceFile": "Register.aspx.designer.cs"}, {"SourceFile": "RepairShop\\Appointments.aspx.cs"}, {"SourceFile": "RepairShop\\Appointments.aspx.designer.cs"}, {"SourceFile": "RepairShop\\Dashboard.aspx.cs"}, {"SourceFile": "RepairShop\\Dashboard.aspx.designer.cs"}, {"SourceFile": "RepairShop\\ServiceRecords.aspx.cs"}, {"SourceFile": "RepairShop\\ServiceRecords.aspx.designer.cs"}, {"SourceFile": "RepairShop\\Services.aspx.cs"}, {"SourceFile": "RepairShop\\Services.aspx.designer.cs"}, {"SourceFile": "Search.aspx.cs"}, {"SourceFile": "Search.aspx.designer.cs"}, {"SourceFile": "ShopDetails.aspx.cs"}, {"SourceFile": "ShopDetails.aspx.designer.cs"}, {"SourceFile": "Site.Master.cs"}, {"SourceFile": "Site.Master.designer.cs"}, {"SourceFile": "Site.Mobile.Master.cs"}, {"SourceFile": "Site.Mobile.Master.designer.cs"}, {"SourceFile": "ViewSwitcher.ascx.cs"}, {"SourceFile": "ViewSwitcher.ascx.designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\try\\WebApplication1\\packages\\Antlr.3.5.0.2\\lib\\Antlr3.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\try\\WebApplication1\\packages\\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\\lib\\net45\\Microsoft.AspNet.FriendlyUrls.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\try\\WebApplication1\\packages\\Microsoft.AspNet.Web.Optimization.WebForms.1.1.3\\lib\\net45\\Microsoft.AspNet.Web.Optimization.WebForms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\try\\WebApplication1\\packages\\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\\lib\\net45\\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\try\\WebApplication1\\packages\\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\\lib\\net45\\Microsoft.ScriptManager.MSAjax.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\try\\WebApplication1\\packages\\Microsoft.AspNet.ScriptManager.WebForms.5.0.0\\lib\\net45\\Microsoft.ScriptManager.WebForms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\try\\WebApplication1\\packages\\Microsoft.Web.Infrastructure.2.0.0\\lib\\net40\\Microsoft.Web.Infrastructure.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\try\\WebApplication1\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.EnterpriseServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.ApplicationServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.DynamicData.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Entity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\try\\WebApplication1\\packages\\Microsoft.AspNet.Web.Optimization.1.1.3\\lib\\net40\\System.Web.Optimization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Services.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\try\\WebApplication1\\packages\\WebGrease.1.6.0\\lib\\WebGrease.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\try\\WebApplication1\\bin\\WebApplication1.dll", "OutputItemRelativePath": "WebApplication1.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}