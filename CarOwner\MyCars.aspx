<%@ Page Title="我的车辆" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="CarOwner_MyCars" Codebehind="MyCars.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-3">
            <div class="col-md-8">
                <h2><i class="fas fa-car"></i> 我的车辆</h2>
            </div>
            <div class="col-md-4 text-right">
                <asp:Button ID="btnAddCar" runat="server" Text="添加新车辆" CssClass="btn btn-primary" OnClick="btnAddCar_Click" />
            </div>
        </div>

        <div class="row">
            <div class="col">
                <asp:Panel ID="pnlAddEditCar" runat="server" Visible="false" CssClass="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <asp:Literal ID="litPanelTitle" runat="server"></asp:Literal>
                        </h5>
                    </div>
                    <div class="card-body">
                        <asp:HiddenField ID="hfCarID" runat="server" />

                        <div class="form-group row">
                            <label for="txtMake" class="col-sm-3 col-form-label">厂商</label>
                            <div class="col-sm-9">
                                <asp:TextBox ID="txtMake" runat="server" CssClass="form-control" placeholder="请输入汽车厂商"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvMake" runat="server" ControlToValidate="txtMake"
                                    ErrorMessage="厂商不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="CarInfo"></asp:RequiredFieldValidator>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="txtModel" class="col-sm-3 col-form-label">型号</label>
                            <div class="col-sm-9">
                                <asp:TextBox ID="txtModel" runat="server" CssClass="form-control" placeholder="请输入汽车型号"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvModel" runat="server" ControlToValidate="txtModel"
                                    ErrorMessage="型号不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="CarInfo"></asp:RequiredFieldValidator>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="txtYear" class="col-sm-3 col-form-label">年份</label>
                            <div class="col-sm-9">
                                <asp:TextBox ID="txtYear" runat="server" CssClass="form-control" placeholder="请输入汽车年份" TextMode="Number"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvYear" runat="server" ControlToValidate="txtYear"
                                    ErrorMessage="年份不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="CarInfo"></asp:RequiredFieldValidator>
                                <asp:RangeValidator ID="rvYear" runat="server" ControlToValidate="txtYear" Type="Integer"
                                    MinimumValue="1900" MaximumValue="2100" ErrorMessage="年份必须在1900到2100之间"
                                    Display="Dynamic" CssClass="text-danger" ValidationGroup="CarInfo"></asp:RangeValidator>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="txtLicensePlate" class="col-sm-3 col-form-label">车牌号</label>
                            <div class="col-sm-9">
                                <asp:TextBox ID="txtLicensePlate" runat="server" CssClass="form-control" placeholder="请输入车牌号"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvLicensePlate" runat="server" ControlToValidate="txtLicensePlate"
                                    ErrorMessage="车牌号不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="CarInfo"></asp:RequiredFieldValidator>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="txtVIN" class="col-sm-3 col-form-label">车架号(VIN)</label>
                            <div class="col-sm-9">
                                <asp:TextBox ID="txtVIN" runat="server" CssClass="form-control" placeholder="请输入车架号"></asp:TextBox>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="txtColor" class="col-sm-3 col-form-label">颜色</label>
                            <div class="col-sm-9">
                                <asp:TextBox ID="txtColor" runat="server" CssClass="form-control" placeholder="请输入汽车颜色"></asp:TextBox>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="fuCarPhoto" class="col-sm-3 col-form-label">车辆照片</label>
                            <div class="col-sm-9">
                                <asp:FileUpload ID="fuCarPhoto" runat="server" CssClass="form-control-file" />
                                <asp:Image ID="imgCarPhotoPreview" runat="server" Width="120px" Height="80px" Style="margin-top:8px;" />
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-9 offset-sm-3">
                                <asp:Button ID="btnSaveCar" runat="server" Text="保存" CssClass="btn btn-primary" OnClick="btnSaveCar_Click" ValidationGroup="CarInfo" />
                                <asp:Button ID="btnCancel" runat="server" Text="取消" CssClass="btn btn-secondary ml-2" OnClick="btnCancel_Click" CausesValidation="false" />
                            </div>
                        </div>
                    </div>
                </asp:Panel>

                <asp:GridView ID="gvCars" runat="server" AutoGenerateColumns="False"
                    CssClass="table table-striped table-hover" DataKeyNames="CarID"
                    OnRowCommand="gvCars_RowCommand" EmptyDataText="暂无车辆，请点击&quot;添加新车辆&quot;按钮添加">
                    <Columns>
                        <asp:TemplateField HeaderText="照片">
                            <ItemTemplate>
                                <asp:Image ID="imgCarPhoto" runat="server" Width="60px" Height="40px" ImageUrl='<%# string.IsNullOrEmpty(Eval("PhotoUrl") as string) ? "/Images/default-car.png" : Eval("PhotoUrl") %>' />
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:BoundField DataField="Make" HeaderText="厂商" />
                        <asp:BoundField DataField="Model" HeaderText="型号" />
                        <asp:BoundField DataField="Year" HeaderText="年份" />
                        <asp:BoundField DataField="LicensePlate" HeaderText="车牌号" />
                        <asp:BoundField DataField="VIN" HeaderText="车架号" />
                        <asp:BoundField DataField="Color" HeaderText="颜色" />
                        <asp:TemplateField HeaderText="操作">
                            <ItemTemplate>
                                <asp:LinkButton ID="lbtnEdit" runat="server" CssClass="btn btn-sm btn-info mr-1"
                                    CommandName="EditCar" CommandArgument='<%# Eval("CarID") %>'>
                                    <i class="fas fa-edit"></i> 编辑
                                </asp:LinkButton>
                                <asp:LinkButton ID="lbtnDelete" runat="server" CssClass="btn btn-sm btn-danger"
                                    CommandName="DeleteCar" CommandArgument='<%# Eval("CarID") %>'
                                    OnClientClick="return confirm('确定要删除此车辆吗？');">
                                    <i class="fas fa-trash-alt"></i> 删除
                                </asp:LinkButton>
                            </ItemTemplate>
                        </asp:TemplateField>
                    </Columns>
                </asp:GridView>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col">
                <asp:Button ID="btnBackToDashboard" runat="server" Text="返回控制台" CssClass="btn btn-secondary" OnClick="btnBackToDashboard_Click" />
            </div>
        </div>
    </div>
</asp:Content> 