using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.Security;

/// <summary>
/// 用户管理类，处理用户的登录和注册
/// </summary>
public class UserManager
{
    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <returns>成功返回用户ID，失败返回-1</returns>
    public static UserInfo ValidateUser(string username, string password)
    {
        string query = "SELECT UserID, Username, Email, UserType FROM Users WHERE Username = @Username AND Password = @Password AND IsActive = 1";
        SqlParameter[] parameters =
        {
            new SqlParameter("@Username", username),
            new SqlParameter("@Password", password)
        };

        DataTable dt = DatabaseHelper.ExecuteQuery(query, parameters);
        if (dt != null && dt.Rows.Count > 0)
        {
            // 更新最后登录时间
            UpdateLastLoginDate(Convert.ToInt32(dt.Rows[0]["UserID"]));

            // 返回用户信息
            UserInfo user = new UserInfo
            {
                UserID = Convert.ToInt32(dt.Rows[0]["UserID"]),
                Username = dt.Rows[0]["Username"].ToString(),
                Email = dt.Rows[0]["Email"].ToString(),
                UserType = dt.Rows[0]["UserType"].ToString()
            };
            return user;
        }
        return null;
    }

    /// <summary>
    /// 更新用户最后登录时间
    /// </summary>
    private static void UpdateLastLoginDate(int userID)
    {
        string query = "UPDATE Users SET LastLoginDate = GETDATE() WHERE UserID = @UserID";
        SqlParameter parameter = new SqlParameter("@UserID", userID);
        DatabaseHelper.ExecuteNonQuery(query, parameter);
    }

    /// <summary>
    /// 检查用户名是否存在
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>存在返回true，不存在返回false</returns>
    public static bool IsUsernameExists(string username)
    {
        string query = "SELECT COUNT(1) FROM Users WHERE Username = @Username";
        SqlParameter parameter = new SqlParameter("@Username", username);
        int count = Convert.ToInt32(DatabaseHelper.ExecuteScalar(query, parameter));
        return count > 0;
    }

    /// <summary>
    /// 检查邮箱是否存在
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <returns>存在返回true，不存在返回false</returns>
    public static bool IsEmailExists(string email)
    {
        string query = "SELECT COUNT(1) FROM Users WHERE Email = @Email";
        SqlParameter parameter = new SqlParameter("@Email", email);
        int count = Convert.ToInt32(DatabaseHelper.ExecuteScalar(query, parameter));
        return count > 0;
    }

    /// <summary>
    /// 注册新用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="email">邮箱</param>
    /// <param name="phoneNumber">电话号码</param>
    /// <param name="userType">用户类型</param>
    /// <returns>成功返回用户ID，失败返回-1</returns>
    public static int RegisterUser(string username, string password, string email, string phoneNumber, string userType)
    {
        string query = @"INSERT INTO Users (Username, Password, Email, PhoneNumber, UserType, CreatedDate, IsActive) 
                        VALUES (@Username, @Password, @Email, @PhoneNumber, @UserType, GETDATE(), 1);
                        SELECT SCOPE_IDENTITY()";

        SqlParameter[] parameters =
        {
            new SqlParameter("@Username", username),
            new SqlParameter("@Password", password),
            new SqlParameter("@Email", email),
            new SqlParameter("@PhoneNumber", phoneNumber ?? (object)DBNull.Value),
            new SqlParameter("@UserType", userType)
        };

        object result = DatabaseHelper.ExecuteScalar(query, parameters);
        if (result != null && result != DBNull.Value)
        {
            int userId = Convert.ToInt32(result);
            
            // 根据用户类型创建相应的记录
            if (userType == "CarOwner")
            {
                CreateCarOwner(userId, username);
            }
            else if (userType == "RepairShop")
            {
                CreateRepairShop(userId, username);
            }
            
            return userId;
        }
        return -1;
    }

    /// <summary>
    /// 创建车主记录
    /// </summary>
    private static void CreateCarOwner(int userId, string defaultName)
    {
        string query = "INSERT INTO CarOwners (UserID, FullName) VALUES (@UserID, @FullName)";
        SqlParameter[] parameters =
        {
            new SqlParameter("@UserID", userId),
            new SqlParameter("@FullName", defaultName)
        };
        DatabaseHelper.ExecuteNonQuery(query, parameters);
    }

    /// <summary>
    /// 创建维修店记录
    /// </summary>
    private static void CreateRepairShop(int userId, string shopName)
    {
        string query = "INSERT INTO RepairShops (UserID, ShopName, Address) VALUES (@UserID, @ShopName, '请更新地址')";
        SqlParameter[] parameters =
        {
            new SqlParameter("@UserID", userId),
            new SqlParameter("@ShopName", shopName)
        };
        DatabaseHelper.ExecuteNonQuery(query, parameters);
    }
} 