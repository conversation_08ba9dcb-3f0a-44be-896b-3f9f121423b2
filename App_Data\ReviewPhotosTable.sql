-- 创建评价照片表
USE CarRepairServiceDB;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ReviewPhotos')
BEGIN
    CREATE TABLE ReviewPhotos (
        PhotoID INT PRIMARY KEY IDENTITY(1,1),
        ReviewID INT NOT NULL,
        FileName NVARCHAR(255) NOT NULL,
        FilePath NVARCHAR(500) NOT NULL,
        UploadDate DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (ReviewID) REFERENCES Reviews(ReviewID) ON DELETE CASCADE
    );
    PRINT '成功创建评价照片表(ReviewPhotos)';
END
ELSE
BEGIN
    PRINT '评价照片表(ReviewPhotos)已经存在';
END 