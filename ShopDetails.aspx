<%@ Page Title="维修店详情" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeBehind="ShopDetails.aspx.cs" Inherits="ShopDetails" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
    <div class="container">
        <div class="row mb-4">
            <div class="col-md-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="Default.aspx">首页</a></li>
                        <li class="breadcrumb-item"><a href="Search.aspx">搜索维修店</a></li>
                        <li class="breadcrumb-item active">维修店详情</li>
                    </ol>
                </nav>
            </div>
        </div>

        <asp:Panel ID="pnlShopDetails" runat="server">
            <div class="row mb-4">
                <div class="col-md-4">
                    <asp:Image ID="imgShopLogo" runat="server" CssClass="img-fluid" AlternateText="维修店Logo" />
                </div>
                <div class="col-md-8">
                    <h2><asp:Literal ID="litShopName" runat="server"></asp:Literal></h2>
                    <div class="mb-3">
                        <span class="text-warning">
                            <asp:Literal ID="litRatingStars" runat="server"></asp:Literal>
                        </span>
                        <asp:Label ID="lblRating" runat="server" CssClass="ml-2"></asp:Label>
                        <asp:Label ID="lblReviewCount" runat="server" CssClass="text-muted ml-2"></asp:Label>
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-map-marker-alt mr-2 text-primary"></i>
                        <asp:Literal ID="litAddress" runat="server"></asp:Literal>
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-phone mr-2 text-primary"></i>
                        <asp:Literal ID="litPhone" runat="server"></asp:Literal>
                    </div>
                    <div class="mb-3">
                        <i class="fas fa-clock mr-2 text-primary"></i>
                        <asp:Literal ID="litBusinessHours" runat="server"></asp:Literal>
                    </div>
                    <div class="mt-4">
                        <asp:LinkButton ID="lnkMakeAppointment" runat="server" CssClass="btn btn-primary"
                            OnClick="lnkMakeAppointment_Click">
                            <i class="fas fa-calendar-alt mr-2"></i>预约维修
                        </asp:LinkButton>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <ul class="nav nav-tabs mb-3" id="shopDetailsTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="services-tab" data-toggle="tab" href="#services" role="tab">
                                服务项目
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="info-tab" data-toggle="tab" href="#info" role="tab">
                                店铺介绍
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="reviews-tab" data-toggle="tab" href="#reviews" role="tab">
                                用户评价
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content" id="shopDetailsTabContent">
                        <!-- 服务项目 -->
                        <div class="tab-pane fade show active" id="services" role="tabpanel" aria-labelledby="services-tab">
                            <h4 class="mb-3">服务项目</h4>
                            <asp:Panel ID="pnlNoServices" runat="server" CssClass="alert alert-info" Visible="false">
                                暂无服务项目信息
                            </asp:Panel>
                            <asp:Repeater ID="rptServices" runat="server">
                                <ItemTemplate>
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <h5 class="card-title"><%# Eval("ServiceName") %></h5>
                                                    <p class="card-text"><%# Eval("Description") %></p>
                                                    <p class="card-text">
                                                        <small class="text-muted">预计时间: <%# Eval("EstimatedTime") %> 小时</small>
                                                    </p>
                                                </div>
                                                <div class="col-md-4 text-right">
                                                    <h5 class="text-primary">¥<%# Eval("BasePrice", "{0:N2}") %></h5>
                                                    <asp:Button ID="btnSelectService" runat="server" Text="选择此服务" CssClass="btn btn-outline-primary btn-sm"
                                                        CommandName="SelectService" CommandArgument='<%# Eval("ServiceID") %>' OnClick="btnSelectService_Click" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>

                        <!-- 店铺介绍 -->
                        <div class="tab-pane fade" id="info" role="tabpanel" aria-labelledby="info-tab">
                            <h4 class="mb-3">店铺介绍</h4>
                            <div class="card">
                                <div class="card-body">
                                    <asp:Literal ID="litShopDescription" runat="server"></asp:Literal>
                                    <hr />
                                    <h5>店铺资质</h5>
                                    <asp:Literal ID="litQualifications" runat="server"></asp:Literal>
                                </div>
                            </div>
                        </div>

                        <!-- 用户评价 -->
                        <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                            <h4 class="mb-3">用户评价</h4>
                            <asp:UpdatePanel ID="upReviews" runat="server">
                                <ContentTemplate>
                                    <div class="mb-4">
                                        <asp:Panel ID="pnlNoReviews" runat="server" CssClass="alert alert-info" Visible="false">
                                            暂无用户评价
                                        </asp:Panel>
                                        <asp:Repeater ID="rptReviews" runat="server">
                                            <ItemTemplate>
                                                <div class="card mb-3">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <h6 class="mb-0"><%# Eval("Username") %></h6>
                                                            <small class="text-muted"><%# Eval("ReviewDate", "{0:yyyy-MM-dd}") %></small>
                                                        </div>
                                                        <div class="mb-2 text-warning">
                                                            <%# GetStarRating(Convert.ToInt32(Eval("Rating"))) %>
                                                        </div>
                                                        <p class="card-text"><%# Eval("Comments") %></p>
                                                        <small class="text-muted">服务项目: <%# Eval("ServiceName") %></small>
                                                    </div>
                                                </div>
                                            </ItemTemplate>
                                        </asp:Repeater>

                                        <asp:Panel ID="pnlPagination" runat="server" CssClass="mt-3">
                                            <nav aria-label="评价分页">
                                                <ul class="pagination justify-content-center">
                                                    <asp:Repeater ID="rptPagination" runat="server" OnItemCommand="rptPagination_ItemCommand">
                                                        <ItemTemplate>
                                                            <li class="page-item <%# Convert.ToInt32(Eval("PageNumber")) == CurrentPage ? "active" : "" %>">
                                                                <asp:LinkButton ID="lnkPage" runat="server" CssClass="page-link"
                                                                    CommandName="ChangePage" CommandArgument='<%# Eval("PageNumber") %>'
                                                                    Text='<%# Eval("PageNumber") %>' />
                                                            </li>
                                                        </ItemTemplate>
                                                    </asp:Repeater>
                                                </ul>
                                            </nav>
                                        </asp:Panel>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <asp:Panel ID="pnlNotFound" runat="server" Visible="false">
            <div class="alert alert-warning">
                <h4>未找到维修店信息</h4>
                <p>抱歉，您请求的维修店信息不存在或已被删除。</p>
                <a href="Search.aspx" class="btn btn-primary">返回搜索页面</a>
            </div>
        </asp:Panel>
    </div>
</asp:Content> 