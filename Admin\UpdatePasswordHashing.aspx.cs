using System;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;

public partial class Admin_UpdatePasswordHashing : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查是否为管理员
        if (!User.Identity.IsAuthenticated || Session["UserType"] == null || Session["UserType"].ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }
    }

    protected void btnUpdatePasswords_Click(object sender, EventArgs e)
    {
        string defaultPassword = txtDefaultPassword.Text.Trim();
        string adminPassword = txtAdminPassword.Text.Trim();

        // 验证管理员密码
        UserInfo adminUser = UserManager.ValidateUser(User.Identity.Name, adminPassword);
        if (adminUser == null || adminUser.UserType != "Admin")
        {
            lblMessage.Text = "管理员密码不正确，无法执行此操作。";
            lblMessage.CssClass = "text-danger";
            return;
        }

        try
        {
            // 获取所有用户
            string query = "SELECT UserID, Username FROM Users";
            DataTable users = DatabaseHelper.ExecuteQuery(query);

            if (users != null && users.Rows.Count > 0)
            {
                StringBuilder successLog = new StringBuilder();
                StringBuilder errorLog = new StringBuilder();
                int updatedCount = 0;

                foreach (DataRow user in users.Rows)
                {
                    int userId = Convert.ToInt32(user["UserID"]);
                    string username = user["Username"].ToString();

                    try
                    {
                        // 为每个用户重新生成密码哈希
                        string hashedPassword = PasswordHasher.HashPassword(defaultPassword);
                        
                        // 更新数据库
                        string updateQuery = "UPDATE Users SET Password = @Password WHERE UserID = @UserID";
                        SqlParameter[] parameters = {
                            new SqlParameter("@Password", hashedPassword),
                            new SqlParameter("@UserID", userId)
                        };
                        
                        int result = DatabaseHelper.ExecuteNonQuery(updateQuery, parameters);
                        if (result > 0)
                        {
                            updatedCount++;
                            successLog.AppendLine($"用户 {username} (ID: {userId}) 密码已更新");
                        }
                        else
                        {
                            errorLog.AppendLine($"用户 {username} (ID: {userId}) 密码更新失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        errorLog.AppendLine($"用户 {username} (ID: {userId}) 密码更新出错: {ex.Message}");
                    }
                }

                // 显示结果
                lblMessage.Text = $"密码更新操作完成。成功更新了 {updatedCount} 个用户的密码。<br/>";
                
                if (errorLog.Length > 0)
                {
                    lblMessage.Text += "<br/>以下用户更新失败:<br/>";
                    lblMessage.Text += errorLog.ToString().Replace(Environment.NewLine, "<br/>");
                }
                
                lblMessage.CssClass = "alert alert-success";
            }
            else
            {
                lblMessage.Text = "未找到任何用户。";
                lblMessage.CssClass = "alert alert-warning";
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = $"更新密码时发生错误: {ex.Message}";
            lblMessage.CssClass = "alert alert-danger";
        }
    }
} 