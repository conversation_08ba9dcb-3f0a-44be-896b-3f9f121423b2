using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Globalization;

public partial class CarOwner_PaymentSimulation : System.Web.UI.Page
{
    private int userID;
    private int ownerID;
    private int selectedRecordID;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取当前用户信息
        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "CarOwner")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        
        // 获取车主ID
        ownerID = CarManager.GetOwnerIDByUserID(userID);
        if (ownerID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (IsPostBack)
        {
            // 恢复选中的记录ID
            if (ViewState["SelectedRecordID"] != null)
            {
                selectedRecordID = Convert.ToInt32(ViewState["SelectedRecordID"]);
            }
        }
        else
        {
            // 初次加载页面
            LoadUnverifiedServiceRecords();
            
            // 检查URL参数
            if (!string.IsNullOrEmpty(Request.QueryString["recordID"]))
            {
                int recordID;
                if (int.TryParse(Request.QueryString["recordID"], out recordID))
                {
                    ShowServiceDetails(recordID);
                }
            }
            else if (!string.IsNullOrEmpty(Request.QueryString["appointmentID"]))
            {
                int appointmentID;
                if (int.TryParse(Request.QueryString["appointmentID"], out appointmentID))
                {
                    // 查找与此预约相关的服务记录
                    DataTable serviceRecords = AppointmentManager.GetServiceRecordsByAppointmentID(appointmentID);
                    if (serviceRecords != null && serviceRecords.Rows.Count > 0)
                    {
                        // 显示第一个匹配的服务记录
                        int recordID = Convert.ToInt32(serviceRecords.Rows[0]["RecordID"]);
                        ShowServiceDetails(recordID);
                    }
                    else
                    {
                        ShowMessage("未找到与此预约相关的服务记录，可能是维修尚未完成。", "warning");
                    }
                }
            }
        }
    }

    #region 数据加载方法

    /// <summary>
    /// 加载未验收的维修服务记录
    /// </summary>
    private void LoadUnverifiedServiceRecords()
    {
        // 更新标签页样式
        SetActiveTab("unverified");

        // 获取未验收的维修记录
        DataTable unverifiedRecords = AppointmentManager.GetUnverifiedServiceRecordsByOwnerID(ownerID);
        gvUnverified.DataSource = unverifiedRecords;
        gvUnverified.DataBind();

        // 显示相应的视图
        mvContent.ActiveViewIndex = 0;
    }

    /// <summary>
    /// 加载未支付的维修服务记录
    /// </summary>
    private void LoadUnpaidServiceRecords()
    {
        // 更新标签页样式
        SetActiveTab("unpaid");

        // 获取未支付的维修记录
        DataTable unpaidRecords = AppointmentManager.GetUnpaidServiceRecordsByOwnerID(ownerID);
        gvUnpaid.DataSource = unpaidRecords;
        gvUnpaid.DataBind();

        // 显示相应的视图
        mvContent.ActiveViewIndex = 1;
    }

    /// <summary>
    /// 加载支付历史记录
    /// </summary>
    private void LoadPaymentHistory()
    {
        // 更新标签页样式
        SetActiveTab("history");

        // 获取支付历史
        DataTable paymentHistory = AppointmentManager.GetPaymentRecordsByOwnerID(ownerID);
        gvPaymentHistory.DataSource = paymentHistory;
        gvPaymentHistory.DataBind();

        // 显示相应的视图
        mvContent.ActiveViewIndex = 2;
    }

    /// <summary>
    /// 设置当前活动的标签
    /// </summary>
    private void SetActiveTab(string activeTab)
    {
        // 重置所有标签样式
        lbtnUnverified.CssClass = "nav-link";
        lbtnUnpaid.CssClass = "nav-link";
        lbtnPaymentHistory.CssClass = "nav-link";

        // 设置活动标签样式
        switch (activeTab.ToLower())
        {
            case "unverified":
                lbtnUnverified.CssClass = "nav-link active";
                break;
            case "unpaid":
                lbtnUnpaid.CssClass = "nav-link active";
                break;
            case "history":
                lbtnPaymentHistory.CssClass = "nav-link active";
                break;
        }
    }

    #endregion

    #region 标签页切换事件

    protected void lbtnUnverified_Click(object sender, EventArgs e)
    {
        // 切换到未验收视图
        pnlMainContent.Visible = true;
        pnlServiceDetails.Visible = false;
        pnlPaymentConfirmation.Visible = false;
        
        LoadUnverifiedServiceRecords();
    }

    protected void lbtnUnpaid_Click(object sender, EventArgs e)
    {
        // 切换到未支付视图
        pnlMainContent.Visible = true;
        pnlServiceDetails.Visible = false;
        pnlPaymentConfirmation.Visible = false;
        
        LoadUnpaidServiceRecords();
    }

    protected void lbtnPaymentHistory_Click(object sender, EventArgs e)
    {
        // 切换到支付历史视图
        pnlMainContent.Visible = true;
        pnlServiceDetails.Visible = false;
        pnlPaymentConfirmation.Visible = false;
        
        LoadPaymentHistory();
    }

    #endregion

    #region 行命令事件

    /// <summary>
    /// 未验收GridView的命令事件
    /// </summary>
    protected void gvUnverified_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        int recordID = Convert.ToInt32(e.CommandArgument);

        if (e.CommandName == "VerifyService")
        {
            // 验收服务
            VerifyService(recordID);
        }
        else if (e.CommandName == "ViewServiceDetails")
        {
            // 查看服务详情
            ShowServiceDetails(recordID);
        }
    }

    /// <summary>
    /// 未支付GridView的命令事件
    /// </summary>
    protected void gvUnpaid_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        int recordID = Convert.ToInt32(e.CommandArgument);

        if (e.CommandName == "PayService")
        {
            // 支付服务
            ShowPaymentOptions(recordID);
        }
        else if (e.CommandName == "ViewServiceDetails")
        {
            // 查看服务详情
            ShowServiceDetails(recordID);
        }
    }

    /// <summary>
    /// 支付历史GridView的命令事件
    /// </summary>
    protected void gvPaymentHistory_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "ViewServiceDetails")
        {
            int recordID = Convert.ToInt32(e.CommandArgument);
            ShowServiceDetails(recordID);
        }
    }

    #endregion

    #region 服务详情和验收支付方法

    /// <summary>
    /// 显示服务详情
    /// </summary>
    private void ShowServiceDetails(int recordID)
    {
        DataTable serviceRecord = AppointmentManager.GetServiceRecordByID(recordID);
        if (serviceRecord != null && serviceRecord.Rows.Count > 0)
        {
            DataRow record = serviceRecord.Rows[0];

            // 保存当前选中的记录ID
            selectedRecordID = recordID;
            ViewState["SelectedRecordID"] = recordID;

            // 填充服务信息
            lblServiceName.Text = record["ServiceName"].ToString();
            lblCompletedDate.Text = Convert.ToDateTime(record["CompletedDate"]).ToString("yyyy-MM-dd HH:mm");
            lblDiagnosisDetails.Text = record["DiagnosisDetails"] != DBNull.Value ? record["DiagnosisDetails"].ToString() : "无";
            lblPartsReplaced.Text = record["PartsReplaced"] != DBNull.Value ? record["PartsReplaced"].ToString() : "无";
            lblTechnicianName.Text = record["TechnicianName"].ToString();

            // 填充费用信息
            decimal laborCost = Convert.ToDecimal(record["LaborCost"]);
            decimal partsCost = Convert.ToDecimal(record["PartsCost"]);
            decimal totalCost = Convert.ToDecimal(record["TotalCost"]);

            lblLaborCost.Text = laborCost.ToString("C", CultureInfo.GetCultureInfo("zh-CN"));
            lblPartsCost.Text = partsCost.ToString("C", CultureInfo.GetCultureInfo("zh-CN"));
            lblTotalCost.Text = totalCost.ToString("C", CultureInfo.GetCultureInfo("zh-CN"));

            bool isVerified = Convert.ToBoolean(record["IsVerified"]);
            bool isPaid = Convert.ToBoolean(record["IsPaid"]);

            lblIsVerified.Text = isVerified ? "<span class='badge badge-success'>已验收</span>" : "<span class='badge badge-warning'>未验收</span>";
            lblIsPaid.Text = isPaid ? "<span class='badge badge-success'>已支付</span>" : "<span class='badge badge-danger'>未支付</span>";

            // 填充车辆信息
            lblCarMakeModel.Text = record["Make"].ToString() + " " + record["Model"].ToString();
            lblLicensePlate.Text = record["LicensePlate"].ToString();

            // 填充维修店信息
            lblShopName.Text = record["ShopName"].ToString();
            lblShopAddress.Text = record["Address"].ToString();
            
            // 检查是否存在PhoneNumber列
            if (record.Table.Columns.Contains("PhoneNumber") && record["PhoneNumber"] != DBNull.Value)
            {
                lblShopPhone.Text = record["PhoneNumber"].ToString();
            }
            else
            {
                lblShopPhone.Text = "未提供";
            }

            // 决定显示哪个操作面板
            pnlVerifyAction.Visible = !isVerified;
            pnlPayAction.Visible = isVerified && !isPaid;

            // 显示服务详情面板
            pnlMainContent.Visible = false;
            pnlServiceDetails.Visible = true;
            pnlPaymentConfirmation.Visible = false;
        }
        else
        {
            ShowMessage("获取服务详情失败，请刷新页面重试。", "danger");
        }
    }

    /// <summary>
    /// 显示支付选项
    /// </summary>
    private void ShowPaymentOptions(int recordID)
    {
        // 检查记录是否已验收
        if (!AppointmentManager.IsServiceRecordVerified(recordID))
        {
            ShowMessage("您需要先验收服务才能支付。", "warning");
            LoadUnverifiedServiceRecords();
            return;
        }

        // 检查是否已经支付
        if (AppointmentManager.IsServiceRecordPaid(recordID))
        {
            ShowMessage("此服务已经完成支付。", "info");
            LoadUnpaidServiceRecords();
            return;
        }

        // 显示服务详情并启用支付选项
        ShowServiceDetails(recordID);
        pnlVerifyAction.Visible = false;
        pnlPayAction.Visible = true;
    }

    /// <summary>
    /// 验收服务
    /// </summary>
    private void VerifyService(int recordID)
    {
        // 验证记录是否存在且属于当前车主
        DataTable serviceRecord = AppointmentManager.GetServiceRecordByID(recordID);
        if (serviceRecord == null || serviceRecord.Rows.Count == 0)
        {
            ShowMessage("服务记录不存在。", "danger");
            return;
        }

        int recordOwnerID = Convert.ToInt32(serviceRecord.Rows[0]["OwnerID"]);
        if (recordOwnerID != ownerID)
        {
            ShowMessage("您无权验收此服务。", "danger");
            return;
        }

        // 检查是否已验收
        if (Convert.ToBoolean(serviceRecord.Rows[0]["IsVerified"]))
        {
            ShowMessage("此服务已经验收。", "info");
            LoadUnverifiedServiceRecords();
            return;
        }

        // 执行验收
        bool success = AppointmentManager.VerifyServiceRecord(recordID);
        if (success)
        {
            ShowMessage("服务验收成功，请继续完成支付。", "success");
            LoadUnpaidServiceRecords();
        }
        else
        {
            ShowMessage("服务验收失败，请稍后重试。", "danger");
            LoadUnverifiedServiceRecords();
        }
    }

    /// <summary>
    /// 支付服务
    /// </summary>
    private decimal ProcessServicePayment(int recordID, string paymentMethod)
    {
        // 验证记录是否存在且属于当前车主
        DataTable serviceRecord = AppointmentManager.GetServiceRecordByID(recordID);
        if (serviceRecord == null || serviceRecord.Rows.Count == 0)
        {
            ShowMessage("服务记录不存在。", "danger");
            return -1;
        }

        int recordOwnerID = Convert.ToInt32(serviceRecord.Rows[0]["OwnerID"]);
        if (recordOwnerID != ownerID)
        {
            ShowMessage("您无权为此服务支付费用。", "danger");
            return -1;
        }

        // 检查是否已支付
        if (Convert.ToBoolean(serviceRecord.Rows[0]["IsPaid"]))
        {
            ShowMessage("此服务已经支付。", "info");
            return -1;
        }

        // 检查是否已验收
        if (!Convert.ToBoolean(serviceRecord.Rows[0]["IsVerified"]))
        {
            ShowMessage("请先验收服务，然后再支付。", "warning");
            return -1;
        }

        // 获取支付金额和相关信息
        int appointmentID = Convert.ToInt32(serviceRecord.Rows[0]["AppointmentID"]);
        decimal amount = Convert.ToDecimal(serviceRecord.Rows[0]["TotalCost"]);
        
        // 生成交易号（模拟）
        string transactionNumber = "TX" + DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(1000, 9999).ToString();
        
        // 执行支付
        int paymentID = AppointmentManager.ProcessPayment(
            recordID,
            appointmentID,
            paymentMethod,
            amount,
            transactionNumber,
            $"车主ID {ownerID} 的付款");

        if (paymentID > 0)
        {
            return amount;
        }
        else
        {
            return -1;
        }
    }

    #endregion

    #region 按钮事件处理程序

    /// <summary>
    /// 验收确认按钮点击事件
    /// </summary>
    protected void btnVerifyService_Click(object sender, EventArgs e)
    {
        VerifyService(selectedRecordID);
    }

    /// <summary>
    /// 支付按钮点击事件
    /// </summary>
    protected void btnPayNow_Click(object sender, EventArgs e)
    {
        // 获取支付方式
        string paymentMethod = rblPaymentMethod.SelectedValue;
        
        // 处理支付
        decimal amount = ProcessServicePayment(selectedRecordID, paymentMethod);
        
        if (amount > 0)
        {
            // 生成模拟交易号
            string transactionNumber = "TX" + DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(1000, 9999).ToString();
            lblTransactionNumber.Text = transactionNumber;
            
            // 显示支付成功界面
            pnlMainContent.Visible = false;
            pnlServiceDetails.Visible = false;
            pnlPaymentConfirmation.Visible = true;
        }
        else
        {
            // 支付失败，返回到未支付列表
            LoadUnpaidServiceRecords();
        }
    }

    /// <summary>
    /// 返回列表按钮点击事件
    /// </summary>
    protected void btnBackToList_Click(object sender, EventArgs e)
    {
        // 决定返回哪个列表（基于当前激活的标签）
        string activeTab = "unverified";
        if (lbtnUnpaid.CssClass.Contains("active"))
        {
            activeTab = "unpaid";
        }
        else if (lbtnPaymentHistory.CssClass.Contains("active"))
        {
            activeTab = "history";
        }
        
        pnlMainContent.Visible = true;
        pnlServiceDetails.Visible = false;
        pnlPaymentConfirmation.Visible = false;
        
        switch (activeTab)
        {
            case "unpaid":
                LoadUnpaidServiceRecords();
                break;
            case "history":
                LoadPaymentHistory();
                break;
            default:
                LoadUnverifiedServiceRecords();
                break;
        }
    }

    /// <summary>
    /// 支付完成按钮点击事件
    /// </summary>
    protected void btnDonePayment_Click(object sender, EventArgs e)
    {
        // 重置界面状态并加载支付历史
        pnlMainContent.Visible = true;
        pnlServiceDetails.Visible = false;
        pnlPaymentConfirmation.Visible = false;
        
        LoadPaymentHistory();
        
        // 显示成功消息
        ShowMessage("支付已成功处理。", "success");
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 显示消息
    /// </summary>
    private void ShowMessage(string message, string type)
    {
        lblMessage.Text = message;
        lblMessage.CssClass = $"alert alert-{type}";
        lblMessage.Visible = true;
    }

    #endregion
} 