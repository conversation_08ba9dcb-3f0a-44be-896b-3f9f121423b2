using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Search : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            // 获取URL中的搜索参数
            string searchQuery = Request.QueryString["q"];
            if (!string.IsNullOrEmpty(searchQuery))
            {
                txtSearch.Text = searchQuery;
                SearchShops(searchQuery);
            }
            else
            {
                // 默认加载所有维修店
                LoadAllShops();
            }
        }
    }

    /// <summary>
    /// 加载所有维修店
    /// </summary>
    private void LoadAllShops()
    {
        int filter = 0;
        if (int.TryParse(ddlFilter.SelectedValue, out int f)) filter = f;
        var dt = ShopManager.GetAllShopsForFrontend(filter);
        if (dt != null && dt.Rows.Count > 0)
        {
            rptShops.DataSource = dt;
            rptShops.DataBind();
            pnlNoResults.Visible = false;
        }
        else
        {
            rptShops.DataSource = null;
            rptShops.DataBind();
            pnlNoResults.Visible = true;
        }
    }

    /// <summary>
    /// 根据搜索词查找维修店
    /// </summary>
    private void SearchShops(string searchQuery)
    {
        int filter = 0;
        if (int.TryParse(ddlFilter.SelectedValue, out int f)) filter = f;
        var dt = ShopManager.SearchShopsForFrontend(searchQuery, filter);
        if (dt != null && dt.Rows.Count > 0)
        {
            rptShops.DataSource = dt;
            rptShops.DataBind();
            pnlNoResults.Visible = false;
        }
        else
        {
            rptShops.DataSource = null;
            rptShops.DataBind();
            pnlNoResults.Visible = true;
        }
    }

    /// <summary>
    /// 获取星级评分的HTML表示
    /// </summary>
    protected string GetRatingStars(double rating)
    {
        // 将评分转换为星星图标
        string starsHtml = "";
        int fullStars = (int)Math.Floor(rating);
        bool hasHalfStar = (rating - fullStars) >= 0.5;

        // 添加实心星星
        for (int i = 0; i < fullStars; i++)
        {
            starsHtml += "<i class=\"fas fa-star\"></i>";
        }

        // 添加半星
        if (hasHalfStar)
        {
            starsHtml += "<i class=\"fas fa-star-half-alt\"></i>";
            fullStars++;
        }

        // 添加空心星星
        for (int i = fullStars; i < 5; i++)
        {
            starsHtml += "<i class=\"far fa-star\"></i>";
        }

        return starsHtml;
    }

    /// <summary>
    /// 搜索按钮点击事件
    /// </summary>
    protected void btnSearch_Click(object sender, EventArgs e)
    {
        string searchQuery = txtSearch.Text.Trim();
        if (!string.IsNullOrEmpty(searchQuery))
        {
            SearchShops(searchQuery);
        }
        else
        {
            LoadAllShops();
        }
    }

    /// <summary>
    /// 过滤选项变更事件
    /// </summary>
    protected void ddlFilter_SelectedIndexChanged(object sender, EventArgs e)
    {
        // 根据选择的过滤选项重新加载数据
        string searchQuery = txtSearch.Text.Trim();
        if (!string.IsNullOrEmpty(searchQuery))
        {
            SearchShops(searchQuery);
        }
        else
        {
            LoadAllShops();
        }
    }

    /// <summary>
    /// Repeater的项命令事件
    /// </summary>
    protected void rptShops_ItemCommand(object source, RepeaterCommandEventArgs e)
    {
        if (e.CommandName == "ViewShop")
        {
            int shopID = Convert.ToInt32(e.CommandArgument);
            Response.Redirect("~/ShopDetails.aspx?id=" + shopID);
        }
    }
} 