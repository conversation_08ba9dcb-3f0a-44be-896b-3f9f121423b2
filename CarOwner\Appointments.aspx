<%@ Page Title="预约管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="CarOwner_Appointments" Codebehind="Appointments.aspx.cs" EnableViewState="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script type="text/javascript">
        // 当页面加载完成时
        $(document).ready(function () {
            // 获取当前时间
            setMinDateTime();
            
            // 当预约时间控件获得焦点时重新设置最小值
            $("[id$='txtAppointmentDate']").on('focus', function () {
                setMinDateTime();
            });
        });
        
        // 设置预约时间的最小值为当前时间
        function setMinDateTime() {
            var now = new Date();
            var year = now.getFullYear();
            var month = (now.getMonth() + 1).toString().padStart(2, '0');
            var day = now.getDate().toString().padStart(2, '0');
            var hours = now.getHours().toString().padStart(2, '0');
            var minutes = now.getMinutes().toString().padStart(2, '0');
            
            var minDateTime = year + '-' + month + '-' + day + 'T' + hours + ':' + minutes;
            $("[id$='txtAppointmentDate']").attr('min', minDateTime);
        }
        
        // 表单提交前验证
        function validateAppointmentDate() {
            var appointmentDateInput = $("[id$='txtAppointmentDate']");
            var appointmentDateStr = appointmentDateInput.val();

            if (!appointmentDateStr) {
                return true; // 让服务器端验证处理空值
            }

            var appointmentDate = new Date(appointmentDateStr);
            var now = new Date();

            if (appointmentDate <= now) {
                alert("预约时间必须是将来的时间。");
                return false;
            }

            return true;
        }

        // 确认取消预约
        function confirmCancelAppointment() {
            try {
                console.log("取消预约确认对话框被调用");

                // 检查隐藏字段中的预约ID
                var hiddenField = $("[id$='hdnCurrentAppointmentID']");
                var appointmentID = hiddenField.length > 0 ? hiddenField.val() : "未找到";
                console.log("客户端获取到的预约ID:", appointmentID);

                if (!appointmentID || appointmentID === "" || appointmentID === "未找到") {
                    alert("错误：无法获取预约ID，请刷新页面后重试。");
                    return false;
                }

                var result = confirm('确定要取消此预约吗？\n\n预约ID: ' + appointmentID + '\n取消后将无法恢复，请确认您的操作。');
                console.log("用户选择结果:", result);

                if (result) {
                    // 显示加载提示
                    var cancelBtn = $("[id$='btnCancelAppointment']");
                    if (cancelBtn.length > 0) {
                        cancelBtn.prop('disabled', true);
                        cancelBtn.text('正在取消...');
                        console.log("已禁用取消按钮并显示加载文本");
                    }
                }

                return result;
            } catch (ex) {
                console.error("确认对话框出错:", ex);
                alert("发生错误：" + ex.message);
                return false;
            }
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <!-- 隐藏字段存储当前预约ID -->
    <asp:HiddenField ID="hdnCurrentAppointmentID" runat="server" />

    <div class="container">
        <div class="row mb-3">
            <div class="col-md-8">
                <h2><i class="fas fa-calendar-alt"></i> 预约管理</h2>
            </div>
            <div class="col-md-4 text-right">
                <asp:Button ID="btnNewAppointment" runat="server" Text="创建新预约" CssClass="btn btn-primary" OnClick="btnNewAppointment_Click" />
                <a href="ServiceSearch.aspx" class="btn btn-outline-success ml-2">
                    <i class="fas fa-search"></i> 智能筛选服务
                </a>
            </div>
        </div>

        <!-- 消息提示区域 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <asp:Label ID="lblMessage" runat="server" CssClass="alert" Visible="false"></asp:Label>
            </div>
        </div>

        <asp:Panel ID="pnlAppointments" runat="server">
            <div class="row">
                <div class="col">
                    <ul class="nav nav-tabs mb-4">
                        <li class="nav-item">
                            <asp:LinkButton ID="lbtnActiveAppointments" runat="server" CssClass="nav-link active" OnClick="lbtnActiveAppointments_Click">未完成预约</asp:LinkButton>
                        </li>
                        <li class="nav-item">
                            <asp:LinkButton ID="lbtnAllAppointments" runat="server" CssClass="nav-link" OnClick="lbtnAllAppointments_Click">所有预约</asp:LinkButton>
                        </li>
                    </ul>

                    <asp:GridView ID="gvAppointments" runat="server" AutoGenerateColumns="False"
                        CssClass="table table-striped table-hover mb-5" DataKeyNames="AppointmentID"
                        OnRowCommand="gvAppointments_RowCommand" EmptyDataText="暂无预约记录">
                        <Columns>
                            <asp:BoundField DataField="AppointmentDate" HeaderText="预约时间" DataFormatString="{0:yyyy-MM-dd HH:mm}" />
                            <asp:BoundField DataField="CarInfo" HeaderText="车辆" />
                            <asp:BoundField DataField="ShopName" HeaderText="维修店" />
                            <asp:BoundField DataField="ServiceName" HeaderText="服务项目" />
                            <asp:TemplateField HeaderText="状态">
                                <ItemTemplate>
                                    <asp:Label ID="lblStatus" runat="server" Text='<%# GetStatusText(Eval("Status").ToString()) %>'
                                        CssClass='<%# GetStatusCssClass(Eval("Status").ToString()) %>'></asp:Label>
                                </ItemTemplate>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="操作">
                                <ItemTemplate>
                                    <asp:LinkButton ID="lbtnView" runat="server" CssClass="btn btn-sm btn-info mr-1"
                                        CommandName="ViewAppointment" CommandArgument='<%# Eval("AppointmentID") %>' ToolTip="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </asp:LinkButton>
                                    <asp:LinkButton ID="lbtnCancel" runat="server" CssClass="btn btn-sm btn-danger mr-1"
                                        CommandName="CancelAppointment" CommandArgument='<%# Eval("AppointmentID") %>'
                                        Visible='<%# CanCancelAppointment(Eval("Status").ToString()) %>'
                                        OnClientClick="return confirm('确定要取消此预约吗？');" ToolTip="取消预约">
                                        <i class="fas fa-times"></i>
                                    </asp:LinkButton>
                                    <asp:LinkButton ID="lbtnPayment" runat="server" CssClass="btn btn-sm btn-success"
                                        CommandName="GoToPayment" CommandArgument='<%# Eval("AppointmentID") %>'
                                        Visible='<%# Eval("Status").ToString() == "Completed" %>'
                                        ToolTip="前往验收支付">
                                        <i class="fas fa-credit-card"></i>
                                    </asp:LinkButton>
                                </ItemTemplate>
                            </asp:TemplateField>
                        </Columns>
                    </asp:GridView>
                </div>
            </div>
        </asp:Panel>

        <asp:Panel ID="pnlNewAppointment" runat="server" Visible="false">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">创建新预约</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-lightbulb"></i> 提示：您可以使用我们的<a href="ServiceSearch.aspx" class="alert-link">智能筛选功能</a>，根据服务类型、价格范围、地址和评分等多条件筛选服务。
                    </div>
                    <div class="form-group row">
                        <label for="ddlCar" class="col-sm-3 col-form-label">选择车辆</label>
                        <div class="col-sm-9">
                            <asp:DropDownList ID="ddlCar" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlCar_SelectedIndexChanged"></asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvCar" runat="server" ControlToValidate="ddlCar"
                                InitialValue="0" ErrorMessage="请选择车辆" Display="Dynamic"
                                CssClass="text-danger" ValidationGroup="NewAppointment"></asp:RequiredFieldValidator>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="ddlShop" class="col-sm-3 col-form-label">选择维修店</label>
                        <div class="col-sm-9">
                            <asp:DropDownList ID="ddlShop" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlShop_SelectedIndexChanged"></asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvShop" runat="server" ControlToValidate="ddlShop"
                                InitialValue="0" ErrorMessage="请选择维修店" Display="Dynamic"
                                CssClass="text-danger" ValidationGroup="NewAppointment"></asp:RequiredFieldValidator>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="ddlService" class="col-sm-3 col-form-label">选择服务项目</label>
                        <div class="col-sm-9">
                            <asp:DropDownList ID="ddlService" runat="server" CssClass="form-control"></asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvService" runat="server" ControlToValidate="ddlService"
                                InitialValue="0" ErrorMessage="请选择服务项目" Display="Dynamic"
                                CssClass="text-danger" ValidationGroup="NewAppointment"></asp:RequiredFieldValidator>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="txtAppointmentDate" class="col-sm-3 col-form-label">预约时间</label>
                        <div class="col-sm-9">
                            <asp:TextBox ID="txtAppointmentDate" runat="server" CssClass="form-control" TextMode="DateTimeLocal" min='<%# DateTime.Now.ToString("yyyy-MM-ddTHH:mm") %>'></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvAppointmentDate" runat="server" ControlToValidate="txtAppointmentDate"
                                ErrorMessage="请选择预约时间" Display="Dynamic"
                                CssClass="text-danger" ValidationGroup="NewAppointment"></asp:RequiredFieldValidator>
                            <asp:CustomValidator ID="cvAppointmentDate" runat="server" ControlToValidate="txtAppointmentDate"
                                ErrorMessage="预约时间必须是将来的时间" Display="Dynamic"
                                CssClass="text-danger" ValidationGroup="NewAppointment"
                                OnServerValidate="cvAppointmentDate_ServerValidate"></asp:CustomValidator>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="txtDescription" class="col-sm-3 col-form-label">问题描述</label>
                        <div class="col-sm-9">
                            <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" placeholder="请描述您车辆的问题"></asp:TextBox>
                        </div>
                    </div>

                    <div class="form-group row">
                        <div class="col-sm-9 offset-sm-3">
                            <asp:Button ID="btnSubmitAppointment" runat="server" Text="提交预约" CssClass="btn btn-primary" ValidationGroup="NewAppointment" OnClick="btnSubmitAppointment_Click" OnClientClick="return validateAppointmentDate();" />
                            <asp:Button ID="btnCancelNewAppointment" runat="server" Text="取消" CssClass="btn btn-secondary ml-2" OnClick="btnCancelNewAppointment_Click" CausesValidation="false" />
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <asp:Panel ID="pnlAppointmentDetails" runat="server" Visible="false">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">预约详情</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>预约信息</h6>
                            <dl class="row">
                                <dt class="col-sm-4">预约时间：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblAppointmentDate" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">状态：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblStatus" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">问题描述：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblDescription" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6>车辆信息</h6>
                            <dl class="row">
                                <dt class="col-sm-4">厂商型号：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblCarMakeModel" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">车牌号：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblLicensePlate" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <hr />

                    <div class="row">
                        <div class="col-md-6">
                            <h6>维修店信息</h6>
                            <dl class="row">
                                <dt class="col-sm-4">维修店名称：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblShopName" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">地址：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblShopAddress" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6>服务信息</h6>
                            <dl class="row">
                                <dt class="col-sm-4">服务项目：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblServiceName" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">预计时间：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblEstimatedTime" runat="server"></asp:Label>
                                </dd>

                                <dt class="col-sm-4">基本价格：</dt>
                                <dd class="col-sm-8">
                                    <asp:Label ID="lblBasePrice" runat="server"></asp:Label>
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <div class="mt-4">
                        <asp:Button ID="btnBackToList" runat="server" Text="返回列表" CssClass="btn btn-secondary" OnClick="btnBackToList_Click" />
                        <asp:Button ID="btnCancelAppointment" runat="server" Text="取消预约" CssClass="btn btn-danger ml-2"
                            OnClick="btnCancelAppointment_Click" OnClientClick="return confirmCancelAppointment();" UseSubmitBehavior="true"
                            data-loading-text="正在取消..." />
                    </div>
                </div>
            </div>
        </asp:Panel>

        <div class="row mt-3 mb-5 pb-4">
            <div class="col">
                <asp:Button ID="btnBackToDashboard" runat="server" Text="返回控制台" CssClass="btn btn-secondary" OnClick="btnBackToDashboard_Click" />
            </div>
        </div>
    </div>
</asp:Content> 