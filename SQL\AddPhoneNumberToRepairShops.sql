USE CarRepairServiceDB;

-- 添加电话号码列到RepairShops表
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairShops]') AND name = 'PhoneNumber')
BEGIN
    ALTER TABLE [dbo].[RepairShops]
    ADD PhoneNumber NVARCHAR(50) NULL;
    PRINT '成功添加PhoneNumber列到RepairShops表';
END
ELSE
BEGIN
    PRINT 'PhoneNumber列已存在于RepairShops表';
END

-- 确认列已添加
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'RepairShops'; 