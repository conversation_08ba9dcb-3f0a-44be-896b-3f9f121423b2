using System;
using System.Data;
using System.Web.UI.WebControls;
using System.Collections.Generic;
using System.Linq;

public partial class ShopDetails : System.Web.UI.Page
{
    protected int CurrentPage = 1;
    private const int PageSize = 5;
    private int _shopID = 0;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 获取URL中的维修店ID
        if (!int.TryParse(Request.QueryString["id"], out _shopID) || _shopID <= 0)
        {
            // 无效的维修店ID，显示错误页面
            pnlShopDetails.Visible = false;
            pnlNotFound.Visible = true;
            return;
        }

        if (!IsPostBack)
        {
            LoadShopDetails();
            LoadShopServices();
            LoadShopReviews();
        }
    }

    /// <summary>
    /// 加载维修店基本信息
    /// </summary>
    private void LoadShopDetails()
    {
        DataTable dt = ShopManager.GetShopDetails(_shopID);
        
        if (dt != null && dt.Rows.Count > 0)
        {
            DataRow row = dt.Rows[0];
            
            // 设置维修店基本信息
            litShopName.Text = row["ShopName"].ToString();
            
            // 设置Logo图片
            string logoUrl = row["LogoUrl"].ToString();
            if (string.IsNullOrEmpty(logoUrl))
            {
                imgShopLogo.ImageUrl = "~/Images/default_shop_logo.jpg";
            }
            else
            {
                imgShopLogo.ImageUrl = logoUrl;
            }
            
            // 评分信息
            double rating = Convert.ToDouble(row["Rating"]);
            int reviewCount = Convert.ToInt32(row["ReviewCount"]);
            
            litRatingStars.Text = GetStarRating(rating);
            lblRating.Text = rating.ToString("F1") + "/5.0";
            lblReviewCount.Text = $"({reviewCount}条评价)";
            
            // 地址和联系信息
            litAddress.Text = row["Address"].ToString();
            litPhone.Text = row["Phone"].ToString();
            litBusinessHours.Text = row["BusinessHours"].ToString();
            
            // 店铺介绍和资质
            litShopDescription.Text = row["Description"].ToString();
            litQualifications.Text = row["Qualifications"].ToString();
            
            // 检查用户是否登录以显示预约按钮
            lnkMakeAppointment.Visible = User.Identity.IsAuthenticated;
            
            pnlShopDetails.Visible = true;
            pnlNotFound.Visible = false;
        }
        else
        {
            // 未找到维修店信息
            pnlShopDetails.Visible = false;
            pnlNotFound.Visible = true;
        }
    }

    /// <summary>
    /// 加载维修店服务项目
    /// </summary>
    private void LoadShopServices()
    {
        DataTable dt = ShopManager.GetShopServices(_shopID);
        
        if (dt != null && dt.Rows.Count > 0)
        {
            rptServices.DataSource = dt;
            rptServices.DataBind();
            pnlNoServices.Visible = false;
        }
        else
        {
            rptServices.DataSource = null;
            rptServices.DataBind();
            pnlNoServices.Visible = true;
        }
    }

    /// <summary>
    /// 加载维修店评价信息
    /// </summary>
    private void LoadShopReviews()
    {
        // 获取当前页码
        if (Request.QueryString["page"] != null)
        {
            int.TryParse(Request.QueryString["page"], out CurrentPage);
            if (CurrentPage <= 0) CurrentPage = 1;
        }

        // 获取总评价数
        int totalReviews = ShopManager.GetShopReviewCount(_shopID);
        
        // 计算总页数
        int totalPages = (int)Math.Ceiling((double)totalReviews / PageSize);
        
        // 确保当前页在有效范围内
        if (CurrentPage > totalPages && totalPages > 0)
        {
            CurrentPage = totalPages;
        }
        
        // 获取当前页的评价数据
        DataTable reviews = ShopManager.GetShopReviews(_shopID, CurrentPage, PageSize);
        
        if (reviews != null && reviews.Rows.Count > 0)
        {
            rptReviews.DataSource = reviews;
            rptReviews.DataBind();
            pnlNoReviews.Visible = false;
            
            // 设置分页
            if (totalPages > 1)
            {
                List<PageInfo> pages = new List<PageInfo>();
                for (int i = 1; i <= totalPages; i++)
                {
                    pages.Add(new PageInfo { PageNumber = i });
                }
                
                rptPagination.DataSource = pages;
                rptPagination.DataBind();
                pnlPagination.Visible = true;
            }
            else
            {
                pnlPagination.Visible = false;
            }
        }
        else
        {
            rptReviews.DataSource = null;
            rptReviews.DataBind();
            pnlNoReviews.Visible = true;
            pnlPagination.Visible = false;
        }
    }

    /// <summary>
    /// 根据评分获取星级HTML
    /// </summary>
    protected string GetStarRating(double rating)
    {
        string starsHtml = "";
        int fullStars = (int)Math.Floor(rating);
        bool hasHalfStar = (rating - fullStars) >= 0.5;

        // 添加实心星星
        for (int i = 0; i < fullStars; i++)
        {
            starsHtml += "<i class=\"fas fa-star\"></i>";
        }

        // 添加半星
        if (hasHalfStar)
        {
            starsHtml += "<i class=\"fas fa-star-half-alt\"></i>";
            fullStars++;
        }

        // 添加空心星星
        for (int i = fullStars; i < 5; i++)
        {
            starsHtml += "<i class=\"far fa-star\"></i>";
        }

        return starsHtml;
    }

    /// <summary>
    /// 处理分页控件的命令事件
    /// </summary>
    protected void rptPagination_ItemCommand(object source, RepeaterCommandEventArgs e)
    {
        if (e.CommandName == "ChangePage")
        {
            int page = Convert.ToInt32(e.CommandArgument);
            Response.Redirect($"ShopDetails.aspx?id={_shopID}&page={page}");
        }
    }

    /// <summary>
    /// 处理选择服务按钮点击事件
    /// </summary>
    protected void btnSelectService_Click(object sender, EventArgs e)
    {
        Button btn = (Button)sender;
        int serviceID = Convert.ToInt32(btn.CommandArgument);
        
        // 如果用户已登录，跳转到预约页面；否则跳转到登录页面
        if (User.Identity.IsAuthenticated)
        {
            Response.Redirect($"~/CarOwner/Appointments.aspx?shopid={_shopID}&serviceid={serviceID}");
        }
        else
        {
            Response.Redirect($"~/Login.aspx?returnurl=ShopDetails.aspx?id={_shopID}");
        }
    }

    /// <summary>
    /// 处理预约按钮点击事件
    /// </summary>
    protected void lnkMakeAppointment_Click(object sender, EventArgs e)
    {
        // 如果用户已登录，跳转到预约页面；否则跳转到登录页面
        if (User.Identity.IsAuthenticated)
        {
            Response.Redirect($"~/CarOwner/Appointments.aspx?shopid={_shopID}");
        }
        else
        {
            Response.Redirect($"~/Login.aspx?returnurl=ShopDetails.aspx?id={_shopID}");
        }
    }

    /// <summary>
    /// 分页信息类
    /// </summary>
    public class PageInfo
    {
        public int PageNumber { get; set; }
    }
} 