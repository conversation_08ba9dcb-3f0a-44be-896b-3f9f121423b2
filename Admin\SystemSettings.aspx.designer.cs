//------------------------------------------------------------------------------
// <自动生成>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，如果
//     重新生成代码，则所做更改将丢失。
// </自动生成>
//------------------------------------------------------------------------------



public partial class Admin_SystemSettings
{

    /// <summary>
    /// btnBackupDatabase 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnBackupDatabase;

    /// <summary>
    /// lblBackupMessage 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label lblBackupMessage;

    /// <summary>
    /// txtSmtpServer 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtSmtpServer;

    /// <summary>
    /// rfvSmtpServer 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvSmtpServer;

    /// <summary>
    /// txtSmtpPort 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtSmtpPort;

    /// <summary>
    /// rfvSmtpPort 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvSmtpPort;

    /// <summary>
    /// rvSmtpPort 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RangeValidator rvSmtpPort;

    /// <summary>
    /// txtEmailAddress 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtEmailAddress;

    /// <summary>
    /// rfvEmailAddress 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvEmailAddress;

    /// <summary>
    /// revEmailAddress 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RegularExpressionValidator revEmailAddress;

    /// <summary>
    /// txtEmailPassword 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtEmailPassword;

    /// <summary>
    /// rfvEmailPassword 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvEmailPassword;

    /// <summary>
    /// chkEnableSsl 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.CheckBox chkEnableSsl;

    /// <summary>
    /// btnSaveEmailSettings 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnSaveEmailSettings;

    /// <summary>
    /// btnTestEmail 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnTestEmail;

    /// <summary>
    /// txtMinPasswordLength 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtMinPasswordLength;

    /// <summary>
    /// rfvMinPasswordLength 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvMinPasswordLength;

    /// <summary>
    /// rvMinPasswordLength 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RangeValidator rvMinPasswordLength;

    /// <summary>
    /// chkRequireUppercase 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.CheckBox chkRequireUppercase;

    /// <summary>
    /// chkRequireDigit 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.CheckBox chkRequireDigit;

    /// <summary>
    /// chkRequireSpecialChar 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.CheckBox chkRequireSpecialChar;

    /// <summary>
    /// txtMaxLoginAttempts 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtMaxLoginAttempts;

    /// <summary>
    /// rfvMaxLoginAttempts 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvMaxLoginAttempts;

    /// <summary>
    /// rvMaxLoginAttempts 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RangeValidator rvMaxLoginAttempts;

    /// <summary>
    /// txtLockoutDuration 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TextBox txtLockoutDuration;

    /// <summary>
    /// rfvLockoutDuration 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvLockoutDuration;

    /// <summary>
    /// rvLockoutDuration 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RangeValidator rvLockoutDuration;

    /// <summary>
    /// btnSaveSecuritySettings 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnSaveSecuritySettings;

    /// <summary>
    /// btnCleanupSystem 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnCleanupSystem;

    /// <summary>
    /// btnOptimizeDatabase 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnOptimizeDatabase;

    /// <summary>
    /// lblMaintenanceMessage 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label lblMaintenanceMessage;

    /// <summary>
    /// lblMessage 控件。
    /// </summary>
    /// <remarks>
    /// 自动生成的字段。
    /// 若要进行修改，请将字段声明从设计器文件移到代码隐藏文件。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label lblMessage;
}
