using System;

/// <summary>
/// 用户信息类
/// </summary>
public class UserInfo
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserID { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// 用户类型
    /// </summary>
    public string UserType { get; set; }  // "Admin", "CarOwner", "RepairShop"

    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime RegisterDate { get; set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; }

    public UserInfo()
    {
        RegisterDate = DateTime.Now;
        IsActive = true;
    }
} 