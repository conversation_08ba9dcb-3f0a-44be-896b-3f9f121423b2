USE CarRepairServiceDB;

-- 添加经度列到RepairShops表
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairShops]') AND name = 'Longitude')
BEGIN
    ALTER TABLE [dbo].[RepairShops]
    ADD Longitude DECIMAL(10, 6) NULL;
    PRINT '成功添加Longitude列到RepairShops表';
END
ELSE
BEGIN
    PRINT 'Longitude列已存在于RepairShops表';
END

-- 添加纬度列到RepairShops表
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairShops]') AND name = 'Latitude')
BEGIN
    ALTER TABLE [dbo].[RepairShops]
    ADD Latitude DECIMAL(10, 6) NULL;
    PRINT '成功添加Latitude列到RepairShops表';
END
ELSE
BEGIN
    PRINT 'Latitude列已存在于RepairShops表';
END

-- 确认列已添加
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'RepairShops'; 