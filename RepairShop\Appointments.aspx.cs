using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class RepairShop_Appointments : System.Web.UI.Page
{
    private int userID;
    private int shopID;
    private int? currentAppointmentID = null;
    private string filterStatus = string.Empty;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取当前用户信息
        if (Session["UserID"] == null || Session["UserType"] == null)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        string userType = Session["UserType"].ToString();

        if (userType != "RepairShop")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取维修店ID
        shopID = ShopManager.GetShopIDByUserID(userID);
        if (shopID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }
        
        // 在页面首次加载时，检查并自动取消过期未确认的预约
        if (!IsPostBack)
        {
            try
            {
                int cancelledCount = AppointmentManager.AutoCancelExpiredAppointments();
                if (cancelledCount > 0)
                {
                    // 添加通知消息
                    lblMessage.Text = $"系统已自动取消 {cancelledCount} 个过期未确认的预约。";
                    lblMessage.CssClass = "alert alert-info";
                    lblMessage.Visible = true;
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响页面加载
                System.Diagnostics.Debug.WriteLine($"页面加载时自动取消过期预约出错: {ex.Message}");
            }
        }

        // 在回发时恢复currentAppointmentID
        if (IsPostBack)
        {
            if (ViewState["CurrentAppointmentID"] != null)
            {
                int storedID;
                if (int.TryParse(ViewState["CurrentAppointmentID"].ToString(), out storedID))
                {
                    currentAppointmentID = storedID;
                    System.Diagnostics.Debug.WriteLine($"从ViewState恢复预约ID: {currentAppointmentID}");
                }
            }
        }

        if (!IsPostBack)
        {
            // 清除之前的消息
            lblMessage.Visible = false;
            
            // 检查是否有特定的预约ID
            if (Request.QueryString["id"] != null)
            {
                int appointmentID;
                if (int.TryParse(Request.QueryString["id"], out appointmentID))
                {
                    // 显示特定预约的详情
                    ShowAppointmentDetails(appointmentID);
                    return;
                }
            }

            // 加载预约列表
            LoadAppointments();
        }
    }

    /// <summary>
    /// 加载预约列表
    /// </summary>
    private void LoadAppointments()
    {
        // 从会话中获取过滤状态（如果有）
        if (Session["AppointmentFilterStatus"] != null)
        {
            filterStatus = Session["AppointmentFilterStatus"].ToString();
            ddlFilterStatus.SelectedValue = filterStatus;
        }

        // 根据过滤条件获取预约列表
        DataTable appointments = GetFilteredAppointments();
        
        gvAppointments.DataSource = appointments;
        gvAppointments.DataBind();
    }

    /// <summary>
    /// 根据过滤条件获取预约列表
    /// </summary>
    private DataTable GetFilteredAppointments()
    {
        string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        co.FullName AS OwnerName, s.ServiceName
                        FROM Appointments a
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE a.ShopID = @ShopID";

        if (!string.IsNullOrEmpty(filterStatus))
        {
            query += " AND a.Status = @Status";
        }

        query += " ORDER BY a.AppointmentDate DESC";

        System.Data.SqlClient.SqlParameter[] parameters;
        if (!string.IsNullOrEmpty(filterStatus))
        {
            parameters = new System.Data.SqlClient.SqlParameter[]
            {
                new System.Data.SqlClient.SqlParameter("@ShopID", shopID),
                new System.Data.SqlClient.SqlParameter("@Status", filterStatus)
            };
        }
        else
        {
            parameters = new System.Data.SqlClient.SqlParameter[]
            {
                new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
            };
        }

        return DatabaseHelper.ExecuteQuery(query, parameters);
    }

    /// <summary>
    /// 显示预约详情
    /// </summary>
    private void ShowAppointmentDetails(int appointmentID)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"显示预约详情 - 预约ID: {appointmentID}");
            
            string query = @"SELECT a.AppointmentID, a.AppointmentDate, a.Status, a.Description,
                            c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                            co.FullName AS OwnerName, s.ServiceName, s.EstimatedTime, s.BasePrice
                            FROM Appointments a
                            INNER JOIN Cars c ON a.CarID = c.CarID
                            INNER JOIN CarOwners co ON c.OwnerID = co.OwnerID
                            INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                            WHERE a.AppointmentID = @AppointmentID AND a.ShopID = @ShopID";

            System.Data.SqlClient.SqlParameter[] parameters = 
            {
                new System.Data.SqlClient.SqlParameter("@AppointmentID", appointmentID),
                new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
            };

            DataTable appointmentDetails = DatabaseHelper.ExecuteQuery(query, parameters);
            
            if (appointmentDetails != null && appointmentDetails.Rows.Count > 0)
            {
                DataRow appointment = appointmentDetails.Rows[0];
                
                // 保存当前预约ID到成员变量和ViewState
                currentAppointmentID = appointmentID;
                ViewState["CurrentAppointmentID"] = appointmentID;
                
                System.Diagnostics.Debug.WriteLine($"成功保存预约ID到ViewState: {appointmentID}");
                
                // 填充详情页面
                lblAppointmentID.Text = appointment["AppointmentID"].ToString();
                lblAppointmentDate.Text = Convert.ToDateTime(appointment["AppointmentDate"]).ToString("yyyy-MM-dd HH:mm");
                lblOwnerName.Text = appointment["OwnerName"].ToString();
                lblCarInfo.Text = appointment["CarInfo"].ToString();
                lblServiceName.Text = appointment["ServiceName"].ToString();
                lblEstimatedTime.Text = appointment["EstimatedTime"].ToString();
                lblBasePrice.Text = string.Format("¥{0:N2}", Convert.ToDecimal(appointment["BasePrice"]));
                
                string status = appointment["Status"].ToString();
                lblStatus.Text = GetStatusText(status);
                lblStatus.CssClass = GetStatusClass(status);
                
                lblDescription.Text = appointment["Description"] != DBNull.Value ? 
                    appointment["Description"].ToString() : "无";
                
                // 根据状态显示不同的按钮
                btnConfirm.Visible = status == "Pending";
                btnComplete.Visible = status == "Confirmed";
                
                // 显示详情面板，隐藏列表和其他面板
                pnlAppointmentDetails.Visible = true;
                gvAppointments.Visible = false;
                pnlCompleteForm.Visible = false;
            }
            else
            {
                // 无法找到预约或不属于该维修店
                System.Diagnostics.Debug.WriteLine($"未找到预约或预约不属于此维修店 - 预约ID: {appointmentID}");
                
                // 清除当前预约ID
                currentAppointmentID = null;
                ViewState["CurrentAppointmentID"] = null;
                
                lblMessage.Text = "找不到此预约信息或该预约不属于您的维修店";
                lblMessage.CssClass = "alert alert-warning";
                lblMessage.Visible = true;
                
                // 返回列表页面
                pnlAppointmentDetails.Visible = false;
                gvAppointments.Visible = true;
                LoadAppointments();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"显示预约详情时出错: {ex.Message}");
            
            lblMessage.Text = "加载预约详情时出错";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
            
            // 返回列表页面
            pnlAppointmentDetails.Visible = false;
            gvAppointments.Visible = true;
            LoadAppointments();
        }
    }

    /// <summary>
    /// 返回列表按钮点击事件
    /// </summary>
    protected void btnBack_Click(object sender, EventArgs e)
    {
        // 隐藏详情面板，显示列表
        pnlAppointmentDetails.Visible = false;
        pnlCompleteForm.Visible = false;
        gvAppointments.Visible = true;
        
        // 重新加载预约列表
        LoadAppointments();
    }

    /// <summary>
    /// 确认预约按钮点击事件
    /// </summary>
    protected void btnConfirm_Click(object sender, EventArgs e)
    {
        if (currentAppointmentID.HasValue)
        {
            bool success = ShopManager.ConfirmAppointment(currentAppointmentID.Value);
            if (success)
            {
                // 成功确认，刷新详情页
                ShowAppointmentDetails(currentAppointmentID.Value);
                lblMessage.Text = "预约已成功确认。";
                lblMessage.CssClass = "alert alert-success";
                lblMessage.Visible = true;
            }
            else
            {
                lblMessage.Text = "确认失败，请稍后再试。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }
    }

    /// <summary>
    /// 完成维修按钮点击事件
    /// </summary>
    protected void btnComplete_Click(object sender, EventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"完成维修按钮被点击，当前预约ID: {(currentAppointmentID.HasValue ? currentAppointmentID.Value.ToString() : "null")}");
            
            if (currentAppointmentID.HasValue)
            {
                // 确保当前预约ID有效
                int appointmentID = currentAppointmentID.Value;
                
                // 将ID存入ViewState防止丢失
                ViewState["CurrentAppointmentID"] = appointmentID;
                
                System.Diagnostics.Debug.WriteLine($"显示完成维修表单 - 预约ID: {appointmentID}");
                
                // 显示完成维修表单
                pnlAppointmentDetails.Visible = false;
                pnlCompleteForm.Visible = true;
                gvAppointments.Visible = false;
                
                // 初始化表单
                txtDiagnosisDetails.Text = string.Empty;
                txtPartsReplaced.Text = string.Empty;
                txtLaborCost.Text = "0.00";
                txtPartsCost.Text = "0.00";
                txtTechnicianName.Text = string.Empty;

                // 确保消息标签隐藏
                lblMessage.Visible = false;
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("无法显示完成维修表单 - 预约ID为空");
                lblMessage.Text = "无法处理此预约，预约ID无效";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                
                // 返回列表页面
                pnlAppointmentDetails.Visible = false;
                pnlCompleteForm.Visible = false;
                gvAppointments.Visible = true;
                LoadAppointments();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"完成维修按钮点击事件出错: {ex.Message}");
            lblMessage.Text = "处理请求时出错，请稍后再试";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
            
            // 返回列表页面
            pnlAppointmentDetails.Visible = false;
            pnlCompleteForm.Visible = false;
            gvAppointments.Visible = true;
            LoadAppointments();
        }
    }

    /// <summary>
    /// 提交完成记录按钮点击事件
    /// </summary>
    protected void btnSubmitComplete_Click(object sender, EventArgs e)
    {
        try
        {
            // 验证表单输入
            if (!Page.IsValid || !currentAppointmentID.HasValue)
            {
                lblMessage.Text = "表单验证失败或预约ID无效";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            // 记录操作开始
            System.Diagnostics.Debug.WriteLine($"开始提交完成维修记录 - 预约ID: {currentAppointmentID.Value}");

            // 获取表单数据
            string diagnosisDetails = txtDiagnosisDetails.Text.Trim();
            string partsReplaced = txtPartsReplaced.Text.Trim();
            decimal laborCost = 0;
            decimal partsCost = 0;

            // 尝试转换费用
            try
            {
                laborCost = Convert.ToDecimal(txtLaborCost.Text);
                partsCost = Convert.ToDecimal(txtPartsCost.Text);
            }
            catch (FormatException)
            {
                lblMessage.Text = "费用金额格式无效，请输入有效的数字";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            string technicianName = txtTechnicianName.Text.Trim();
            
            if (string.IsNullOrEmpty(diagnosisDetails))
            {
                lblMessage.Text = "诊断详情不能为空";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            if (string.IsNullOrEmpty(technicianName))
            {
                lblMessage.Text = "技师姓名不能为空";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            System.Diagnostics.Debug.WriteLine($"表单数据验证通过，准备调用CompleteAppointment方法");
            
            // 调用完成预约方法
            int recordID = ShopManager.CompleteAppointment(
                currentAppointmentID.Value,
                diagnosisDetails,
                partsReplaced,
                laborCost,
                partsCost,
                technicianName
            );
            
            System.Diagnostics.Debug.WriteLine($"CompleteAppointment方法返回值: {recordID}");
            
            if (recordID > 0)
            {
                // 完成成功，清除预约ID和输入表单
                currentAppointmentID = null;
                
                // 返回列表页
                pnlCompleteForm.Visible = false;
                gvAppointments.Visible = true;
                
                // 重新加载预约列表
                LoadAppointments();
                
                // 显示成功消息
                lblMessage.Text = "维修已完成，记录已保存！";
                lblMessage.CssClass = "alert alert-success";
                lblMessage.Visible = true;
            }
            else
            {
                lblMessage.Text = "操作失败，维修记录保存失败。请稍后再试。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }
        catch (Exception ex)
        {
            // 记录详细错误
            System.Diagnostics.Debug.WriteLine($"完成维修表单提交时出错: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
            
            lblMessage.Text = $"提交过程中发生错误: {ex.Message}";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
        }
    }

    /// <summary>
    /// 取消完成表单按钮点击事件
    /// </summary>
    protected void btnCancelComplete_Click(object sender, EventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("取消完成维修记录操作");
            
            // 返回预约详情页
            if (currentAppointmentID.HasValue)
            {
                System.Diagnostics.Debug.WriteLine($"返回到预约详情页 - 预约ID: {currentAppointmentID.Value}");
                pnlCompleteForm.Visible = false;
                
                // 清除可能的错误消息
                lblMessage.Visible = false;
                
                // 重新显示详情页
                ShowAppointmentDetails(currentAppointmentID.Value);
            }
            else
            {
                // 如果ID无效，返回列表页
                System.Diagnostics.Debug.WriteLine("预约ID无效，返回列表页");
                pnlCompleteForm.Visible = false;
                gvAppointments.Visible = true;
                LoadAppointments();
                
                lblMessage.Text = "操作已取消";
                lblMessage.CssClass = "alert alert-info";
                lblMessage.Visible = true;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"取消完成表单时出错: {ex.Message}");
            
            // 确保返回列表页面
            pnlCompleteForm.Visible = false;
            gvAppointments.Visible = true;
            LoadAppointments();
            
            lblMessage.Text = "处理请求时发生错误，已返回列表页";
            lblMessage.CssClass = "alert alert-warning";
            lblMessage.Visible = true;
        }
    }

    /// <summary>
    /// GridView行命令事件
    /// </summary>
    protected void gvAppointments_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        int appointmentID = Convert.ToInt32(e.CommandArgument);
        
        if (e.CommandName == "ViewAppointment")
        {
            // 查看预约详情
            ShowAppointmentDetails(appointmentID);
        }
        else if (e.CommandName == "ConfirmAppointment")
        {
            // 确认预约
            bool success = ShopManager.ConfirmAppointment(appointmentID);
            if (success)
            {
                // 成功确认，刷新列表
                LoadAppointments();
                lblMessage.Text = "预约已成功确认。";
                lblMessage.CssClass = "alert alert-success";
                lblMessage.Visible = true;
            }
            else
            {
                lblMessage.Text = "确认失败，请稍后再试。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }
        else if (e.CommandName == "CompleteAppointment")
        {
            try
            {
                // 保存当前预约ID，并显示完成维修表单
                currentAppointmentID = appointmentID;
                ViewState["CurrentAppointmentID"] = appointmentID;
                
                System.Diagnostics.Debug.WriteLine($"列表中点击完成按钮 - 预约ID: {appointmentID}");
                
                // 先加载预约详情以验证预约状态
                string query = @"SELECT Status FROM Appointments 
                                WHERE AppointmentID = @AppointmentID AND ShopID = @ShopID";
                
                System.Data.SqlClient.SqlParameter[] parameters = 
                {
                    new System.Data.SqlClient.SqlParameter("@AppointmentID", appointmentID),
                    new System.Data.SqlClient.SqlParameter("@ShopID", shopID)
                };
                
                DataTable result = DatabaseHelper.ExecuteQuery(query, parameters);
                
                if (result != null && result.Rows.Count > 0)
                {
                    string status = result.Rows[0]["Status"].ToString();
                    
                    if (status == "Confirmed")
                    {
                        // 显示完成维修表单，不需要先显示详情
                        pnlAppointmentDetails.Visible = false;
                        pnlCompleteForm.Visible = true;
                        gvAppointments.Visible = false;
                        
                        // 初始化表单
                        txtDiagnosisDetails.Text = string.Empty;
                        txtPartsReplaced.Text = string.Empty;
                        txtLaborCost.Text = "0.00";
                        txtPartsCost.Text = "0.00";
                        txtTechnicianName.Text = string.Empty;
                        
                        // 确保消息标签隐藏
                        lblMessage.Visible = false;
                    }
                    else
                    {
                        lblMessage.Text = "只能对已确认的预约进行完成操作";
                        lblMessage.CssClass = "alert alert-warning";
                        lblMessage.Visible = true;
                    }
                }
                else
                {
                    lblMessage.Text = "找不到对应的预约信息";
                    lblMessage.CssClass = "alert alert-danger";
                    lblMessage.Visible = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理CompleteAppointment命令出错: {ex.Message}");
                lblMessage.Text = "无法显示维修表单，请稍后再试。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }
    }

    /// <summary>
    /// 过滤器选择变更事件
    /// </summary>
    protected void ddlFilterStatus_SelectedIndexChanged(object sender, EventArgs e)
    {
        // 保存过滤状态到会话
        filterStatus = ddlFilterStatus.SelectedValue;
        Session["AppointmentFilterStatus"] = filterStatus;
        
        // 重新加载预约列表
        LoadAppointments();
    }

    /// <summary>
    /// GridView分页事件
    /// </summary>
    protected void gvAppointments_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvAppointments.PageIndex = e.NewPageIndex;
        LoadAppointments();
    }

    /// <summary>
    /// 获取状态文本
    /// </summary>
    public string GetStatusText(string status)
    {
        switch (status)
        {
            case "Pending":
                return "待处理";
            case "Confirmed":
                return "已确认";
            case "Completed":
                return "已完成";
            case "Cancelled":
                return "已取消";
            default:
                return status;
        }
    }

    /// <summary>
    /// 获取状态CSS类
    /// </summary>
    public string GetStatusClass(string status)
    {
        switch (status)
        {
            case "Pending":
                return "badge badge-warning";
            case "Confirmed":
                return "badge badge-success";
            case "Completed":
                return "badge badge-primary";
            case "Cancelled":
                return "badge badge-danger";
            default:
                return "badge badge-secondary";
        }
    }
} 