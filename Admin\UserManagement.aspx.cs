using System;
using System.Data;
using System.Web.UI;
using System.Data.SqlClient;
using System.Web.UI.WebControls;

public partial class Admin_UserManagement : Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否已登录且为管理员
        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "Admin")
        {
            Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.RawUrl));
            return;
        }

        if (!IsPostBack)
        {
            // 检查是否有指定的用户类型参数
            string userType = Request.QueryString["type"];
            if (!string.IsNullOrEmpty(userType))
            {
                ddlUserType.SelectedValue = userType;
            }

            // 加载用户列表
            LoadUsers();
        }
    }

    /// <summary>
    /// 加载用户列表
    /// </summary>
    private void LoadUsers()
    {
        try
        {
            string userType = ddlUserType.SelectedValue;
            string searchText = txtSearch.Text.Trim();

            // 获取用户列表
            DataTable dt = AdminManager.GetUsers(userType, searchText);
            gvUsers.DataSource = dt;
            gvUsers.DataBind();
        }
        catch (Exception ex)
        {
            lblMessage.Text = "加载用户列表时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 搜索按钮点击事件
    /// </summary>
    protected void btnSearch_Click(object sender, EventArgs e)
    {
        // 重置分页索引
        gvUsers.PageIndex = 0;
        
        // 加载用户列表
        LoadUsers();
        
        // 隐藏用户详情面板
        pnlUserDetails.Visible = false;
    }

    /// <summary>
    /// 用户列表行命令事件
    /// </summary>
    protected void gvUsers_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        try
        {
            int userID = Convert.ToInt32(e.CommandArgument);
            
            switch (e.CommandName)
            {
                case "ViewUser":
                    // 查看用户详情
                    ViewUserDetails(userID);
                    break;
                    
                case "ToggleStatus":
                    // 切换用户状态
                    ToggleUserStatus(userID);
                    break;
                    
                case "DeleteUser":
                    // 删除用户
                    DeleteUser(userID);
                    break;
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "处理用户操作时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 用户列表行数据绑定事件
    /// </summary>
    protected void gvUsers_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            // 获取用户类型
            string userType = DataBinder.Eval(e.Row.DataItem, "UserType").ToString();
            
            // 如果是管理员，禁用删除按钮
            if (userType == "Admin")
            {
                LinkButton lbtnDelete = (LinkButton)e.Row.FindControl("lbtnDelete");
                if (lbtnDelete != null)
                {
                    // 检查是否是当前登录的管理员
                    int userID = Convert.ToInt32(DataBinder.Eval(e.Row.DataItem, "UserID"));
                    int currentUserID = Convert.ToInt32(Session["UserID"]);
                    
                    if (userID == currentUserID)
                    {
                        // 禁用当前登录管理员的状态切换和删除按钮
                        LinkButton lbtnStatus = (LinkButton)e.Row.FindControl("lbtnStatus");
                        if (lbtnStatus != null)
                        {
                            lbtnStatus.Enabled = false;
                            lbtnStatus.CssClass = "btn btn-sm btn-secondary";
                            lbtnStatus.OnClientClick = "";
                        }
                        
                        lbtnDelete.Enabled = false;
                        lbtnDelete.CssClass = "btn btn-sm btn-secondary";
                        lbtnDelete.OnClientClick = "";
                    }
                }
            }
        }
    }

    /// <summary>
    /// 用户列表分页事件
    /// </summary>
    protected void gvUsers_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvUsers.PageIndex = e.NewPageIndex;
        LoadUsers();
    }

    /// <summary>
    /// 获取用户类型徽章样式
    /// </summary>
    protected string GetUserTypeBadgeClass(string userType)
    {
        switch (userType)
        {
            case "CarOwner":
                return "badge badge-primary";
            case "RepairShop":
                return "badge badge-success";
            case "Admin":
                return "badge badge-danger";
            default:
                return "badge badge-secondary";
        }
    }

    /// <summary>
    /// 获取用户类型文本
    /// </summary>
    protected string GetUserTypeText(string userType)
    {
        switch (userType)
        {
            case "CarOwner":
                return "车主";
            case "RepairShop":
                return "维修店";
            case "Admin":
                return "管理员";
            default:
                return userType;
        }
    }

    /// <summary>
    /// 查看用户详情
    /// </summary>
    private void ViewUserDetails(int userID)
    {
        try
        {
            // 获取用户详情
            DataTable dt = AdminManager.GetUserDetails(userID);
            if (dt.Rows.Count > 0)
            {
                DataRow row = dt.Rows[0];
                
                // 设置基本用户信息
                lblUserID.Text = row["UserID"].ToString();
                lblUsername.Text = row["Username"].ToString();
                lblEmail.Text = row["Email"].ToString();
                lblUserType.Text = GetUserTypeText(row["UserType"].ToString());
                lblStatus.Text = (bool)row["IsActive"] ? "已激活" : "已禁用";
                lblRegistrationDate.Text = Convert.ToDateTime(row["RegisterDate"]).ToString("yyyy-MM-dd HH:mm:ss");
                
                // 根据用户类型显示不同的详情
                string userType = row["UserType"].ToString();
                pnlCarOwnerDetails.Visible = false;
                pnlShopDetails.Visible = false;
                
                switch (userType)
                {
                    case "CarOwner":
                        // 显示车主详情
                        ShowCarOwnerDetails(userID);
                        break;
                        
                    case "RepairShop":
                        // 显示维修店详情
                        ShowShopDetails(userID);
                        break;
                }
                
                // 显示用户详情面板
                pnlUserDetails.Visible = true;
            }
            else
            {
                lblMessage.Text = "找不到指定的用户";
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "查看用户详情时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 显示车主详情
    /// </summary>
    private void ShowCarOwnerDetails(int userID)
    {
        try
        {
            // 获取车主详情
            string query = @"SELECT co.* 
                            FROM CarOwners co
                            WHERE co.UserID = @UserID";
            SqlParameter parameter = new SqlParameter("@UserID", userID);
            DataTable dt = DatabaseHelper.ExecuteQuery(query, parameter);
            
            if (dt.Rows.Count > 0)
            {
                DataRow row = dt.Rows[0];
                int ownerID = Convert.ToInt32(row["OwnerID"]);
                
                // 设置车主信息
                lblOwnerName.Text = row["FullName"].ToString();
                lblOwnerAddress.Text = row["Address"] != DBNull.Value ? row["Address"].ToString() : "未设置";
                
                // 获取车辆列表
                string carsQuery = @"SELECT * FROM Cars WHERE OwnerID = @OwnerID";
                SqlParameter carsParameter = new SqlParameter("@OwnerID", ownerID);
                DataTable carsDt = DatabaseHelper.ExecuteQuery(carsQuery, carsParameter);
                
                gvCars.DataSource = carsDt;
                gvCars.DataBind();
                
                // 显示车主详情面板
                pnlCarOwnerDetails.Visible = true;
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "显示车主详情时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 显示维修店详情
    /// </summary>
    private void ShowShopDetails(int userID)
    {
        try
        {
            // 获取维修店详情
            string query = @"SELECT rs.*, u.PhoneNumber 
                            FROM RepairShops rs
                            JOIN Users u ON rs.UserID = u.UserID
                            WHERE rs.UserID = @UserID";
            SqlParameter parameter = new SqlParameter("@UserID", userID);
            DataTable dt = DatabaseHelper.ExecuteQuery(query, parameter);
            
            if (dt.Rows.Count > 0)
            {
                DataRow row = dt.Rows[0];
                int shopID = Convert.ToInt32(row["ShopID"]);
                
                // 设置维修店信息
                lblShopName.Text = row["ShopName"].ToString();
                lblContactPerson.Text = row["ContactPerson"] != DBNull.Value ? row["ContactPerson"].ToString() : "未设置";
                lblShopPhone.Text = row["PhoneNumber"] != DBNull.Value ? row["PhoneNumber"].ToString() : "未设置";
                lblShopAddress.Text = row["Address"].ToString();
                lblDescription.Text = row["Description"] != DBNull.Value ? row["Description"].ToString() : "未设置";
                lblRating.Text = row["Rating"] != DBNull.Value ? Convert.ToDecimal(row["Rating"]).ToString("0.0") + " / 5.0" : "暂无评分";
                
                // 获取服务列表
                string servicesQuery = @"SELECT rs.ServiceID, rs.ServiceName, rs.BasePrice as Price, rs.IsActive, sc.CategoryName
                                        FROM RepairServices rs
                                        LEFT JOIN ServiceCategories sc ON rs.CategoryID = sc.CategoryID
                                        WHERE rs.ShopID = @ShopID";
                SqlParameter servicesParameter = new SqlParameter("@ShopID", shopID);
                DataTable servicesDt = DatabaseHelper.ExecuteQuery(servicesQuery, servicesParameter);
                
                gvServices.DataSource = servicesDt;
                gvServices.DataBind();
                
                // 显示维修店详情面板
                pnlShopDetails.Visible = true;
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "显示维修店详情时出错：" + ex.Message;
        }
    }

    /// <summary>
    /// 切换用户状态
    /// </summary>
    private void ToggleUserStatus(int userID)
    {
        try
        {
            // 获取当前用户状态
            string query = "SELECT IsActive FROM Users WHERE UserID = @UserID";
            SqlParameter parameter = new SqlParameter("@UserID", userID);
            object result = DatabaseHelper.ExecuteScalar(query, parameter);
            
            if (result != null && result != DBNull.Value)
            {
                bool currentStatus = Convert.ToBoolean(result);
                
                // 更新用户状态
                bool updateResult = AdminManager.UpdateUserStatus(userID, !currentStatus);
                if (updateResult)
                {
                    lblMessage.Text = "用户状态已更新";
                    lblMessage.CssClass = "text-success";
                }
                else
                {
                    lblMessage.Text = "更新用户状态失败";
                    lblMessage.CssClass = "text-danger";
                }
                
                // 重新加载用户列表
                LoadUsers();
                
                // 如果正在查看该用户的详情，则刷新详情
                if (pnlUserDetails.Visible && lblUserID.Text == userID.ToString())
                {
                    ViewUserDetails(userID);
                }
            }
            else
            {
                lblMessage.Text = "找不到指定的用户";
                lblMessage.CssClass = "text-danger";
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "切换用户状态时出错：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    private void DeleteUser(int userID)
    {
        try
        {
            // 删除用户
            bool result = AdminManager.DeleteUser(userID);
            if (result)
            {
                lblMessage.Text = "用户已成功删除";
                lblMessage.CssClass = "text-success";
            }
            else
            {
                lblMessage.Text = "删除用户失败";
                lblMessage.CssClass = "text-danger";
            }
            
            // 重新加载用户列表
            LoadUsers();
            
            // 隐藏用户详情面板
            pnlUserDetails.Visible = false;
        }
        catch (Exception ex)
        {
            lblMessage.Text = "删除用户时出错：" + ex.Message;
            lblMessage.CssClass = "text-danger";
        }
    }

    /// <summary>
    /// 关闭详情按钮点击事件
    /// </summary>
    protected void lbtnCloseDetails_Click(object sender, EventArgs e)
    {
        pnlUserDetails.Visible = false;
    }

    /// <summary>
    /// 解锁用户账号
    /// </summary>
    protected void btnUnlockAccount_Click(object sender, EventArgs e)
    {
        string username = txtUnlockUsername.Text.Trim();
        
        if (string.IsNullOrEmpty(username))
        {
            lblUnlockMessage.Text = "请输入用户名";
            lblUnlockMessage.CssClass = "text-danger";
            return;
        }
        
        try
        {
            // 查找用户ID
            string getUserQuery = "SELECT UserID FROM Users WHERE Username = @Username";
            System.Data.SqlClient.SqlParameter userParam = new System.Data.SqlClient.SqlParameter("@Username", username);
            object userIDObj = DatabaseHelper.ExecuteScalar(getUserQuery, userParam);
            
            if (userIDObj == null || userIDObj == DBNull.Value)
            {
                lblUnlockMessage.Text = "找不到此用户";
                lblUnlockMessage.CssClass = "text-danger";
                return;
            }
            
            int userID = Convert.ToInt32(userIDObj);
            
            // 检查用户是否被锁定
            string checkLockQuery = @"SELECT LockoutEndTime 
                                    FROM UserLogins 
                                    WHERE UserID = @UserID 
                                    AND LockoutEndTime IS NOT NULL";
            System.Data.SqlClient.SqlParameter lockParam = new System.Data.SqlClient.SqlParameter("@UserID", userID);
            object lockTimeObj = DatabaseHelper.ExecuteScalar(checkLockQuery, lockParam);
            
            if (lockTimeObj == null || lockTimeObj == DBNull.Value)
            {
                lblUnlockMessage.Text = "此用户账号未被锁定";
                lblUnlockMessage.CssClass = "text-info";
                return;
            }
            
            // 解锁账号
            string unlockQuery = @"UPDATE UserLogins SET 
                                LockoutEndTime = NULL, 
                                FailedAttempts = 0
                                WHERE UserID = @UserID";
            System.Data.SqlClient.SqlParameter unlockParam = new System.Data.SqlClient.SqlParameter("@UserID", userID);
            DatabaseHelper.ExecuteNonQuery(unlockQuery, unlockParam);
            
            // 记录解锁操作
            System.Diagnostics.Debug.WriteLine($"管理员手动解锁了用户 {username}(ID:{userID}) 的账号");
            
            lblUnlockMessage.Text = $"已成功解锁用户 {username} 的账号";
            lblUnlockMessage.CssClass = "text-success";
            
            // 清空输入框
            txtUnlockUsername.Text = string.Empty;
        }
        catch (Exception ex)
        {
            lblUnlockMessage.Text = "解锁账号时出错：" + ex.Message;
            lblUnlockMessage.CssClass = "text-danger";
        }
    }
    
    /// <summary>
    /// 检查账号锁定状态
    /// </summary>
    protected void btnCheckLockStatus_Click(object sender, EventArgs e)
    {
        string username = txtUnlockUsername.Text.Trim();
        
        if (string.IsNullOrEmpty(username))
        {
            lblUnlockMessage.Text = "请输入用户名";
            lblUnlockMessage.CssClass = "text-danger";
            return;
        }
        
        try
        {
            // 查找用户ID
            string getUserQuery = "SELECT UserID FROM Users WHERE Username = @Username";
            System.Data.SqlClient.SqlParameter userParam = new System.Data.SqlClient.SqlParameter("@Username", username);
            object userIDObj = DatabaseHelper.ExecuteScalar(getUserQuery, userParam);
            
            if (userIDObj == null || userIDObj == DBNull.Value)
            {
                lblUnlockMessage.Text = "找不到此用户";
                lblUnlockMessage.CssClass = "text-danger";
                return;
            }
            
            int userID = Convert.ToInt32(userIDObj);
            
            // 检查用户锁定状态
            string checkStatusQuery = @"SELECT FailedAttempts, LockoutEndTime, LastLoginAttempt
                                     FROM UserLogins 
                                     WHERE UserID = @UserID";
            System.Data.SqlClient.SqlParameter statusParam = new System.Data.SqlClient.SqlParameter("@UserID", userID);
            System.Data.DataTable statusTable = DatabaseHelper.ExecuteQuery(checkStatusQuery, statusParam);
            
            if (statusTable == null || statusTable.Rows.Count == 0)
            {
                lblUnlockMessage.Text = $"用户 {username} 尚未有登录记录";
                lblUnlockMessage.CssClass = "text-info";
                return;
            }
            
            System.Data.DataRow row = statusTable.Rows[0];
            int failedAttempts = row["FailedAttempts"] != DBNull.Value ? Convert.ToInt32(row["FailedAttempts"]) : 0;
            object lockoutEndTimeObj = row["LockoutEndTime"];
            DateTime? lastLoginAttempt = row["LastLoginAttempt"] != DBNull.Value ? (DateTime?)Convert.ToDateTime(row["LastLoginAttempt"]) : null;
            
            if (lockoutEndTimeObj != DBNull.Value)
            {
                DateTime lockoutEndTime = Convert.ToDateTime(lockoutEndTimeObj);
                
                if (lockoutEndTime > DateTime.Now)
                {
                    TimeSpan remainingTime = lockoutEndTime - DateTime.Now;
                    lblUnlockMessage.Text = $"用户 {username} 的账号已被锁定，将在 {Math.Ceiling(remainingTime.TotalMinutes)} 分钟后（{lockoutEndTime:yyyy-MM-dd HH:mm:ss}）自动解锁";
                    lblUnlockMessage.CssClass = "text-danger";
                }
                else
                {
                    lblUnlockMessage.Text = $"用户 {username} 的账号锁定已过期，但尚未被清除。即将在下次登录时自动解锁。";
                    lblUnlockMessage.CssClass = "text-warning";
                }
            }
            else if (failedAttempts > 0)
            {
                // 获取剩余尝试次数
                int maxAttempts = Convert.ToInt32(SecurityHelper.GetSystemSetting("MaxLoginAttempts", "5"));
                int remainingAttempts = maxAttempts - failedAttempts;
                
                if (remainingAttempts > 0)
                {
                    lblUnlockMessage.Text = $"用户 {username} 的账号未被锁定，但已有 {failedAttempts} 次失败尝试，还剩 {remainingAttempts} 次尝试机会";
                    lblUnlockMessage.CssClass = "text-warning";
                }
                else
                {
                    lblUnlockMessage.Text = $"用户 {username} 的账号状态异常：已达到失败尝试上限但未被锁定";
                    lblUnlockMessage.CssClass = "text-danger";
                }
            }
            else
            {
                lblUnlockMessage.Text = $"用户 {username} 的账号未被锁定";
                lblUnlockMessage.CssClass = "text-success";
            }
            
            if (lastLoginAttempt.HasValue)
            {
                lblUnlockMessage.Text += $"，最后一次登录尝试时间：{lastLoginAttempt.Value:yyyy-MM-dd HH:mm:ss}";
            }
        }
        catch (Exception ex)
        {
            lblUnlockMessage.Text = "检查账号状态时出错：" + ex.Message;
            lblUnlockMessage.CssClass = "text-danger";
        }
    }
} 