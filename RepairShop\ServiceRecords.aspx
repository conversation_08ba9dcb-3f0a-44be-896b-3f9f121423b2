<%@ Page Title="维修记录" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="RepairShop_ServiceRecords" Codebehind="ServiceRecords.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-history"></i> 维修记录</h2>
                <hr />
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-clipboard-list"></i> 维修记录列表</h5>
                            <div>
                                <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control form-control-sm" placeholder="搜索车牌号或车主"></asp:TextBox>
                            </div>
                            <asp:Button ID="btnSearch" runat="server" Text="搜索" CssClass="btn btn-light btn-sm ml-2" OnClick="btnSearch_Click" />
                        </div>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvServiceRecords" runat="server" AutoGenerateColumns="False"
                            CssClass="table table-striped table-hover" DataKeyNames="RecordID"
                            OnRowCommand="gvServiceRecords_RowCommand" AllowPaging="true"
                            PageSize="10" OnPageIndexChanging="gvServiceRecords_PageIndexChanging"
                            EmptyDataText="暂无维修记录">
                            <Columns>
                                <asp:BoundField DataField="RecordID" HeaderText="记录ID" />
                                <asp:BoundField DataField="CompletedDate" HeaderText="完成日期" DataFormatString="{0:yyyy-MM-dd}" />
                                <asp:BoundField DataField="CarInfo" HeaderText="车辆信息" />
                                <asp:BoundField DataField="OwnerName" HeaderText="车主" />
                                <asp:BoundField DataField="ServiceName" HeaderText="服务项目" />
                                <asp:BoundField DataField="TotalCost" HeaderText="总费用" DataFormatString="{0:C}" />
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lbtnView" runat="server" CssClass="btn btn-sm btn-info"
                                            CommandName="ViewRecord" CommandArgument='<%# Eval("RecordID") %>' ToolTip="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <PagerSettings Mode="NumericFirstLast" FirstPageText="首页" LastPageText="末页" />
                            <PagerStyle HorizontalAlign="Center" CssClass="pagination-container" />
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>

        <asp:Panel ID="pnlRecordDetails" runat="server" Visible="false">
            <div class="row">
                <div class="col-md-10 offset-md-1">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle"></i> 维修记录详情</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <dl class="row">
                                        <dt class="col-sm-4">记录ID：</dt>
                                        <dd class="col-sm-8"><asp:Label ID="lblRecordID" runat="server"></asp:Label></dd>
                                        
                                        <dt class="col-sm-4">预约日期：</dt>
                                        <dd class="col-sm-8"><asp:Label ID="lblAppointmentDate" runat="server"></asp:Label></dd>
                                        
                                        <dt class="col-sm-4">完成日期：</dt>
                                        <dd class="col-sm-8"><asp:Label ID="lblCompletedDate" runat="server"></asp:Label></dd>
                                        
                                        <dt class="col-sm-4">车主：</dt>
                                        <dd class="col-sm-8"><asp:Label ID="lblOwnerName" runat="server"></asp:Label></dd>
                                        
                                        <dt class="col-sm-4">车辆信息：</dt>
                                        <dd class="col-sm-8"><asp:Label ID="lblCarInfo" runat="server"></asp:Label></dd>
                                    </dl>
                                </div>
                                <div class="col-md-6">
                                    <dl class="row">
                                        <dt class="col-sm-4">服务项目：</dt>
                                        <dd class="col-sm-8"><asp:Label ID="lblServiceName" runat="server"></asp:Label></dd>
                                        
                                        <dt class="col-sm-4">工时费：</dt>
                                        <dd class="col-sm-8"><asp:Label ID="lblLaborCost" runat="server"></asp:Label></dd>
                                        
                                        <dt class="col-sm-4">零件费：</dt>
                                        <dd class="col-sm-8"><asp:Label ID="lblPartsCost" runat="server"></asp:Label></dd>
                                        
                                        <dt class="col-sm-4">总费用：</dt>
                                        <dd class="col-sm-8"><asp:Label ID="lblTotalCost" runat="server" CssClass="text-danger font-weight-bold"></asp:Label></dd>
                                        
                                        <dt class="col-sm-4">技师：</dt>
                                        <dd class="col-sm-8"><asp:Label ID="lblTechnicianName" runat="server"></asp:Label></dd>
                                    </dl>
                                </div>
                            </div>
                            
                            <hr />
                            
                            <div class="row">
                                <div class="col-md-12">
                                    <h5>诊断详情</h5>
                                    <div class="p-3 bg-light rounded">
                                        <asp:Label ID="lblDiagnosisDetails" runat="server"></asp:Label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <h5>更换零件</h5>
                                    <div class="p-3 bg-light rounded">
                                        <asp:Label ID="lblPartsReplaced" runat="server"></asp:Label>
                                    </div>
                                </div>
                            </div>

                            <asp:Panel ID="pnlReviewDetails" runat="server" Visible="false" CssClass="mt-4">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header bg-warning">
                                                <h5 class="mb-0"><i class="fas fa-star"></i> 客户评价</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="text-warning mb-2">
                                                            <asp:Literal ID="litRatingStars" runat="server"></asp:Literal>
                                                        </div>
                                                        <div class="h4">
                                                            <asp:Label ID="lblRating" runat="server"></asp:Label> <small>/ 5</small>
                                                        </div>
                                                        <small class="text-muted">
                                                            评价日期：<asp:Label ID="lblReviewDate" runat="server"></asp:Label>
                                                        </small>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <blockquote class="blockquote">
                                                            <p class="mb-0">
                                                                <asp:Label ID="lblReviewComments" runat="server"></asp:Label>
                                                            </p>
                                                        </blockquote>
                                                    </div>
                                                    
                                                    <asp:Panel ID="pnlReviewPhotos" runat="server" Visible="false" CssClass="mt-3">
                                                        <div class="row">
                                                            <div class="col-12">
                                                                <h6>评价照片</h6>
                                                                <div class="review-photos-container row">
                                                                    <asp:Repeater ID="rptReviewPhotos" runat="server">
                                                                        <ItemTemplate>
                                                                            <div class="col-md-4 mb-2">
                                                                                <a href='<%# ResolveUrl(Eval("FilePath").ToString()) %>' target="_blank">
                                                                                    <img src='<%# ResolveUrl(Eval("FilePath").ToString()) %>' class="img-thumbnail" style="max-height:200px;" />
                                                                                </a>
                                                                            </div>
                                                                        </ItemTemplate>
                                                                    </asp:Repeater>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </asp:Panel>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </asp:Panel>
                            
                            <div class="text-center mt-4">
                                <asp:Button ID="btnBack" runat="server" Text="返回列表" CssClass="btn btn-secondary" OnClick="btnBack_Click" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <div class="row mt-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
            </div>
        </div>
    </div>
</asp:Content> 