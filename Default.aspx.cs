﻿using System;
using System.Web.UI;

public partial class _Default : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            // 绑定控件数据源，以使数据绑定表达式生效
            DataBind();
            
            // 如果用户已登录，根据用户类型重定向到相应的主页
            if (User.Identity.IsAuthenticated && Session["UserType"] != null)
            {
                string userType = Session["UserType"].ToString();
                
                switch (userType)
                {
                    case "CarOwner":
                        hlCarOwner.NavigateUrl = "~/CarOwner/Dashboard.aspx";
                        hlCarOwner.Text = "进入车主中心";
                        hlRepairShop.Visible = false;
                        break;
                    case "RepairShop":
                        hlRepairShop.NavigateUrl = "~/RepairShop/Dashboard.aspx";
                        hlRepairShop.Text = "进入维修店管理";
                        hlCarOwner.Visible = false;
                        break;
                    case "Admin":
                        Response.Redirect("~/Admin/Dashboard.aspx");
                        break;
                }
            }
            else
            {
                // 未登录用户显示注册链接
                hlCarOwner.NavigateUrl = "~/Register.aspx?type=owner";
                hlCarOwner.Text = "注册为车主";
                hlRepairShop.NavigateUrl = "~/Register.aspx?type=shop";
                hlRepairShop.Text = "注册为维修店";
            }
        }
    }

    protected void btnRegister_Click(object sender, EventArgs e)
    {
        Response.Redirect("~/Register.aspx");
    }

    protected void btnLogin_Click(object sender, EventArgs e)
    {
        Response.Redirect("~/Login.aspx");
    }
}