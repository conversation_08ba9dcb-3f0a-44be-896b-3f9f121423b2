using System;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class ForgotPassword
{
    /// <summary>
    /// 第一步面板
    /// </summary>
    protected global::System.Web.UI.WebControls.Panel pnlStep1;
    
    /// <summary>
    /// 用户名输入框
    /// </summary>
    protected global::System.Web.UI.WebControls.TextBox txtUsername;
    
    /// <summary>
    /// 用户名验证控件
    /// </summary>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvUsername;
    
    /// <summary>
    /// 手机号码输入框
    /// </summary>
    protected global::System.Web.UI.WebControls.TextBox txtPhoneNumber;
    
    /// <summary>
    /// 手机号码验证控件
    /// </summary>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvPhoneNumber;
    
    /// <summary>
    /// 手机号码格式验证控件
    /// </summary>
    protected global::System.Web.UI.WebControls.RegularExpressionValidator revPhoneNumber;
    
    /// <summary>
    /// 第一步消息标签
    /// </summary>
    protected global::System.Web.UI.WebControls.Label lblMessage;
    
    /// <summary>
    /// 下一步按钮
    /// </summary>
    protected global::System.Web.UI.WebControls.Button btnNext;
    
    /// <summary>
    /// 第二步面板
    /// </summary>
    protected global::System.Web.UI.WebControls.Panel pnlStep2;
    
    /// <summary>
    /// 验证码输入框
    /// </summary>
    protected global::System.Web.UI.WebControls.TextBox txtVerificationCode;
    
    /// <summary>
    /// 发送验证码按钮
    /// </summary>
    protected global::System.Web.UI.WebControls.Button btnSendCode;
    
    /// <summary>
    /// 验证码验证控件
    /// </summary>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvVerificationCode;
    
    /// <summary>
    /// 验证码消息标签
    /// </summary>
    protected global::System.Web.UI.WebControls.Label lblVerifyMessage;
    
    /// <summary>
    /// 验证按钮
    /// </summary>
    protected global::System.Web.UI.WebControls.Button btnVerifyCode;
    
    /// <summary>
    /// 返回按钮
    /// </summary>
    protected global::System.Web.UI.WebControls.Button btnBack;
    
    /// <summary>
    /// 第三步面板
    /// </summary>
    protected global::System.Web.UI.WebControls.Panel pnlStep3;
    
    /// <summary>
    /// 新密码输入框
    /// </summary>
    protected global::System.Web.UI.WebControls.TextBox txtNewPassword;
    
    /// <summary>
    /// 新密码验证控件
    /// </summary>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvNewPassword;
    
    /// <summary>
    /// 确认密码输入框
    /// </summary>
    protected global::System.Web.UI.WebControls.TextBox txtConfirmPassword;
    
    /// <summary>
    /// 确认密码验证控件
    /// </summary>
    protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvConfirmPassword;
    
    /// <summary>
    /// 密码比较验证控件
    /// </summary>
    protected global::System.Web.UI.WebControls.CompareValidator cvPassword;
    
    /// <summary>
    /// 最小密码长度标签
    /// </summary>
    protected global::System.Web.UI.WebControls.Literal minLength;
    
    /// <summary>
    /// 大写字母要求
    /// </summary>
    protected global::System.Web.UI.HtmlControls.HtmlGenericControl requireUppercase;
    
    /// <summary>
    /// 数字要求
    /// </summary>
    protected global::System.Web.UI.HtmlControls.HtmlGenericControl requireDigit;
    
    /// <summary>
    /// 特殊字符要求
    /// </summary>
    protected global::System.Web.UI.HtmlControls.HtmlGenericControl requireSpecialChar;
    
    /// <summary>
    /// 重置密码消息标签
    /// </summary>
    protected global::System.Web.UI.WebControls.Label lblResetMessage;
    
    /// <summary>
    /// 重置密码按钮
    /// </summary>
    protected global::System.Web.UI.WebControls.Button btnResetPassword;
    
    /// <summary>
    /// 成功面板
    /// </summary>
    protected global::System.Web.UI.WebControls.Panel pnlSuccess;
    
    /// <summary>
    /// 返回登录按钮
    /// </summary>
    protected global::System.Web.UI.WebControls.Button btnBackToLogin;
} 