using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class CarOwner_Appointments : System.Web.UI.Page
{
    private int userID;
    private int ownerID;
    private int selectedAppointmentID;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户登录和权限
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "CarOwner")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        ownerID = CarManager.GetOwnerIDByUserID(userID);

        if (ownerID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            // 显示成功消息（如果有）
            if (Session["AppointmentSuccess"] != null)
            {
                lblMessage.Text = Session["AppointmentSuccess"].ToString();
                lblMessage.CssClass = "alert alert-success";
                lblMessage.Visible = true;
                Session.Remove("AppointmentSuccess");
            }

            // 检查是否有特定预约ID要显示
            if (Request.QueryString["id"] != null)
            {
                int appointmentID;
                if (int.TryParse(Request.QueryString["id"], out appointmentID))
                {
                    ShowAppointmentDetails(appointmentID);
                    return;
                }
            }

            // 默认加载活跃预约
            LoadActiveAppointments();
        }
    }

    #region 预约列表

    /// <summary>
    /// 加载未完成预约
    /// </summary>
    private void LoadActiveAppointments()
    {
        lbtnActiveAppointments.CssClass = "nav-link active";
        lbtnAllAppointments.CssClass = "nav-link";

        DataTable appointmentsTable = AppointmentManager.GetActiveAppointmentsByOwnerID(ownerID);
        gvAppointments.DataSource = appointmentsTable;
        gvAppointments.DataBind();
    }

    /// <summary>
    /// 加载所有预约
    /// </summary>
    private void LoadAllAppointments()
    {
        lbtnAllAppointments.CssClass = "nav-link active";
        lbtnActiveAppointments.CssClass = "nav-link";

        DataTable appointmentsTable = AppointmentManager.GetAppointmentsByOwnerID(ownerID);
        gvAppointments.DataSource = appointmentsTable;
        gvAppointments.DataBind();
    }

    /// <summary>
    /// 未完成预约标签点击事件
    /// </summary>
    protected void lbtnActiveAppointments_Click(object sender, EventArgs e)
    {
        LoadActiveAppointments();
    }

    /// <summary>
    /// 所有预约标签点击事件
    /// </summary>
    protected void lbtnAllAppointments_Click(object sender, EventArgs e)
    {
        LoadAllAppointments();
    }

    /// <summary>
    /// GridView行命令事件
    /// </summary>
    protected void gvAppointments_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        int appointmentID = Convert.ToInt32(e.CommandArgument);

        if (e.CommandName == "ViewAppointment")
        {
            ShowAppointmentDetails(appointmentID);
        }
        else if (e.CommandName == "CancelAppointment")
        {
            CancelAppointment(appointmentID);
        }
        else if (e.CommandName == "GoToPayment")
        {
            // 重定向到支付验收页面
            Response.Redirect($"~/CarOwner/PaymentSimulation.aspx?appointmentID={appointmentID}");
        }
    }

    /// <summary>
    /// 获取状态文本
    /// </summary>
    public string GetStatusText(string status)
    {
        switch (status)
        {
            case "Pending":
                return "待确认";
            case "Confirmed":
                return "已确认";
            case "Completed":
                return "已完成";
            case "Cancelled":
                return "已取消";
            default:
                return status;
        }
    }

    /// <summary>
    /// 获取状态CSS类
    /// </summary>
    public string GetStatusCssClass(string status)
    {
        switch (status)
        {
            case "Pending":
                return "badge badge-warning";
            case "Confirmed":
                return "badge badge-success";
            case "Completed":
                return "badge badge-info";
            case "Cancelled":
                return "badge badge-danger";
            default:
                return "badge badge-secondary";
        }
    }

    /// <summary>
    /// 判断是否可以取消预约
    /// </summary>
    public bool CanCancelAppointment(string status)
    {
        return status == "Pending" || status == "Confirmed";
    }

    #endregion

    #region 查看预约详情

    /// <summary>
    /// 显示预约详情
    /// </summary>
    private void ShowAppointmentDetails(int appointmentID)
    {
        DataTable appointmentTable = AppointmentManager.GetAppointmentByID(appointmentID);
        if (appointmentTable != null && appointmentTable.Rows.Count > 0)
        {
            DataRow row = appointmentTable.Rows[0];

            // 保存选中的预约ID - 使用多种方式确保ID不丢失
            selectedAppointmentID = appointmentID;
            ViewState["SelectedAppointmentID"] = appointmentID;
            Session["CurrentAppointmentID"] = appointmentID;
            hdnCurrentAppointmentID.Value = appointmentID.ToString();

            System.Diagnostics.Debug.WriteLine($"ShowAppointmentDetails - 设置预约ID: {appointmentID}");

            // 显示预约信息
            lblAppointmentDate.Text = Convert.ToDateTime(row["AppointmentDate"]).ToString("yyyy-MM-dd HH:mm");
            lblStatus.Text = GetStatusText(row["Status"].ToString());
            lblStatus.CssClass = GetStatusCssClass(row["Status"].ToString());
            lblDescription.Text = row["Description"] != DBNull.Value ? row["Description"].ToString() : "无";

            // 显示车辆信息
            lblCarMakeModel.Text = row["Make"].ToString() + " " + row["Model"].ToString();
            lblLicensePlate.Text = row["LicensePlate"].ToString();

            // 显示维修店信息
            lblShopName.Text = row["ShopName"].ToString();
            lblShopAddress.Text = row["Address"].ToString();

            // 显示服务信息
            lblServiceName.Text = row["ServiceName"].ToString();
            lblEstimatedTime.Text = row["EstimatedTime"] != DBNull.Value ? row["EstimatedTime"].ToString() + " 分钟" : "未知";
            lblBasePrice.Text = row["BasePrice"] != DBNull.Value ? string.Format("¥{0:N2}", row["BasePrice"]) : "未设定";

            // 设置取消按钮可见性
            btnCancelAppointment.Visible = CanCancelAppointment(row["Status"].ToString());

            // 重置消息
            lblMessage.Visible = false;

            // 切换面板
            pnlAppointments.Visible = false;
            pnlNewAppointment.Visible = false;
            pnlAppointmentDetails.Visible = true;
        }
        else
        {
            lblMessage.Text = "未找到预约信息。";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
        }
    }

    /// <summary>
    /// 取消预约按钮点击事件
    /// </summary>
    protected void btnCancelAppointment_Click(object sender, EventArgs e)
    {
        try
        {
            // 调试输出所有ID源
            DebugAppointmentID();

            // 尝试获取预约ID
            int appointmentID = 0;

            // 首先尝试从ViewState获取
            if (ViewState["SelectedAppointmentID"] != null)
            {
                appointmentID = Convert.ToInt32(ViewState["SelectedAppointmentID"]);
                System.Diagnostics.Debug.WriteLine($"从ViewState获取到预约ID: {appointmentID}");
            }

            // 如果ViewState没有值，使用成员变量
            if (appointmentID <= 0)
            {
                appointmentID = selectedAppointmentID;
                System.Diagnostics.Debug.WriteLine($"从成员变量获取到预约ID: {appointmentID}");
            }

            // 如果还是没有有效ID，尝试从隐藏字段获取
            if (appointmentID <= 0 && !string.IsNullOrEmpty(hdnCurrentAppointmentID.Value))
            {
                if (int.TryParse(hdnCurrentAppointmentID.Value, out appointmentID))
                {
                    System.Diagnostics.Debug.WriteLine($"从隐藏字段获取到预约ID: {appointmentID}");
                }
            }

            // 如果还是没有有效ID，尝试从Session获取
            if (appointmentID <= 0 && Session["CurrentAppointmentID"] != null)
            {
                appointmentID = Convert.ToInt32(Session["CurrentAppointmentID"]);
                System.Diagnostics.Debug.WriteLine($"从Session获取到预约ID: {appointmentID}");
            }

            // 最后尝试从查询字符串获取
            if (appointmentID <= 0 && Request.QueryString["id"] != null)
            {
                if (int.TryParse(Request.QueryString["id"], out appointmentID))
                {
                    System.Diagnostics.Debug.WriteLine($"从查询字符串获取到预约ID: {appointmentID}");
                }
            }

            // 检查最终获取的ID是否有效
            if (appointmentID <= 0)
            {
                lblMessage.Text = "无法取消预约，预约ID无效。请返回列表重新选择预约。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                System.Diagnostics.Debug.WriteLine("取消预约失败 - 无效的预约ID");

                // 强制返回列表页面
                pnlAppointmentDetails.Visible = false;
                pnlAppointments.Visible = true;
                LoadActiveAppointments();
                return;
            }

            // 使用找到的ID取消预约
            System.Diagnostics.Debug.WriteLine($"最终使用的预约ID: {appointmentID}");
            CancelAppointment(appointmentID);
        }
        catch (Exception ex)
        {
            // 捕获并记录任何异常
            System.Diagnostics.Debug.WriteLine($"取消预约按钮点击时出错: {ex.Message}");
            lblMessage.Text = $"处理请求时发生错误: {ex.Message}";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
        }
    }

    /// <summary>
    /// 返回列表按钮点击事件
    /// </summary>
    protected void btnBackToList_Click(object sender, EventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("返回列表按钮点击 - 清理预约ID");
            
            // 清理预约ID
            selectedAppointmentID = 0;
            ViewState["SelectedAppointmentID"] = null;
            
            // 隐藏消息
            lblMessage.Visible = false;
            
            // 切换面板
            pnlAppointmentDetails.Visible = false;
            pnlAppointments.Visible = true;
            
            System.Diagnostics.Debug.WriteLine("成功返回预约列表视图");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"返回列表时出错: {ex.Message}");
            
            // 即使出错也尝试回到列表视图
            pnlAppointmentDetails.Visible = false;
            pnlAppointments.Visible = true;
        }
    }

    /// <summary>
    /// 取消预约
    /// </summary>
    private void CancelAppointment(int appointmentID)
    {
        try
        {
            // 记录日志
            System.Diagnostics.Debug.WriteLine("执行取消预约，ID: " + appointmentID);
            
            // 验证预约ID
            if (appointmentID <= 0)
            {
                lblMessage.Text = "预约ID无效，无法取消。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }
            
            // 首先检查预约是否属于当前车主，防止越权操作
            DataTable appointmentTable = AppointmentManager.GetAppointmentByID(appointmentID);
            if (appointmentTable == null || appointmentTable.Rows.Count == 0)
            {
                lblMessage.Text = "找不到预约信息，无法取消。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            int appointmentOwnerID = Convert.ToInt32(appointmentTable.Rows[0]["OwnerID"]);
            if (appointmentOwnerID != ownerID)
            {
                lblMessage.Text = "您无权取消此预约。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                return;
            }

            // 执行取消操作
            System.Diagnostics.Debug.WriteLine($"开始执行取消预约操作 - 预约ID: {appointmentID}, 车主ID: {ownerID}");
            bool success = AppointmentManager.CancelAppointment(appointmentID);
            System.Diagnostics.Debug.WriteLine($"取消预约操作结果: {success}");

            if (success)
            {
                lblMessage.Text = "预约已成功取消。";
                lblMessage.CssClass = "alert alert-success";
                lblMessage.Visible = true;

                // 清理所有保存的预约ID
                selectedAppointmentID = 0;
                ViewState["SelectedAppointmentID"] = null;
                Session["CurrentAppointmentID"] = null;
                hdnCurrentAppointmentID.Value = "";

                // 返回预约列表
                pnlAppointmentDetails.Visible = false;
                pnlAppointments.Visible = true;
                // 重新加载预约数据
                LoadActiveAppointments();

                System.Diagnostics.Debug.WriteLine("预约取消成功，已返回列表页面");
            }
            else
            {
                lblMessage.Text = "取消预约失败，可能预约已经完成或已被取消。";
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
                System.Diagnostics.Debug.WriteLine("预约取消失败");
            }
        }
        catch (Exception ex)
        {
            lblMessage.Text = "取消预约时发生错误：" + ex.Message;
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
        }
    }

    #endregion

    #region 创建新预约

    /// <summary>
    /// 新建预约按钮点击事件
    /// </summary>
    protected void btnNewAppointment_Click(object sender, EventArgs e)
    {
        // 重置消息
        lblMessage.Text = string.Empty;
        lblMessage.Visible = false;

        // 加载车辆下拉菜单
        LoadCars();

        // 加载维修店下拉菜单
        LoadShops();

        // 初始化控件
        DateTime now = DateTime.Now;
        txtAppointmentDate.Text = now.ToString("yyyy-MM-ddTHH:mm");
        txtAppointmentDate.Attributes["min"] = now.ToString("yyyy-MM-ddTHH:mm");
        txtDescription.Text = string.Empty;

        // 执行数据绑定
        Page.DataBind();

        // 切换显示面板
        pnlAppointments.Visible = false;
        pnlAppointmentDetails.Visible = false;
        pnlNewAppointment.Visible = true;
    }

    /// <summary>
    /// 加载车辆列表
    /// </summary>
    private void LoadCars()
    {
        DataTable carsTable = CarManager.GetCarsByOwnerID(ownerID);
        ddlCar.Items.Clear();
        ddlCar.Items.Add(new ListItem("-- 请选择车辆 --", "0"));

        if (carsTable != null && carsTable.Rows.Count > 0)
        {
            foreach (DataRow row in carsTable.Rows)
            {
                string carInfo = row["Make"].ToString() + " " + row["Model"].ToString() + " (" + row["LicensePlate"].ToString() + ")";
                ddlCar.Items.Add(new ListItem(carInfo, row["CarID"].ToString()));
            }
        }
    }

    /// <summary>
    /// 加载维修店列表
    /// </summary>
    private void LoadShops()
    {
        DataTable shopsTable = AppointmentManager.GetAllRepairShops();
        ddlShop.Items.Clear();
        ddlShop.Items.Add(new ListItem("-- 请选择维修店 --", "0"));

        if (shopsTable != null && shopsTable.Rows.Count > 0)
        {
            foreach (DataRow row in shopsTable.Rows)
            {
                string shopInfo = row["ShopName"].ToString();
                if (row["Rating"] != DBNull.Value)
                {
                    shopInfo += " (评分: " + row["Rating"].ToString() + ")";
                }
                ddlShop.Items.Add(new ListItem(shopInfo, row["ShopID"].ToString()));
            }
        }
    }

    /// <summary>
    /// 加载服务列表
    /// </summary>
    private void LoadServices(int shopID)
    {
        DataTable servicesTable = AppointmentManager.GetServicesByShopID(shopID);
        ddlService.Items.Clear();
        ddlService.Items.Add(new ListItem("-- 请选择服务项目 --", "0"));

        if (servicesTable != null && servicesTable.Rows.Count > 0)
        {
            string currentCategory = string.Empty;
            ListItemCollection items = new ListItemCollection();

            foreach (DataRow row in servicesTable.Rows)
            {
                string category = row["CategoryName"].ToString();
                string serviceName = row["ServiceName"].ToString();
                string basePrice = row["BasePrice"] != DBNull.Value ? string.Format("¥{0:N2}", row["BasePrice"]) : "未定价";
                
                ListItem item = new ListItem(serviceName + " (" + basePrice + ")", row["ServiceID"].ToString());
                
                if (category != currentCategory)
                {
                    if (!string.IsNullOrEmpty(currentCategory))
                    {
                        ListItem separator = new ListItem("──────────────", "");
                        separator.Attributes.Add("disabled", "disabled");
                        items.Add(separator);
                    }
                    
                    ListItem categoryHeader = new ListItem(category, "");
                    categoryHeader.Attributes.Add("disabled", "disabled");
                    categoryHeader.Attributes.Add("style", "font-weight:bold;");
                    items.Add(categoryHeader);
                    
                    currentCategory = category;
                }
                
                items.Add(item);
            }
            
            foreach (ListItem item in items)
            {
                ddlService.Items.Add(item);
            }
        }
    }

    /// <summary>
    /// 车辆下拉框选择改变事件
    /// </summary>
    protected void ddlCar_SelectedIndexChanged(object sender, EventArgs e)
    {
        // 可以在这里添加额外的业务逻辑，例如根据选中的车辆自动推荐服务
    }

    /// <summary>
    /// 维修店下拉框选择改变事件
    /// </summary>
    protected void ddlShop_SelectedIndexChanged(object sender, EventArgs e)
    {
        int shopID;
        if (int.TryParse(ddlShop.SelectedValue, out shopID) && shopID > 0)
        {
            // 加载该维修店提供的服务
            LoadServices(shopID);
        }
        else
        {
            // 清空服务列表
            ddlService.Items.Clear();
            ddlService.Items.Add(new ListItem("-- 请先选择维修店 --", "0"));
        }
    }

    /// <summary>
    /// 预约时间验证
    /// </summary>
    protected void cvAppointmentDate_ServerValidate(object source, ServerValidateEventArgs args)
    {
        DateTime appointmentDate;
        if (DateTime.TryParse(args.Value, out appointmentDate))
        {
            args.IsValid = appointmentDate > DateTime.Now;
        }
        else
        {
            args.IsValid = false;
        }
    }

    /// <summary>
    /// 提交新预约按钮点击事件
    /// </summary>
    protected void btnSubmitAppointment_Click(object sender, EventArgs e)
    {
        if (!Page.IsValid)
        {
            return;
        }

        // 检查是否选择了车辆
        if (ddlCar.SelectedIndex == 0)
        {
            lblMessage.Text = "请选择车辆。";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
            return;
        }

        // 检查是否选择了维修店
        if (ddlShop.SelectedIndex == 0)
        {
            lblMessage.Text = "请选择维修店。";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
            return;
        }

        // 检查是否选择了服务项目
        if (ddlService.SelectedIndex == 0)
        {
            lblMessage.Text = "请选择服务项目。";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
            return;
        }

        // 解析预约时间
        DateTime appointmentDate;
        if (!DateTime.TryParse(txtAppointmentDate.Text, out appointmentDate))
        {
            lblMessage.Text = "预约时间格式无效。";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
            return;
        }

        // 再次验证预约时间是否是未来时间
        if (appointmentDate <= DateTime.Now)
        {
            lblMessage.Text = "预约时间必须是将来的时间。";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
            return;
        }

        // 创建预约
        int carID = Convert.ToInt32(ddlCar.SelectedValue);
        int shopID = Convert.ToInt32(ddlShop.SelectedValue);
        int serviceID = Convert.ToInt32(ddlService.SelectedValue);
        string description = txtDescription.Text.Trim();

        int appointmentID = AppointmentManager.AddAppointment(carID, shopID, serviceID, appointmentDate, description);
        if (appointmentID > 0)
        {
            lblMessage.Text = "预约创建成功！";
            lblMessage.CssClass = "alert alert-success";
            lblMessage.Visible = true;

            // 返回预约列表并重新加载
            pnlNewAppointment.Visible = false;
            pnlAppointments.Visible = true;
            LoadActiveAppointments();
        }
        else
        {
            lblMessage.Text = "预约创建失败，请稍后再试。";
            lblMessage.CssClass = "alert alert-danger";
            lblMessage.Visible = true;
        }
    }

    /// <summary>
    /// 取消新预约按钮点击事件
    /// </summary>
    protected void btnCancelNewAppointment_Click(object sender, EventArgs e)
    {
        // 清空错误信息
        lblMessage.Text = string.Empty;
        lblMessage.Visible = false;

        // 返回预约列表
        pnlNewAppointment.Visible = false;
        pnlAppointments.Visible = true;
    }

    #endregion

    /// <summary>
    /// 返回控制台按钮点击事件
    /// </summary>
    protected void btnBackToDashboard_Click(object sender, EventArgs e)
    {
        Response.Redirect("~/CarOwner/Dashboard.aspx");
    }

    /// <summary>
    /// 调试方法：输出所有可能的预约ID源
    /// </summary>
    private void DebugAppointmentID()
    {
        System.Diagnostics.Debug.WriteLine("=== 预约ID调试信息 ===");
        System.Diagnostics.Debug.WriteLine($"成员变量 selectedAppointmentID: {selectedAppointmentID}");
        System.Diagnostics.Debug.WriteLine($"ViewState['SelectedAppointmentID']: {ViewState["SelectedAppointmentID"] ?? "null"}");
        System.Diagnostics.Debug.WriteLine($"Session['CurrentAppointmentID']: {Session["CurrentAppointmentID"] ?? "null"}");
        System.Diagnostics.Debug.WriteLine($"hdnCurrentAppointmentID.Value: {hdnCurrentAppointmentID?.Value ?? "null"}");
        System.Diagnostics.Debug.WriteLine($"Request.QueryString['id']: {Request.QueryString["id"] ?? "null"}");
        System.Diagnostics.Debug.WriteLine("========================");
    }
}
