﻿<%@ Page Title="登录" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="Login" Codebehind="Login.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .login-container {
            max-width: 500px;
            margin: 60px auto;
        }
        
        .login-card {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border: none;
        }
        
        .login-header {
            padding: 25px;
            text-align: center;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        }
        
        .login-title {
            margin: 0;
            font-size: 1.75rem;
            font-weight: 700;
        }
        
        .login-body {
            padding: 40px;
        }
        
        .login-icon {
            font-size: 4rem;
            color: white;
            margin-bottom: 15px;
        }
        
        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .form-control {
            height: auto;
            padding: 12px;
        }
        
        .btn-login {
            padding: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
            font-size: 1rem;
            margin-top: 10px;
        }
        
        .login-footer {
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            border-top: 1px solid #eee;
        }
        
        .login-links a {
            color: var(--primary-color);
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .login-links a:hover {
            color: var(--primary-dark);
            text-decoration: none;
        }
        
        .social-login {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .social-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            margin: 0 5px;
            color: white;
            transition: all 0.3s ease;
        }
        
        .social-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .social-btn-wechat {
            background-color: #7BB32E;
        }
        
        .social-btn-qq {
            background-color: #12B7F5;
        }
        
        .social-btn-weibo {
            background-color: #E6162D;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="login-container" data-aos="fade-up">
        <div class="card login-card">
            <div class="login-header">
                <i class="fas fa-user-circle login-icon"></i>
                <h3 class="login-title text-white">用户登录</h3>
                <p class="text-white-50 mb-0">请使用您的账号登录系统</p>
            </div>
            
            <div class="login-body">
                <div class="form-group">
                    <label for="txtUsername"><i class="fas fa-user mr-2"></i>用户名</label>
                    <asp:TextBox ID="txtUsername" runat="server" CssClass="form-control" placeholder="请输入用户名"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvUsername" runat="server" ControlToValidate="txtUsername"
                        ErrorMessage="用户名不能为空" Display="Dynamic" CssClass="text-danger"></asp:RequiredFieldValidator>
                </div>
                
                <div class="form-group">
                    <label for="txtPassword"><i class="fas fa-lock mr-2"></i>密码</label>
                    <div class="input-group">
                        <asp:TextBox ID="txtPassword" runat="server" TextMode="Password" CssClass="form-control" placeholder="请输入密码"></asp:TextBox>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <asp:RequiredFieldValidator ID="rfvPassword" runat="server" ControlToValidate="txtPassword"
                        ErrorMessage="密码不能为空" Display="Dynamic" CssClass="text-danger"></asp:RequiredFieldValidator>
                </div>
                
                <div class="form-group">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="rememberMe">
                        <label class="custom-control-label" for="rememberMe">记住我的登录状态</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
                </div>
                
                <div class="form-group">
                    <asp:Button ID="btnLogin" runat="server" Text="登 录" CssClass="btn btn-primary btn-block btn-login" OnClick="btnLogin_Click" />
                </div>
                
                <div class="social-login text-center">
                    <p class="text-muted mb-3">其他登录方式</p>
                    <a href="#" class="social-btn social-btn-wechat" title="微信登录">
                        <i class="fab fa-weixin"></i>
                    </a>
                    <a href="#" class="social-btn social-btn-qq" title="QQ登录">
                        <i class="fab fa-qq"></i>
                    </a>
                    <a href="#" class="social-btn social-btn-weibo" title="微博登录">
                        <i class="fab fa-weibo"></i>
                    </a>
                </div>
            </div>
            
            <div class="login-footer">
                <div class="login-links">
                    <p class="mb-1">还没有账号？ <a href="Register.aspx"><i class="fas fa-user-plus mr-1"></i>立即注册</a></p>
                    <p class="mb-0"><a href="ForgotPassword.aspx"><i class="fas fa-key mr-1"></i>忘记密码？</a></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('<%= txtPassword.ClientID %>');
            const eyeIcon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        });
    </script>
</asp:Content>
