<%@ Page Title="评论管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="Admin_ReviewManagement" Codebehind="ReviewManagement.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2><i class="fas fa-comments"></i> 评论管理</h2>
                <hr />
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">评论列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-row mb-3">
                            <div class="col-md-4">
                                <asp:DropDownList ID="ddlShopFilter" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlShopFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="0" Text="所有维修店" Selected="True"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-4">
                                <asp:DropDownList ID="ddlRatingFilter" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlRatingFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="0" Text="所有评分" Selected="True"></asp:ListItem>
                                    <asp:ListItem Value="5" Text="5星"></asp:ListItem>
                                    <asp:ListItem Value="4" Text="4星"></asp:ListItem>
                                    <asp:ListItem Value="3" Text="3星"></asp:ListItem>
                                    <asp:ListItem Value="2" Text="2星"></asp:ListItem>
                                    <asp:ListItem Value="1" Text="1星"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-4">
                                <div class="input-group">
                                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="搜索评论内容"></asp:TextBox>
                                    <div class="input-group-append">
                                        <asp:Button ID="btnSearch" runat="server" Text="搜索" CssClass="btn btn-outline-secondary" OnClick="btnSearch_Click" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <asp:GridView ID="gvReviews" runat="server" AutoGenerateColumns="False" CssClass="table table-striped table-hover"
                            DataKeyNames="ReviewID" OnRowCommand="gvReviews_RowCommand" OnRowDataBound="gvReviews_RowDataBound"
                            EmptyDataText="没有找到符合条件的评论" AllowPaging="True" PageSize="10" OnPageIndexChanging="gvReviews_PageIndexChanging">
                            <Columns>
                                <asp:BoundField DataField="ReviewID" HeaderText="ID" />
                                <asp:TemplateField HeaderText="评分">
                                    <ItemTemplate>
                                        <div class="text-warning">
                                            <asp:Literal ID="litRatingStars" runat="server"></asp:Literal>
                                        </div>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:BoundField DataField="Comments" HeaderText="评论内容" />
                                <asp:BoundField DataField="Username" HeaderText="用户名" />
                                <asp:BoundField DataField="ShopName" HeaderText="维修店" />
                                <asp:BoundField DataField="ReviewDate" HeaderText="评论日期" DataFormatString="{0:yyyy-MM-dd}" />
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="btnDelete" runat="server" CssClass="btn btn-sm btn-danger" 
                                            CommandName="DeleteReview" CommandArgument='<%# Eval("ReviewID") %>'
                                            OnClientClick="return confirm('确定要删除此评论吗？此操作不可恢复。');">
                                            <i class="fas fa-trash-alt"></i> 删除
                                        </asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle CssClass="pagination-container" HorizontalAlign="Center" />
                            <PagerSettings Mode="NumericFirstLast" FirstPageText="首页" LastPageText="末页" />
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="alert alert-success">
                    <asp:Literal ID="litMessage" runat="server"></asp:Literal>
                </asp:Panel>
                <div class="mt-3">
                    <asp:Button ID="btnBackToDashboard" runat="server" Text="返回控制台" CssClass="btn btn-secondary" OnClick="btnBackToDashboard_Click" />
                </div>
            </div>
        </div>
    </div>
    
    <style>
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        
        .pagination-container a,
        .pagination-container span {
            padding: 6px 12px;
            margin: 0 4px;
            border: 1px solid #dee2e6;
            color: #007bff;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .pagination-container span {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
    </style>
</asp:Content> 