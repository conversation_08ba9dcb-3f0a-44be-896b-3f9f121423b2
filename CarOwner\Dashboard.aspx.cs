using System;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;

public partial class CarOwner_Dashboard : System.Web.UI.Page
{
    private int userID;
    private int ownerID;

    protected void Page_Load(object sender, EventArgs e)
    {
        // 检查用户是否登录
        if (!User.Identity.IsAuthenticated)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        // 获取当前用户信息
        if (Session["UserID"] == null || Session["UserType"] == null || Session["UserType"].ToString() != "CarOwner")
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        userID = Convert.ToInt32(Session["UserID"]);
        
        // 获取车主ID
        ownerID = CarManager.GetOwnerIDByUserID(userID);
        if (ownerID == -1)
        {
            Response.Redirect("~/Login.aspx");
            return;
        }

        if (!IsPostBack)
        {
            // 加载数据
            LoadCarCount();
            LoadAppointmentCount();
            LoadServiceCount();
            LoadUpcomingAppointments();
            LoadSystemNotifications();
        }
    }

    /// <summary>
    /// 加载系统通知
    /// </summary>
    private void LoadSystemNotifications()
    {
        DataTable notifications = AdminManager.GetActiveSystemNotifications();
        rptNotifications.DataSource = notifications;
        rptNotifications.DataBind();
    }

    /// <summary>
    /// 加载车辆数量
    /// </summary>
    private void LoadCarCount()
    {
        DataTable carsTable = CarManager.GetCarsByOwnerID(ownerID);
        int carCount = carsTable?.Rows.Count ?? 0;
        lblCarCount.Text = string.Format("您有 {0} 辆车", carCount);
    }

    /// <summary>
    /// 加载预约数量
    /// </summary>
    private void LoadAppointmentCount()
    {
        // 获取未完成的预约数量
        string query = @"SELECT COUNT(1) FROM Appointments a
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        WHERE c.OwnerID = @OwnerID AND a.Status IN ('Pending', 'Confirmed')";
        SqlParameter parameter = new SqlParameter("@OwnerID", ownerID);
        object result = DatabaseHelper.ExecuteScalar(query, parameter);
        int appointmentCount = (result != null && result != DBNull.Value) ? Convert.ToInt32(result) : 0;
        lblAppointmentCount.Text = string.Format("待处理预约 {0} 个", appointmentCount);
    }

    /// <summary>
    /// 加载维修记录数量
    /// </summary>
    private void LoadServiceCount()
    {
        // 获取已完成的维修记录数量
        string query = @"SELECT COUNT(1) FROM ServiceRecords sr
                        INNER JOIN Appointments a ON sr.AppointmentID = a.AppointmentID
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        WHERE c.OwnerID = @OwnerID";
        SqlParameter parameter = new SqlParameter("@OwnerID", ownerID);
        object result = DatabaseHelper.ExecuteScalar(query, parameter);
        int serviceCount = (result != null && result != DBNull.Value) ? Convert.ToInt32(result) : 0;
        lblServiceCount.Text = string.Format("历史维修 {0} 次", serviceCount);
    }

    /// <summary>
    /// 加载近期预约
    /// </summary>
    private void LoadUpcomingAppointments()
    {
        string query = @"SELECT TOP 5 a.AppointmentID, a.AppointmentDate, a.Status,
                        c.Make + ' ' + c.Model + ' (' + c.LicensePlate + ')' AS CarInfo,
                        rs.ShopName, s.ServiceName
                        FROM Appointments a
                        INNER JOIN Cars c ON a.CarID = c.CarID
                        INNER JOIN RepairShops rs ON a.ShopID = rs.ShopID
                        INNER JOIN RepairServices s ON a.ServiceID = s.ServiceID
                        WHERE c.OwnerID = @OwnerID AND a.AppointmentDate > GETDATE()
                        ORDER BY a.AppointmentDate";
        SqlParameter parameter = new SqlParameter("@OwnerID", ownerID);
        DataTable appointmentsTable = DatabaseHelper.ExecuteQuery(query, parameter);

        // 转换状态显示为中文
        if (appointmentsTable != null && appointmentsTable.Rows.Count > 0)
        {
            foreach (DataRow row in appointmentsTable.Rows)
            {
                string status = row["Status"].ToString();
                switch (status)
                {
                    case "Pending":
                        row["Status"] = "待确认";
                        break;
                    case "Confirmed":
                        row["Status"] = "已确认";
                        break;
                    case "Completed":
                        row["Status"] = "已完成";
                        break;
                    case "Cancelled":
                        row["Status"] = "已取消";
                        break;
                }
            }
        }

        gvUpcomingAppointments.DataSource = appointmentsTable;
        gvUpcomingAppointments.DataBind();
    }
} 