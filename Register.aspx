<%@ Page Title="注册" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="Register" Codebehind="Register.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style>
        .register-container {
            max-width: 800px;
            margin: 40px auto;
        }
        
        .register-card {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border: none;
        }
        
        .register-header {
            padding: 25px;
            text-align: center;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        }
        
        .register-title {
            margin: 0;
            font-size: 1.75rem;
            font-weight: 700;
            color: white;
        }
        
        .register-body {
            padding: 40px;
        }
        
        .register-icon {
            font-size: 4rem;
            color: white;
            margin-bottom: 15px;
        }
        
        .register-footer {
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            border-top: 1px solid #eee;
        }
        
        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .form-control, .custom-select {
            height: auto;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ddd;
            transition: all 0.3s;
        }
        
        .form-control:focus, .custom-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .btn-register {
            padding: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
            font-size: 1rem;
            margin-top: 10px;
            border-radius: 30px;
        }
        
        .user-type-selector {
            margin-bottom: 30px;
        }
        
        .user-type-btn {
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            position: relative;
        }
        
        .user-type-btn.active {
            border: 2px solid var(--primary-color);
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .user-type-btn:not(.active) {
            border: 2px solid transparent;
            opacity: 0.7;
        }
        
        .user-type-btn .icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .user-type-btn.shop .icon {
            color: var(--secondary-color);
        }
        
        .user-type-btn .title {
            font-weight: 600;
            font-size: 1.2rem;
        }
        
        .user-type-btn .description {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 10px;
        }
        
        .password-strength {
            height: 5px;
            border-radius: 3px;
            margin-top: 8px;
            transition: all 0.3s;
        }
        
        .password-strength-text {
            font-size: 0.85rem;
            margin-top: 5px;
        }
        
        .password-weak {
            background-color: #e74c3c;
            width: 30%;
        }
        
        .password-medium {
            background-color: #f39c12;
            width: 60%;
        }
        
        .password-strong {
            background-color: #2ecc71;
            width: 100%;
        }
        
        .form-check {
            padding-left: 1.8rem;
        }
        
        .field-icon {
            color: var(--primary-color);
        }
        
        .alert-info {
            background-color: rgba(52, 152, 219, 0.1);
            border-color: rgba(52, 152, 219, 0.2);
            color: var(--primary-dark);
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="register-container" data-aos="fade-up">
        <div class="card register-card">
            <div class="register-header">
                <i class="fas fa-user-plus register-icon"></i>
                <h3 class="register-title">创建新账户</h3>
                <p class="text-white-50 mb-0">加入我们的汽车维修服务平台</p>
            </div>
            
            <div class="register-body">
                <!-- 用户类型选择器 -->
                <div class="user-type-selector mb-4">
                    <div class="row">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div id="carOwnerBtn" class="user-type-btn active" onclick="selectUserType('CarOwner')">
                                <div class="icon"><i class="fas fa-car"></i></div>
                                <div class="title">车主</div>
                                <div class="description">我需要为我的爱车寻找维修服务</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div id="repairShopBtn" class="user-type-btn shop" onclick="selectUserType('RepairShop')">
                                <div class="icon"><i class="fas fa-tools"></i></div>
                                <div class="title">维修店</div>
                                <div class="description">我的维修店需要接单和管理服务</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <asp:HiddenField ID="hdnUserType" runat="server" Value="CarOwner" />
                
                <!-- 车主特有信息 -->
                <div id="carOwnerFields" class="mb-4">
                    <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-car-side mr-2"></i>车主信息</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="txtFullName"><i class="fas fa-user-circle field-icon mr-2"></i>姓名</label>
                                <asp:TextBox ID="txtFullName" runat="server" CssClass="form-control" placeholder="请输入您的真实姓名"></asp:TextBox>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="ddlGender"><i class="fas fa-venus-mars field-icon mr-2"></i>性别</label>
                                <asp:DropDownList ID="ddlGender" runat="server" CssClass="form-control">
                                    <asp:ListItem Text="请选择" Value=""></asp:ListItem>
                                    <asp:ListItem Text="男" Value="M"></asp:ListItem>
                                    <asp:ListItem Text="女" Value="F"></asp:ListItem>
                                    <asp:ListItem Text="其他" Value="O"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 维修店特有信息 -->
                <div id="repairShopFields" class="mb-4" style="display:none;">
                    <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-store mr-2"></i>维修店信息</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="txtShopName"><i class="fas fa-store-alt field-icon mr-2"></i>店铺名称</label>
                                <asp:TextBox ID="txtShopName" runat="server" CssClass="form-control" placeholder="请输入维修店名称"></asp:TextBox>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="txtBusinessLicense"><i class="fas fa-file-contract field-icon mr-2"></i>营业执照号</label>
                                <asp:TextBox ID="txtBusinessLicense" runat="server" CssClass="form-control" placeholder="请输入营业执照号码"></asp:TextBox>
                                <small class="form-text text-muted">用于验证店铺资质</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="txtShopAddress"><i class="fas fa-map-marker-alt field-icon mr-2"></i>店铺地址</label>
                                <asp:TextBox ID="txtShopAddress" runat="server" CssClass="form-control" placeholder="请输入详细地址"></asp:TextBox>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="ddlShopType"><i class="fas fa-cogs field-icon mr-2"></i>店铺类型</label>
                                <asp:DropDownList ID="ddlShopType" runat="server" CssClass="form-control">
                                    <asp:ListItem Text="请选择" Value=""></asp:ListItem>
                                    <asp:ListItem Text="综合维修" Value="General"></asp:ListItem>
                                    <asp:ListItem Text="4S店" Value="4S"></asp:ListItem>
                                    <asp:ListItem Text="快修店" Value="Express"></asp:ListItem>
                                    <asp:ListItem Text="专修店" Value="Specialty"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="txtUsername"><i class="fas fa-user field-icon mr-2"></i>用户名</label>
                            <asp:TextBox ID="txtUsername" runat="server" CssClass="form-control" placeholder="请输入用户名"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvUsername" runat="server" ControlToValidate="txtUsername"
                                ErrorMessage="用户名不能为空" Display="Dynamic" CssClass="text-danger"></asp:RequiredFieldValidator>
                        </div>
                        
                        <div class="form-group">
                            <label for="txtPassword"><i class="fas fa-lock field-icon mr-2"></i>密码</label>
                            <div class="input-group">
                                <asp:TextBox ID="txtPassword" runat="server" TextMode="Password" CssClass="form-control" placeholder="请输入密码" onkeyup="checkPasswordStrength()"></asp:TextBox>
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <asp:RequiredFieldValidator ID="rfvPassword" runat="server" ControlToValidate="txtPassword"
                                ErrorMessage="密码不能为空" Display="Dynamic" CssClass="text-danger"></asp:RequiredFieldValidator>
                                
                            <!-- 密码强度指示器 -->
                            <div class="password-strength" id="passwordStrength"></div>
                            <div class="password-strength-text" id="passwordStrengthText"></div>
                            
                            <div class="alert alert-info mt-2">
                                <small>
                                    <asp:Panel ID="passwordRequirements" runat="server">
                                        <i class="fas fa-info-circle mr-2"></i>密码要求：
                                        <ul class="mb-0">
                                            <li>最少<asp:Label ID="minLength" runat="server" Text="8"></asp:Label>个字符</li>
                                            <asp:Panel ID="requireUppercase" runat="server"><li>必须包含大写字母</li></asp:Panel>
                                            <asp:Panel ID="requireDigit" runat="server"><li>必须包含数字</li></asp:Panel>
                                            <asp:Panel ID="requireSpecialChar" runat="server"><li>必须包含特殊字符</li></asp:Panel>
                                        </ul>
                                    </asp:Panel>
                                </small>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="txtConfirmPassword"><i class="fas fa-lock field-icon mr-2"></i>确认密码</label>
                            <asp:TextBox ID="txtConfirmPassword" runat="server" TextMode="Password" CssClass="form-control" placeholder="请再次输入密码"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvConfirmPassword" runat="server" ControlToValidate="txtConfirmPassword"
                                ErrorMessage="确认密码不能为空" Display="Dynamic" CssClass="text-danger"></asp:RequiredFieldValidator>
                            <asp:CompareValidator ID="cvPassword" runat="server" ControlToValidate="txtConfirmPassword"
                                ControlToCompare="txtPassword" ErrorMessage="两次输入的密码不一致" Display="Dynamic"
                                CssClass="text-danger"></asp:CompareValidator>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="txtEmail"><i class="fas fa-envelope field-icon mr-2"></i>电子邮箱</label>
                            <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" placeholder="请输入电子邮箱"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvEmail" runat="server" ControlToValidate="txtEmail"
                                ErrorMessage="邮箱不能为空" Display="Dynamic" CssClass="text-danger"></asp:RequiredFieldValidator>
                            <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txtEmail"
                                ErrorMessage="请输入有效的邮箱地址" Display="Dynamic"
                                ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" CssClass="text-danger"></asp:RegularExpressionValidator>
                            <small class="form-text text-muted">我们将发送验证链接到此邮箱</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="txtPhoneNumber"><i class="fas fa-mobile-alt field-icon mr-2"></i>手机号码</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">+86</span>
                                </div>
                                <asp:TextBox ID="txtPhoneNumber" runat="server" CssClass="form-control" placeholder="请输入手机号码"></asp:TextBox>
                            </div>
                            <asp:RegularExpressionValidator ID="revPhoneNumber" runat="server" ControlToValidate="txtPhoneNumber"
                                ErrorMessage="请输入有效的手机号码" Display="Dynamic"
                                ValidationExpression="^1[3456789]\d{9}$" CssClass="text-danger"></asp:RegularExpressionValidator>
                            <small class="form-text text-muted">用于接收服务通知和账号安全验证</small>
                        </div>
                        
                        <div class="form-group mt-4">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="agreeTerms" required>
                                <label class="custom-control-label" for="agreeTerms">
                                    我已阅读并同意 <a href="#" data-toggle="modal" data-target="#termsModal">服务条款</a> 和 <a href="#" data-toggle="modal" data-target="#privacyModal">隐私政策</a>
                                </label>
                            </div>
                            <div class="custom-control custom-checkbox mt-2">
                                <input type="checkbox" class="custom-control-input" id="agreeNotification">
                                <label class="custom-control-label" for="agreeNotification">
                                    我同意接收平台的最新动态和优惠信息
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
                </div>
                
                <div class="form-group text-center">
                    <asp:Button ID="btnRegister" runat="server" Text="注 册" CssClass="btn btn-primary btn-lg btn-register px-5" OnClick="btnRegister_Click" />
                </div>
            </div>
            
            <div class="register-footer">
                <p class="mb-0">已有账号？ <a href="Login.aspx"><i class="fas fa-sign-in-alt mr-1"></i>立即登录</a></p>
            </div>
        </div>
    </div>
    
    <!-- 条款和隐私政策模态框 -->
    <div class="modal fade" id="termsModal" tabindex="-1" role="dialog" aria-labelledby="termsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="termsModalLabel">服务条款</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>注册和使用本平台，即表示您同意以下条款：</p>
                    <ol>
                        <li>用户应提供真实、准确的个人信息。</li>
                        <li>用户应自行保管账号和密码，对账号下的所有活动负责。</li>
                        <li>平台有权在用户违反相关规定时，暂停或终止其账号使用权限。</li>
                        <li>用户不得利用本平台进行任何违法活动或侵犯他人合法权益的行为。</li>
                        <li>平台保留随时更改服务条款的权利。</li>
                    </ol>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">我已阅读并理解</button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="modal fade" id="privacyModal" tabindex="-1" role="dialog" aria-labelledby="privacyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="privacyModalLabel">隐私政策</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>我们非常重视您的隐私保护，本隐私政策说明了：</p>
                    <ol>
                        <li>我们收集的信息类型及用途。</li>
                        <li>我们如何使用和保护这些信息。</li>
                        <li>您对个人信息的权利。</li>
                        <li>我们可能向第三方共享的信息。</li>
                        <li>我们使用的数据安全措施。</li>
                    </ol>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">我已阅读并理解</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function selectUserType(type) {
            // 更新隐藏字段值
            document.getElementById('<%= hdnUserType.ClientID %>').value = type;
            
            // 更新UI状态
            if (type === 'CarOwner') {
                document.getElementById('carOwnerBtn').classList.add('active');
                document.getElementById('repairShopBtn').classList.remove('active');
                document.getElementById('carOwnerFields').style.display = 'block';
                document.getElementById('repairShopFields').style.display = 'none';
            } else {
                document.getElementById('repairShopBtn').classList.add('active');
                document.getElementById('carOwnerBtn').classList.remove('active');
                document.getElementById('carOwnerFields').style.display = 'none';
                document.getElementById('repairShopFields').style.display = 'block';
            }
        }
        
        function checkPasswordStrength() {
            const password = document.getElementById('<%= txtPassword.ClientID %>').value;
            const strengthBar = document.getElementById('passwordStrength');
            const strengthText = document.getElementById('passwordStrengthText');
            
            // 移除之前的所有类
            strengthBar.classList.remove('password-weak', 'password-medium', 'password-strong');
            
            if (password.length === 0) {
                strengthBar.style.display = 'none';
                strengthText.textContent = '';
                return;
            }
            
            strengthBar.style.display = 'block';
            
            // 简单的密码强度检查
            let strength = 0;
            
            // 长度检查
            if (password.length >= 8) strength += 1;
            if (password.length >= 12) strength += 1;
            
            // 复杂性检查
            if (/[A-Z]/.test(password)) strength += 1;
            if (/[0-9]/.test(password)) strength += 1;
            if (/[^A-Za-z0-9]/.test(password)) strength += 1;
            
            // 显示强度结果
            if (strength <= 2) {
                strengthBar.classList.add('password-weak');
                strengthText.textContent = '密码强度：弱';
                strengthText.className = 'password-strength-text text-danger';
            } else if (strength <= 4) {
                strengthBar.classList.add('password-medium');
                strengthText.textContent = '密码强度：中';
                strengthText.className = 'password-strength-text text-warning';
            } else {
                strengthBar.classList.add('password-strong');
                strengthText.textContent = '密码强度：强';
                strengthText.className = 'password-strength-text text-success';
            }
        }
        
        // 显示/隐藏密码
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('<%= txtPassword.ClientID %>');
            const eyeIcon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        });
    </script>
</asp:Content> 