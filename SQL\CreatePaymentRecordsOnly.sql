USE CarRepairServiceDB;

-- 创建支付记录表，不使用IF EXISTS检查
CREATE TABLE PaymentRecords (
    PaymentID INT PRIMARY KEY IDENTITY(1,1),
    AppointmentID INT NOT NULL,
    ServiceRecordID INT NOT NULL,
    Amount DECIMAL(10, 2) NOT NULL,
    PaymentMethod NVARCHAR(50) NOT NULL, -- 'CreditCard', 'DebitCard', 'Cash', 'WeChat', 'Alipay'
    PaymentDate DATETIME DEFAULT GETDATE(),
    TransactionNumber NVARCHAR(100),
    Status NVARCHAR(20) NOT NULL DEFAULT 'Completed', -- 'Pending', 'Completed', 'Failed', 'Refunded'
    Notes NVARCHAR(500),
    FOREIGN KEY (AppointmentID) REFERENCES Appointments(AppointmentID),
    FOREIGN KEY (ServiceRecordID) REFERENCES ServiceRecords(RecordID)
);

-- 确认表已创建
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME = 'PaymentRecords'; 