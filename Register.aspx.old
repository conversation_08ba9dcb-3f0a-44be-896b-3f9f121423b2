<%@ Page Title="注册" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="Register" Codebehind="Register.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">用户注册</h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="ddlUserType">用户类型</label>
                        <asp:DropDownList ID="ddlUserType" runat="server" CssClass="form-control">
                            <asp:ListItem Value="CarOwner" Text="车主" Selected="True"></asp:ListItem>
                            <asp:ListItem Value="RepairShop" Text="维修店"></asp:ListItem>
                        </asp:DropDownList>
                    </div>

                    <div class="form-group">
                        <label for="txtUsername">用户名</label>
                        <asp:TextBox ID="txtUsername" runat="server" CssClass="form-control" placeholder="请输入用户名"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvUsername" runat="server" ControlToValidate="txtUsername"
                            ErrorMessage="用户名不能为空" Display="Dynamic" CssClass="text-danger"></asp:RequiredFieldValidator>
                    </div>

                    <div class="form-group">
                        <label for="txtPassword">密码</label>
                        <asp:TextBox ID="txtPassword" runat="server" TextMode="Password" CssClass="form-control" placeholder="请输入密码"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvPassword" runat="server" ControlToValidate="txtPassword"
                            ErrorMessage="密码不能为空" Display="Dynamic" CssClass="text-danger"></asp:RequiredFieldValidator>
                    </div>

                    <div class="form-group">
                        <label for="txtConfirmPassword">确认密码</label>
                        <asp:TextBox ID="txtConfirmPassword" runat="server" TextMode="Password" CssClass="form-control" placeholder="请再次输入密码"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvConfirmPassword" runat="server" ControlToValidate="txtConfirmPassword"
                            ErrorMessage="确认密码不能为空" Display="Dynamic" CssClass="text-danger"></asp:RequiredFieldValidator>
                        <asp:CompareValidator ID="cvPassword" runat="server" ControlToValidate="txtConfirmPassword"
                            ControlToCompare="txtPassword" ErrorMessage="两次输入的密码不一致" Display="Dynamic"
                            CssClass="text-danger"></asp:CompareValidator>
                    </div>

                    <div class="form-group">
                        <label for="txtEmail">电子邮箱</label>
                        <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" placeholder="请输入电子邮箱"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvEmail" runat="server" ControlToValidate="txtEmail"
                            ErrorMessage="邮箱不能为空" Display="Dynamic" CssClass="text-danger"></asp:RequiredFieldValidator>
                        <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txtEmail"
                            ErrorMessage="请输入有效的邮箱地址" Display="Dynamic"
                            ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" CssClass="text-danger"></asp:RegularExpressionValidator>
                    </div>

                    <div class="form-group">
                        <label for="txtPhoneNumber">手机号码</label>
                        <asp:TextBox ID="txtPhoneNumber" runat="server" CssClass="form-control" placeholder="请输入手机号码"></asp:TextBox>
                        <asp:RegularExpressionValidator ID="revPhoneNumber" runat="server" ControlToValidate="txtPhoneNumber"
                            ErrorMessage="请输入有效的手机号码" Display="Dynamic"
                            ValidationExpression="^1[3456789]\d{9}$" CssClass="text-danger"></asp:RegularExpressionValidator>
                    </div>

                    <div class="form-group">
                        <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
                    </div>

                    <div class="form-group">
                        <asp:Button ID="btnRegister" runat="server" Text="注册" CssClass="btn btn-primary btn-block" OnClick="btnRegister_Click" />
                    </div>

                    <div class="text-center">
                        <p>已有账号？ <a href="Login.aspx">立即登录</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content> 