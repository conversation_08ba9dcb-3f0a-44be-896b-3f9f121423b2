using System;
using System.Web;
using System.Web.Security;
using System.Web.UI;

public partial class MasterPage : System.Web.UI.MasterPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (HttpContext.Current.User.Identity.IsAuthenticated)
            {
                // 获取当前用户信息
                if (Session["UserID"] != null && Session["UserType"] != null)
                {
                    string userType = Session["UserType"].ToString();
                    
                    // 根据用户类型显示对应的导航菜单
                    switch (userType)
                    {
                        case "CarOwner":
                            phCarOwnerNav.Visible = true;
                            break;
                        case "RepairShop":
                            phRepairShopNav.Visible = true;
                            break;
                        case "Admin":
                            phAdminNav.Visible = true;
                            break;
                    }
                }
                else
                {
                    // 如果Session中没有用户信息，但用户已登录，则注销登录
                    FormsAuthentication.SignOut();
                    Response.Redirect("~/Login.aspx");
                }
            }
        }
    }
} 