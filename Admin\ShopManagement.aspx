<%@ Page Title="维修店管理" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="Admin_ShopManagement" Codebehind="ShopManagement.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-store"></i> 维修店管理</h2>
                <hr />
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-search"></i> 维修店搜索</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-10">
                                <div class="form-group">
                                    <label for="txtSearch">搜索：</label>
                                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="输入店铺名称、地址、联系人或邮箱搜索"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <asp:Button ID="btnSearch" runat="server" Text="搜索" CssClass="btn btn-primary btn-block" OnClick="btnSearch_Click" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list"></i> 维修店列表</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvShops" runat="server" AutoGenerateColumns="False" CssClass="table table-striped table-bordered"
                            OnRowCommand="gvShops_RowCommand" DataKeyNames="ShopID" AllowPaging="true" PageSize="10" OnPageIndexChanging="gvShops_PageIndexChanging">
                            <Columns>
                                <asp:BoundField DataField="ShopID" HeaderText="ID" />
                                <asp:BoundField DataField="ShopName" HeaderText="店铺名称" />
                                <asp:BoundField DataField="Address" HeaderText="地址" />
                                <asp:BoundField DataField="ContactPerson" HeaderText="联系人" />
                                <asp:TemplateField HeaderText="评分">
                                    <ItemTemplate>
                                        <%# Eval("Rating") != DBNull.Value ? Convert.ToDecimal(Eval("Rating")).ToString("0.0") : "暂无评分" %>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="状态">
                                    <ItemTemplate>
                                        <span class='<%# (bool)Eval("IsActive") ? "badge badge-success" : "badge badge-danger" %>'>
                                            <%# (bool)Eval("IsActive") ? "已激活" : "已禁用" %>
                                        </span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lbtnView" runat="server" CssClass="btn btn-sm btn-info" CommandName="ViewShop" CommandArgument='<%# Eval("ShopID") %>'>
                                            <i class="fas fa-eye"></i> 查看
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="lbtnStatus" runat="server" CssClass='<%# (bool)Eval("IsActive") ? "btn btn-sm btn-warning" : "btn btn-sm btn-success" %>'
                                            CommandName="ToggleStatus" CommandArgument='<%# Eval("ShopID") %>' OnClientClick="return confirm('确定要更改此维修店的状态吗？');">
                                            <i class='<%# (bool)Eval("IsActive") ? "fas fa-ban" : "fas fa-check" %>'></i>
                                            <%# (bool)Eval("IsActive") ? "禁用" : "激活" %>
                                        </asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle CssClass="pagination-ys" HorizontalAlign="Center" />
                            <EmptyDataTemplate>
                                <div class="alert alert-info">
                                    没有找到符合条件的维修店
                                </div>
                            </EmptyDataTemplate>
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>

        <asp:Panel ID="pnlShopDetails" runat="server" Visible="false" CssClass="mt-4">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-store"></i> 维修店详情</h5>
                                <asp:LinkButton ID="lbtnCloseDetails" runat="server" CssClass="btn btn-sm btn-light" OnClick="lbtnCloseDetails_Click">
                                    <i class="fas fa-times"></i> 关闭
                                </asp:LinkButton>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>店铺ID：</label>
                                        <asp:Label ID="lblShopID" runat="server" CssClass="form-control"></asp:Label>
                                    </div>
                                    <div class="form-group">
                                        <label>店铺名称：</label>
                                        <asp:Label ID="lblShopName" runat="server" CssClass="form-control"></asp:Label>
                                    </div>
                                    <div class="form-group">
                                        <label>联系人：</label>
                                        <asp:Label ID="lblContactPerson" runat="server" CssClass="form-control"></asp:Label>
                                    </div>
                                    <div class="form-group">
                                        <label>电话：</label>
                                        <asp:Label ID="lblPhone" runat="server" CssClass="form-control"></asp:Label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>地址：</label>
                                        <asp:Label ID="lblAddress" runat="server" CssClass="form-control"></asp:Label>
                                    </div>
                                    <div class="form-group">
                                        <label>用户名：</label>
                                        <asp:Label ID="lblUsername" runat="server" CssClass="form-control"></asp:Label>
                                    </div>
                                    <div class="form-group">
                                        <label>邮箱：</label>
                                        <asp:Label ID="lblEmail" runat="server" CssClass="form-control"></asp:Label>
                                    </div>
                                    <div class="form-group">
                                        <label>状态：</label>
                                        <asp:Label ID="lblStatus" runat="server" CssClass="form-control"></asp:Label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>描述：</label>
                                        <asp:Label ID="lblDescription" runat="server" CssClass="form-control" Height="100px"></asp:Label>
                                    </div>
                                </div>
                            </div>

                            <hr />

                            <h5><i class="fas fa-tools"></i> 服务列表</h5>
                            <asp:GridView ID="gvServices" runat="server" AutoGenerateColumns="False" CssClass="table table-sm table-striped table-bordered">
                                <Columns>
                                    <asp:BoundField DataField="ServiceID" HeaderText="ID" />
                                    <asp:BoundField DataField="ServiceName" HeaderText="服务名称" />
                                    <asp:BoundField DataField="CategoryName" HeaderText="类别" />
                                    <asp:BoundField DataField="Price" HeaderText="价格" DataFormatString="{0:C}" />
                                    <asp:TemplateField HeaderText="状态">
                                        <ItemTemplate>
                                            <span class='<%# (bool)Eval("IsActive") ? "badge badge-success" : "badge badge-danger" %>'>
                                                <%# (bool)Eval("IsActive") ? "已激活" : "已禁用" %>
                                            </span>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                                <EmptyDataTemplate>
                                    <div class="alert alert-info">
                                        该维修店暂无服务项目
                                    </div>
                                </EmptyDataTemplate>
                            </asp:GridView>

                            <hr />

                            <h5><i class="fas fa-star"></i> 评价列表</h5>
                            <asp:GridView ID="gvReviews" runat="server" AutoGenerateColumns="False" CssClass="table table-sm table-striped table-bordered">
                                <Columns>
                                    <asp:BoundField DataField="ReviewID" HeaderText="ID" />
                                    <asp:BoundField DataField="Username" HeaderText="用户" />
                                    <asp:TemplateField HeaderText="评分">
                                        <ItemTemplate>
                                            <div class="text-warning">
                                                <%# GetStarRating(Convert.ToInt32(Eval("Rating"))) %>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    <asp:BoundField DataField="Comment" HeaderText="评论" />
                                    <asp:BoundField DataField="ReviewDate" HeaderText="日期" DataFormatString="{0:yyyy-MM-dd}" />
                                </Columns>
                                <EmptyDataTemplate>
                                    <div class="alert alert-info">
                                        该维修店暂无评价
                                    </div>
                                </EmptyDataTemplate>
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <div class="row mt-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
            </div>
        </div>
    </div>
</asp:Content> 