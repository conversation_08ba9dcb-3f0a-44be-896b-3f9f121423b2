<%@ Page Title="系统设置" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" Inherits="Admin_SystemSettings" Codebehind="SystemSettings.aspx.cs" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-cogs"></i> 系统设置</h2>
                <hr />
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-database"></i> 数据库备份</h5>
                    </div>
                    <div class="card-body">
                        <p>手动备份数据库，以防数据丢失。</p>
                        <asp:Button ID="btnBackupDatabase" runat="server" Text="备份数据库" CssClass="btn btn-primary" OnClick="btnBackupDatabase_Click" />
                        <asp:Label ID="lblBackupMessage" runat="server" CssClass="ml-3"></asp:Label>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-envelope"></i> 邮件设置</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="txtSmtpServer">SMTP服务器：</label>
                            <asp:TextBox ID="txtSmtpServer" runat="server" CssClass="form-control"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvSmtpServer" runat="server" ControlToValidate="txtSmtpServer"
                                ErrorMessage="SMTP服务器不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="EmailSettings"></asp:RequiredFieldValidator>
                        </div>
                        <div class="form-group">
                            <label for="txtSmtpPort">SMTP端口：</label>
                            <asp:TextBox ID="txtSmtpPort" runat="server" CssClass="form-control" TextMode="Number"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvSmtpPort" runat="server" ControlToValidate="txtSmtpPort"
                                ErrorMessage="SMTP端口不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="EmailSettings"></asp:RequiredFieldValidator>
                            <asp:RangeValidator ID="rvSmtpPort" runat="server" ControlToValidate="txtSmtpPort"
                                ErrorMessage="SMTP端口必须是1-65535之间的数字" Display="Dynamic" CssClass="text-danger"
                                MinimumValue="1" MaximumValue="65535" Type="Integer" ValidationGroup="EmailSettings"></asp:RangeValidator>
                        </div>
                        <div class="form-group">
                            <label for="txtEmailAddress">发件人邮箱：</label>
                            <asp:TextBox ID="txtEmailAddress" runat="server" CssClass="form-control" TextMode="Email"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvEmailAddress" runat="server" ControlToValidate="txtEmailAddress"
                                ErrorMessage="发件人邮箱不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="EmailSettings"></asp:RequiredFieldValidator>
                            <asp:RegularExpressionValidator ID="revEmailAddress" runat="server" ControlToValidate="txtEmailAddress"
                                ErrorMessage="请输入有效的邮箱地址" Display="Dynamic" CssClass="text-danger"
                                ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" ValidationGroup="EmailSettings"></asp:RegularExpressionValidator>
                        </div>
                        <div class="form-group">
                            <label for="txtEmailPassword">邮箱密码：</label>
                            <asp:TextBox ID="txtEmailPassword" runat="server" CssClass="form-control" TextMode="Password"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvEmailPassword" runat="server" ControlToValidate="txtEmailPassword"
                                ErrorMessage="邮箱密码不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="EmailSettings"></asp:RequiredFieldValidator>
                        </div>
                        <div class="form-group">
                            <div class="form-check">
                                <asp:CheckBox ID="chkEnableSsl" runat="server" />
                                <label class="form-check-label" for="chkEnableSsl">
                                    启用SSL
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <asp:Button ID="btnSaveEmailSettings" runat="server" Text="保存邮件设置" CssClass="btn btn-primary" ValidationGroup="EmailSettings" OnClick="btnSaveEmailSettings_Click" />
                            <asp:Button ID="btnTestEmail" runat="server" Text="测试邮件设置" CssClass="btn btn-info ml-2" OnClick="btnTestEmail_Click" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-shield-alt"></i> 安全设置</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="txtMinPasswordLength">最小密码长度：</label>
                            <asp:TextBox ID="txtMinPasswordLength" runat="server" CssClass="form-control" TextMode="Number"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvMinPasswordLength" runat="server" ControlToValidate="txtMinPasswordLength"
                                ErrorMessage="最小密码长度不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="SecuritySettings"></asp:RequiredFieldValidator>
                            <asp:RangeValidator ID="rvMinPasswordLength" runat="server" ControlToValidate="txtMinPasswordLength"
                                ErrorMessage="最小密码长度必须是6-20之间的数字" Display="Dynamic" CssClass="text-danger"
                                MinimumValue="6" MaximumValue="20" Type="Integer" ValidationGroup="SecuritySettings"></asp:RangeValidator>
                        </div>
                        <div class="form-group">
                            <div class="form-check">
                                <asp:CheckBox ID="chkRequireUppercase" runat="server" />
                                <label class="form-check-label" for="chkRequireUppercase">
                                    密码必须包含大写字母
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-check">
                                <asp:CheckBox ID="chkRequireDigit" runat="server" />
                                <label class="form-check-label" for="chkRequireDigit">
                                    密码必须包含数字
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-check">
                                <asp:CheckBox ID="chkRequireSpecialChar" runat="server" />
                                <label class="form-check-label" for="chkRequireSpecialChar">
                                    密码必须包含特殊字符
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="txtMaxLoginAttempts">最大登录尝试次数：</label>
                            <asp:TextBox ID="txtMaxLoginAttempts" runat="server" CssClass="form-control" TextMode="Number"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvMaxLoginAttempts" runat="server" ControlToValidate="txtMaxLoginAttempts"
                                ErrorMessage="最大登录尝试次数不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="SecuritySettings"></asp:RequiredFieldValidator>
                            <asp:RangeValidator ID="rvMaxLoginAttempts" runat="server" ControlToValidate="txtMaxLoginAttempts"
                                ErrorMessage="最大登录尝试次数必须是3-10之间的数字" Display="Dynamic" CssClass="text-danger"
                                MinimumValue="3" MaximumValue="10" Type="Integer" ValidationGroup="SecuritySettings"></asp:RangeValidator>
                        </div>
                        <div class="form-group">
                            <label for="txtLockoutDuration">账户锁定时间（分钟）：</label>
                            <asp:TextBox ID="txtLockoutDuration" runat="server" CssClass="form-control" TextMode="Number"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvLockoutDuration" runat="server" ControlToValidate="txtLockoutDuration"
                                ErrorMessage="账户锁定时间不能为空" Display="Dynamic" CssClass="text-danger" ValidationGroup="SecuritySettings"></asp:RequiredFieldValidator>
                            <asp:RangeValidator ID="rvLockoutDuration" runat="server" ControlToValidate="txtLockoutDuration"
                                ErrorMessage="账户锁定时间必须是5-60之间的数字" Display="Dynamic" CssClass="text-danger"
                                MinimumValue="5" MaximumValue="60" Type="Integer" ValidationGroup="SecuritySettings"></asp:RangeValidator>
                        </div>
                        <div class="form-group">
                            <asp:Button ID="btnSaveSecuritySettings" runat="server" Text="保存安全设置" CssClass="btn btn-primary" ValidationGroup="SecuritySettings" OnClick="btnSaveSecuritySettings_Click" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-broom"></i> 系统维护</h5>
                    </div>
                    <div class="card-body">
                        <p>清理系统中的临时文件和日志，优化数据库。</p>
                        <asp:Button ID="btnCleanupSystem" runat="server" Text="清理系统" CssClass="btn btn-warning" OnClick="btnCleanupSystem_Click" OnClientClick="return confirm('确定要清理系统吗？此操作将删除临时文件和日志，但不会影响用户数据。');" />
                        <asp:Button ID="btnOptimizeDatabase" runat="server" Text="优化数据库" CssClass="btn btn-info ml-2" OnClick="btnOptimizeDatabase_Click" OnClientClick="return confirm('确定要优化数据库吗？此操作可能需要一些时间。');" />
                        <asp:Label ID="lblMaintenanceMessage" runat="server" CssClass="ml-3"></asp:Label>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col">
                <asp:Label ID="lblMessage" runat="server" CssClass="text-danger"></asp:Label>
            </div>
        </div>
    </div>
</asp:Content> 